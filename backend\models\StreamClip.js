const mongoose = require('mongoose');

const StreamClipSchema = new mongoose.Schema({
  stream: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'LiveStream',
    required: true,
  },
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  title: {
    type: String,
    required: [true, 'Please provide a title for the clip'],
    trim: true,
    maxlength: [100, 'Title cannot be more than 100 characters'],
  },
  description: {
    type: String,
    trim: true,
    maxlength: [500, 'Description cannot be more than 500 characters'],
  },
  startTime: {
    type: Number, // Seconds from the start of the stream
    required: true,
  },
  endTime: {
    type: Number, // Seconds from the start of the stream
    required: true,
  },
  duration: {
    type: Number, // Duration in seconds
    required: true,
  },
  clipUrl: {
    type: String,
    required: true,
  },
  thumbnailUrl: {
    type: String,
    required: true,
  },
  publicId: {
    type: String,
  },
  isAIGenerated: {
    type: Boolean,
    default: false,
  },
  aiMetadata: {
    confidence: {
      type: Number,
      min: 0,
      max: 1,
    },
    tags: [String],
    detectedObjects: [String],
    detectedEmotions: [String],
    highlightReason: {
      type: String,
      enum: ['engagement_spike', 'emotional_moment', 'action_detected', 'conversation_highlight', 'custom'],
    },
  },
  isRemixed: {
    type: Boolean,
    default: false,
  },
  remixMetadata: {
    effects: [String],
    filters: [String],
    music: {
      title: String,
      artist: String,
      url: String,
    },
    transitions: [String],
    textOverlays: [{
      text: String,
      position: {
        x: Number,
        y: Number,
      },
      style: String,
    }],
  },
  viewCount: {
    type: Number,
    default: 0,
  },
  likesCount: {
    type: Number,
    default: 0,
  },
  sharesCount: {
    type: Number,
    default: 0,
  },
  status: {
    type: String,
    enum: ['processing', 'ready', 'failed', 'deleted'],
    default: 'processing',
  },
  isPublic: {
    type: Boolean,
    default: true,
  },
  isNFT: {
    type: Boolean,
    default: false,
  },
  nftMetadata: {
    tokenId: String,
    contractAddress: String,
    blockchain: String,
    mintedAt: Date,
    owner: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
    },
  },
}, { timestamps: true });

// Create indexes for efficient queries
StreamClipSchema.index({ stream: 1, createdAt: -1 });
StreamClipSchema.index({ user: 1, createdAt: -1 });
StreamClipSchema.index({ isAIGenerated: 1 });
StreamClipSchema.index({ isRemixed: 1 });
StreamClipSchema.index({ isNFT: 1 });

module.exports = mongoose.model('StreamClip', StreamClipSchema);
