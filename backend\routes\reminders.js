const express = require('express');
const router = express.Router();
const { protect } = require('../middleware/auth');
const {
  getUserReminders,
  getReminder,
  createReminder,
  updateReminder,
  deleteReminder
} = require('../controllers/reminderController');

// All routes are protected
router.use(protect);

// User reminder routes
router.route('/')
  .get(getUserReminders)
  .post(createReminder);

router.route('/:id')
  .get(getReminder)
  .put(updateReminder)
  .delete(deleteReminder);

module.exports = router;
