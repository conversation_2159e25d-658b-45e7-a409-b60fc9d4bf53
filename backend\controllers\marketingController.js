const Campaign = require('../models/Campaign');
const Banner = require('../models/Banner');
const Analytics = require('../models/Analytics');
const { LoyaltyProgram, UserLoyalty } = require('../models/LoyaltyProgram');
const Product = require('../models/Product');
const User = require('../models/User');

// @desc    Get all campaigns
// @route   GET /api/marketing/campaigns
// @access  Private
const getCampaigns = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      type,
      status,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    let query = { creator: req.user.id };

    if (type) query.type = type;
    if (status) query.status = status;

    const sort = {};
    sort[sortBy] = sortOrder === 'desc' ? -1 : 1;

    const campaigns = await Campaign.find(query)
      .sort(sort)
      .limit(limit * 1)
      .skip((page - 1) * limit)
      .populate('discountConfig.applicableProducts', 'name price images')
      .lean();

    const total = await Campaign.countDocuments(query);

    // Add performance metrics
    const campaignsWithMetrics = campaigns.map(campaign => ({
      ...campaign,
      performance: {
        ...campaign.analytics,
        isActive: campaign.status === 'active' &&
                 new Date(campaign.startDate) <= new Date() &&
                 new Date(campaign.endDate) >= new Date(),
        daysRemaining: Math.ceil((new Date(campaign.endDate) - new Date()) / (1000 * 60 * 60 * 24))
      }
    }));

    res.status(200).json({
      success: true,
      data: campaignsWithMetrics,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error getting campaigns:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Create new campaign
// @route   POST /api/marketing/campaigns
// @access  Private
const createCampaign = async (req, res) => {
  try {
    const campaignData = {
      ...req.body,
      creator: req.user.id
    };

    // Generate coupon code if it's a coupon campaign
    if (campaignData.type === 'coupon' && !campaignData.couponConfig?.code) {
      campaignData.couponConfig = {
        ...campaignData.couponConfig,
        code: generateCouponCode()
      };
    }

    const campaign = new Campaign(campaignData);
    await campaign.save();

    // If it's a product boost campaign, update product rankings
    if (campaign.type === 'product_boost' && campaign.productBoostConfig?.boostedProducts) {
      await updateProductBoosts(campaign.productBoostConfig.boostedProducts);
    }

    res.status(201).json({
      success: true,
      data: campaign
    });
  } catch (error) {
    console.error('Error creating campaign:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Update campaign
// @route   PUT /api/marketing/campaigns/:id
// @access  Private
const updateCampaign = async (req, res) => {
  try {
    let campaign = await Campaign.findById(req.params.id);

    if (!campaign) {
      return res.status(404).json({
        success: false,
        message: 'Campaign not found'
      });
    }

    // Check if user owns the campaign
    if (campaign.creator.toString() !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: 'Not authorized to update this campaign'
      });
    }

    campaign = await Campaign.findByIdAndUpdate(
      req.params.id,
      req.body,
      {
        new: true,
        runValidators: true
      }
    );

    res.status(200).json({
      success: true,
      data: campaign
    });
  } catch (error) {
    console.error('Error updating campaign:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Delete campaign
// @route   DELETE /api/marketing/campaigns/:id
// @access  Private
const deleteCampaign = async (req, res) => {
  try {
    const campaign = await Campaign.findById(req.params.id);

    if (!campaign) {
      return res.status(404).json({
        success: false,
        message: 'Campaign not found'
      });
    }

    // Check if user owns the campaign
    if (campaign.creator.toString() !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: 'Not authorized to delete this campaign'
      });
    }

    await campaign.deleteOne();

    res.status(200).json({
      success: true,
      data: {}
    });
  } catch (error) {
    console.error('Error deleting campaign:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Get campaign analytics
// @route   GET /api/marketing/campaigns/:id/analytics
// @access  Private
const getCampaignAnalytics = async (req, res) => {
  try {
    const campaign = await Campaign.findById(req.params.id);

    if (!campaign) {
      return res.status(404).json({
        success: false,
        message: 'Campaign not found'
      });
    }

    // Check if user owns the campaign
    if (campaign.creator.toString() !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: 'Not authorized to view this campaign'
      });
    }

    // Get detailed analytics from Analytics collection
    const analyticsData = await Analytics.find({
      campaign: req.params.id,
      type: 'campaign_performance'
    }).sort({ date: -1 }).limit(30);

    const performance = campaign.getPerformance();

    res.status(200).json({
      success: true,
      data: {
        campaign: {
          id: campaign._id,
          name: campaign.name,
          type: campaign.type,
          status: campaign.status
        },
        performance,
        timeline: analyticsData,
        summary: {
          totalImpressions: campaign.analytics.impressions,
          totalClicks: campaign.analytics.clicks,
          totalConversions: campaign.analytics.conversions,
          totalRevenue: campaign.analytics.revenue,
          averageCTR: campaign.analytics.ctr,
          averageConversionRate: campaign.analytics.conversionRate,
          roas: campaign.analytics.roas
        }
      }
    });
  } catch (error) {
    console.error('Error getting campaign analytics:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Apply coupon code
// @route   POST /api/marketing/coupons/apply
// @access  Private
const applyCoupon = async (req, res) => {
  try {
    const { code, cartTotal } = req.body;

    const campaign = await Campaign.findOne({
      'couponConfig.code': code,
      status: 'active',
      startDate: { $lte: new Date() },
      endDate: { $gte: new Date() }
    });

    if (!campaign) {
      return res.status(404).json({
        success: false,
        message: 'Invalid or expired coupon code'
      });
    }

    // Check usage limits
    if (campaign.couponConfig.usageLimit &&
        campaign.couponConfig.usageCount >= campaign.couponConfig.usageLimit) {
      return res.status(400).json({
        success: false,
        message: 'Coupon usage limit exceeded'
      });
    }

    // Check minimum order value
    if (campaign.discountConfig.minOrderValue &&
        cartTotal < campaign.discountConfig.minOrderValue) {
      return res.status(400).json({
        success: false,
        message: `Minimum order value of $${campaign.discountConfig.minOrderValue} required`
      });
    }

    // Calculate discount
    let discountAmount = 0;
    if (campaign.discountConfig.type === 'percentage') {
      discountAmount = (cartTotal * campaign.discountConfig.value) / 100;
      if (campaign.discountConfig.maxDiscount) {
        discountAmount = Math.min(discountAmount, campaign.discountConfig.maxDiscount);
      }
    } else if (campaign.discountConfig.type === 'fixed_amount') {
      discountAmount = campaign.discountConfig.value;
    }

    res.status(200).json({
      success: true,
      data: {
        code,
        discountAmount,
        discountType: campaign.discountConfig.type,
        campaignName: campaign.name,
        description: campaign.description
      }
    });
  } catch (error) {
    console.error('Error applying coupon:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Get seasonal templates
// @route   GET /api/marketing/templates
// @access  Private
const getSeasonalTemplates = async (req, res) => {
  try {
    const templates = [
      {
        id: 'black_friday',
        name: 'Black Friday',
        description: 'Black Friday mega sale template',
        category: 'seasonal',
        colors: {
          primary: '#000000',
          secondary: '#ff6b6b',
          accent: '#ffd700'
        },
        defaultContent: {
          headline: 'BLACK FRIDAY MEGA SALE',
          subheadline: 'Up to 70% OFF Everything',
          buttonText: 'Shop Now'
        }
      },
      {
        id: 'christmas',
        name: 'Christmas',
        description: 'Christmas holiday template',
        category: 'seasonal',
        colors: {
          primary: '#c41e3a',
          secondary: '#2e8b57',
          accent: '#ffd700'
        },
        defaultContent: {
          headline: 'Christmas Special Offers',
          subheadline: 'Perfect gifts for everyone',
          buttonText: 'Find Gifts'
        }
      },
      {
        id: 'eid',
        name: 'Eid Celebration',
        description: 'Eid festival template',
        category: 'seasonal',
        colors: {
          primary: '#4a90e2',
          secondary: '#f5a623',
          accent: '#7ed321'
        },
        defaultContent: {
          headline: 'Eid Mubarak Sale',
          subheadline: 'Celebrate with amazing deals',
          buttonText: 'Explore Offers'
        }
      },
      {
        id: 'new_year',
        name: 'New Year',
        description: 'New Year celebration template',
        category: 'seasonal',
        colors: {
          primary: '#9013fe',
          secondary: '#ff6b6b',
          accent: '#ffd700'
        },
        defaultContent: {
          headline: 'New Year, New Deals',
          subheadline: 'Start fresh with great savings',
          buttonText: 'Shop Sale'
        }
      },
      {
        id: 'summer_sale',
        name: 'Summer Sale',
        description: 'Summer season template',
        category: 'seasonal',
        colors: {
          primary: '#ff9500',
          secondary: '#4a90e2',
          accent: '#ffeb3b'
        },
        defaultContent: {
          headline: 'Summer Sale is Here',
          subheadline: 'Hot deals for hot days',
          buttonText: 'Cool Deals'
        }
      }
    ];

    res.status(200).json({
      success: true,
      data: templates
    });
  } catch (error) {
    console.error('Error getting templates:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// Helper function to generate coupon code
function generateCouponCode() {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let result = '';
  for (let i = 0; i < 8; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

// Helper function to update product boosts
async function updateProductBoosts(boostedProducts) {
  for (const boost of boostedProducts) {
    await Product.findByIdAndUpdate(
      boost.product,
      {
        $inc: { boostScore: boost.position },
        $set: { isBoosted: true }
      }
    );
  }
}

// @desc    Get all banners
// @route   GET /api/marketing/banners
// @access  Private
const getBanners = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      type,
      status,
      position
    } = req.query;

    let query = { creator: req.user.id };

    if (type) query.type = type;
    if (status) query.status = status;
    if (position) query.position = position;

    const banners = await Banner.find(query)
      .sort({ priority: -1, createdAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit)
      .populate('campaign', 'name type')
      .lean();

    const total = await Banner.countDocuments(query);

    res.status(200).json({
      success: true,
      data: banners,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error getting banners:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Create new banner
// @route   POST /api/marketing/banners
// @access  Private
const createBanner = async (req, res) => {
  try {
    const bannerData = {
      ...req.body,
      creator: req.user.id
    };

    const banner = new Banner(bannerData);
    await banner.save();

    res.status(201).json({
      success: true,
      data: banner
    });
  } catch (error) {
    console.error('Error creating banner:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Update banner
// @route   PUT /api/marketing/banners/:id
// @access  Private
const updateBanner = async (req, res) => {
  try {
    let banner = await Banner.findById(req.params.id);

    if (!banner) {
      return res.status(404).json({
        success: false,
        message: 'Banner not found'
      });
    }

    // Check if user owns the banner
    if (banner.creator.toString() !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: 'Not authorized to update this banner'
      });
    }

    banner = await Banner.findByIdAndUpdate(
      req.params.id,
      req.body,
      {
        new: true,
        runValidators: true
      }
    );

    res.status(200).json({
      success: true,
      data: banner
    });
  } catch (error) {
    console.error('Error updating banner:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Delete banner
// @route   DELETE /api/marketing/banners/:id
// @access  Private
const deleteBanner = async (req, res) => {
  try {
    const banner = await Banner.findById(req.params.id);

    if (!banner) {
      return res.status(404).json({
        success: false,
        message: 'Banner not found'
      });
    }

    // Check if user owns the banner
    if (banner.creator.toString() !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: 'Not authorized to delete this banner'
      });
    }

    await banner.deleteOne();

    res.status(200).json({
      success: true,
      data: {}
    });
  } catch (error) {
    console.error('Error deleting banner:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Get active banners for display
// @route   GET /api/marketing/banners/active
// @access  Public
const getActiveBanners = async (req, res) => {
  try {
    const { position, page, device } = req.query;

    const context = {
      position,
      page,
      device,
      currentUrl: req.get('Referer')
    };

    const banners = await Banner.getActiveBanners(context);

    res.status(200).json({
      success: true,
      data: banners
    });
  } catch (error) {
    console.error('Error getting active banners:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Record banner impression
// @route   POST /api/marketing/banners/:id/impression
// @access  Public
const recordBannerImpression = async (req, res) => {
  try {
    const banner = await Banner.findById(req.params.id);

    if (!banner) {
      return res.status(404).json({
        success: false,
        message: 'Banner not found'
      });
    }

    await banner.recordImpression();

    res.status(200).json({
      success: true,
      message: 'Impression recorded'
    });
  } catch (error) {
    console.error('Error recording impression:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Record banner click
// @route   POST /api/marketing/banners/:id/click
// @access  Public
const recordBannerClick = async (req, res) => {
  try {
    const banner = await Banner.findById(req.params.id);

    if (!banner) {
      return res.status(404).json({
        success: false,
        message: 'Banner not found'
      });
    }

    await banner.recordClick();

    res.status(200).json({
      success: true,
      message: 'Click recorded'
    });
  } catch (error) {
    console.error('Error recording click:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Send email/SMS campaign
// @route   POST /api/marketing/send-campaign
// @access  Private (Admin only)
const sendCampaign = async (req, res) => {
  try {
    // Check if user is admin
    if (req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Access denied. Admin privileges required.'
      });
    }

    const { type, subject, message, targetAudience, scheduleType, scheduledDate, scheduledTime } = req.body;

    // Validate required fields
    if (!type || !message) {
      return res.status(400).json({
        success: false,
        message: 'Campaign type and message are required'
      });
    }

    if (type === 'email' && !subject) {
      return res.status(400).json({
        success: false,
        message: 'Subject is required for email campaigns'
      });
    }

    // Create campaign record
    const campaignData = {
      name: `${type.toUpperCase()} Campaign - ${new Date().toLocaleDateString()}`,
      type: 'email_sms',
      status: scheduleType === 'now' ? 'active' : 'scheduled',
      creator: req.user.id,
      emailSmsConfig: {
        type,
        subject: type === 'email' ? subject : undefined,
        message,
        targetAudience,
        scheduleType,
        scheduledDate: scheduleType === 'scheduled' ? scheduledDate : undefined,
        scheduledTime: scheduleType === 'scheduled' ? scheduledTime : undefined
      },
      startDate: scheduleType === 'now' ? new Date() : new Date(`${scheduledDate}T${scheduledTime}`),
      endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days from now
    };

    const campaign = new Campaign(campaignData);
    await campaign.save();

    // Simulate sending (in production, integrate with email/SMS service)
    let recipientCount = 0;
    switch (targetAudience) {
      case 'all':
        recipientCount = 12450;
        break;
      case 'customers':
        recipientCount = 8320;
        break;
      case 'subscribers':
        recipientCount = 5670;
        break;
      case 'new_users':
        recipientCount = 2180;
        break;
      default:
        recipientCount = 0;
    }

    // Update campaign analytics
    campaign.analytics = {
      impressions: recipientCount,
      clicks: Math.floor(recipientCount * (type === 'email' ? 0.032 : 0.085)), // Estimated CTR
      conversions: Math.floor(recipientCount * (type === 'email' ? 0.008 : 0.025)), // Estimated conversion
      revenue: Math.floor(recipientCount * (type === 'email' ? 2.5 : 8.5)) // Estimated revenue per recipient
    };

    await campaign.save();

    res.status(200).json({
      success: true,
      data: {
        campaign,
        recipientCount,
        estimatedReach: recipientCount,
        message: `${type.toUpperCase()} campaign ${scheduleType === 'now' ? 'sent' : 'scheduled'} successfully`
      }
    });

  } catch (error) {
    console.error('Error sending campaign:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

module.exports = {
  getCampaigns,
  createCampaign,
  updateCampaign,
  deleteCampaign,
  getCampaignAnalytics,
  applyCoupon,
  getSeasonalTemplates,
  getBanners,
  createBanner,
  updateBanner,
  deleteBanner,
  getActiveBanners,
  recordBannerImpression,
  recordBannerClick,
  sendCampaign
};
