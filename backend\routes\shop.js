const express = require('express');
const router = express.Router();
const { protect, optionalAuth } = require('../middleware/auth');
const {
  getProducts,
  getProduct,
  createProduct,
  updateProduct,
  deleteProduct,
  getMyProducts,
  searchProducts,
  getProductsByCategory,
  getTrendingProducts,
  getRecommendedProducts,
  getProductSuggestions,
  compareProducts,
  getProductReviews,
  createProductReview,
  incrementProductViews,
  getRelatedProducts,
  getProductDeals,
  getFlashDeals,
  getCart,
  addToCart,
  updateCartItem,
  removeFromCart,
  clearCart,
  getWishlist,
  addToWishlist,
  removeFromWishlist,
  getRecentlyViewed,
  addToRecentlyViewed,
  getCategories,
  getProductFilters,
  bulkAddToCart,
  validateCart,
  getCartSummary,
  applyPromoCode,
  removePromoCode,
  getSavedAddresses,
  addSavedAddress,
  updateSavedAddress,
  deleteSavedAddress,
  getSavedPaymentMethods,
  addSavedPaymentMethod,
  deleteSavedPaymentMethod,
  getDeliveryOptions,
  calculateShipping,
  initiateCheckout,
  processPayment,
  getOrderHistory,
  getOrder,
  trackOrder
} = require('../controllers/shopController');

// Public routes
router.get('/products', optionalAuth, getProducts);
router.get('/products/search', optionalAuth, searchProducts);
router.get('/products/trending', optionalAuth, getTrendingProducts);
router.get('/products/suggestions', optionalAuth, getProductSuggestions);
router.get('/products/featured', optionalAuth, getTrendingProducts); // Use trending as featured
router.get('/products/deals', optionalAuth, getProductDeals);
router.get('/products/flash-deals', optionalAuth, getFlashDeals);
router.get('/products/categories', getCategories);
router.get('/products/filters', getProductFilters);
router.get('/products/category/:category', optionalAuth, getProductsByCategory);
router.get('/products/:id', optionalAuth, getProduct);
router.get('/products/:id/related', optionalAuth, getRelatedProducts);
router.get('/products/:id/reviews', getProductReviews);
router.post('/products/:id/views', optionalAuth, incrementProductViews);
router.post('/products/compare', optionalAuth, compareProducts);

// Protected routes
router.use(protect);

// Product management (for vendors)
router.get('/products/my-products', getMyProducts);
router.post('/products', createProduct);
router.put('/products/:id', updateProduct);
router.delete('/products/:id', deleteProduct);
router.post('/products/:id/reviews', createProductReview);

// Recommendations
router.get('/recommendations', getRecommendedProducts);

// Cart management
router.get('/cart', getCart);
router.post('/cart', addToCart);
router.post('/cart/bulk', bulkAddToCart);
router.put('/cart/:itemId', updateCartItem);
router.delete('/cart/:itemId', removeFromCart);
router.delete('/cart', clearCart);
router.get('/cart/summary', getCartSummary);
router.post('/cart/validate', validateCart);

// Promo codes
router.post('/cart/promo', applyPromoCode);
router.post('/cart/apply-coupon', applyPromoCode); // Alias for applying coupons
router.delete('/cart/promo', removePromoCode);

// Wishlist management
router.get('/wishlist', getWishlist);
router.post('/wishlist', addToWishlist);
router.delete('/wishlist/:productId', removeFromWishlist);
router.delete('/wishlist', clearCart); // Reuse clearCart logic for clearing wishlist

// Recently viewed
router.get('/recently-viewed', getRecentlyViewed);
router.post('/recently-viewed', addToRecentlyViewed);

// User preferences
router.get('/addresses', getSavedAddresses);
router.post('/addresses', addSavedAddress);
router.put('/addresses/:id', updateSavedAddress);
router.delete('/addresses/:id', deleteSavedAddress);

router.get('/payment-methods', getSavedPaymentMethods);
router.post('/payment-methods', addSavedPaymentMethod);
router.delete('/payment-methods/:id', deleteSavedPaymentMethod);

// Delivery and shipping
router.get('/delivery-options', getDeliveryOptions);
router.post('/shipping/calculate', calculateShipping);

// Checkout and orders
router.post('/checkout', initiateCheckout);
router.post('/payment/process', processPayment);
router.get('/orders', getOrderHistory);
router.get('/orders/:id', getOrder);
router.get('/orders/:id/track', trackOrder);
router.post('/orders/:id/reorder', bulkAddToCart); // Reuse bulkAddToCart for reordering

module.exports = router;
