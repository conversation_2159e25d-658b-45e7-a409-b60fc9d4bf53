import React, { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import {
  Container,
  Card,
  CardContent,
  TextField,
  Button,
  Typography,
  Box,
  Input,
  FormLabel
} from '@mui/material'
import { useSnackbar } from 'notistack'
import axios from '../utils/fixedAxios'

const CreatePost = () => {
  const [formData, setFormData] = useState({
    caption: '',
    image: null
  })
  const [loading, setLoading] = useState(false)
  const navigate = useNavigate()
  const { enqueueSnackbar } = useSnackbar()

  const handleSubmit = async (e) => {
    e.preventDefault()

    if (!formData.caption && !formData.image) {
      enqueueSnackbar('Please add a caption or image', { variant: 'error' })
      return
    }

    try {
      setLoading(true)
      const data = new FormData()
      data.append('caption', formData.caption)
      if (formData.image) {
        data.append('image', formData.image)
      }

      await axios.post('/api/posts', data, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })

      enqueueSnackbar('Post created successfully!', { variant: 'success' })
      navigate('/')
    } catch (error) {
      console.error('Error creating post:', error)
      enqueueSnackbar('Failed to create post', { variant: 'error' })
    } finally {
      setLoading(false)
    }
  }

  return (
    <Container maxWidth="sm" sx={{ py: 3 }}>
      <Card>
        <CardContent>
          <Typography variant="h4" component="h1" gutterBottom fontWeight="bold">
            Create Post
          </Typography>

          <Box component="form" onSubmit={handleSubmit} sx={{ mt: 2 }}>
            <TextField
              fullWidth
              multiline
              rows={4}
              label="Caption"
              value={formData.caption}
              onChange={(e) => setFormData({ ...formData, caption: e.target.value })}
              placeholder="What's on your mind?"
              margin="normal"
            />

            <Box sx={{ mt: 2, mb: 2 }}>
              <FormLabel component="legend">Image</FormLabel>
              <Input
                type="file"
                inputProps={{ accept: 'image/*' }}
                onChange={(e) => setFormData({ ...formData, image: e.target.files[0] })}
                sx={{ mt: 1 }}
              />
            </Box>

            <Box sx={{ display: 'flex', gap: 2, mt: 3 }}>
              <Button
                type="submit"
                variant="contained"
                disabled={loading}
                fullWidth
              >
                {loading ? 'Creating...' : 'Create Post'}
              </Button>
              <Button
                variant="outlined"
                onClick={() => navigate('/')}
              >
                Cancel
              </Button>
            </Box>
          </Box>
        </CardContent>
      </Card>
    </Container>
  )
}

export default CreatePost
