const express = require('express');
const router = express.Router();
const { protect } = require('../middleware/auth');
const {
  getLoyaltyAccount,
  getLoyaltyPrograms,
  joinLoyaltyProgram,
  redeemPoints,
  getReferralLink,
  processReferral,
  getLeaderboard,
  getUserBadges
} = require('../controllers/loyaltyController');

// Loyalty account routes
router.get('/account', protect, getLoyaltyAccount);
router.get('/programs', getLoyaltyPrograms);
router.post('/join/:programId', protect, joinLoyaltyProgram);
router.post('/redeem', protect, redeemPoints);

// Referral routes
router.get('/referral', protect, getReferralLink);
router.post('/referral/process', protect, processReferral);

// Gamification routes
router.get('/leaderboard', getLeaderboard);
router.get('/badges', protect, getUserBadges);

module.exports = router;
