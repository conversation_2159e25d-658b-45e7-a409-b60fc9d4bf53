const Story = require('../models/Story');
const User = require('../models/User');
const Follow = require('../models/Follow');
const { createError } = require('../utils/error');
const { uploadToCloudinary } = require('../config/cloudinary');
const mongoose = require('mongoose');

/**
 * Get stories feed - combines stories from following users and trending stories
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.getStoriesFeed = async (req, res, next) => {
  try {
    const { limit = 20, includeUserDetails = false } = req.query;

    // Find users that the current user follows
    const following = await Follow.find({ follower: req.user.id }).select('following');
    const followingIds = following.map(follow => follow.following);

    // Add current user's ID to include their own stories
    followingIds.push(req.user.id);

    // Find stories from followed users that haven't expired yet
    const followingStories = await Story.find({
      user: { $in: followingIds },
      expiresAt: { $gt: new Date() }
    })
      .sort({ createdAt: -1 })
      .limit(parseInt(limit) * 0.7) // 70% of stories from following
      .populate(includeUserDetails ? 'user' : 'user', 'username name profilePicture isVerified')
      .populate('emotions.emotion', 'name color icon category');

    // Find trending stories (stories with most views) that haven't expired yet
    const trendingStories = await Story.find({
      user: { $nin: followingIds }, // Exclude stories from users we already follow
      expiresAt: { $gt: new Date() }
    })
      .sort({ 'viewers.length': -1, createdAt: -1 })
      .limit(parseInt(limit) * 0.3) // 30% trending stories
      .populate(includeUserDetails ? 'user' : 'user', 'username name profilePicture isVerified')
      .populate('emotions.emotion', 'name color icon category');

    // Combine stories
    const allStories = [...followingStories, ...trendingStories];

    // Sort by creation date (newest first)
    allStories.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

    res.status(200).json({
      success: true,
      count: allStories.length,
      data: allStories
    });
  } catch (err) {
    console.error('Error getting stories feed:', err);
    next(err);
  }
};

/**
 * Get all stories with pagination and filtering
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.getStories = async (req, res, next) => {
  try {
    const { page = 1, limit = 20 } = req.query;

    // Find stories that haven't expired yet
    const stories = await Story.find({ expiresAt: { $gt: new Date() } })
      .sort({ createdAt: -1 })
      .skip((parseInt(page) - 1) * parseInt(limit))
      .limit(parseInt(limit))
      .populate('user', 'username name profilePicture isVerified')
      .populate('emotions.emotion', 'name color icon category');

    // Get total count for pagination
    const total = await Story.countDocuments({ expiresAt: { $gt: new Date() } });

    res.status(200).json({
      success: true,
      count: stories.length,
      total,
      data: stories,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages: Math.ceil(total / parseInt(limit))
      }
    });
  } catch (err) {
    console.error('Error getting stories:', err);
    next(err);
  }
};

/**
 * Get a single story by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.getStory = async (req, res, next) => {
  try {
    const story = await Story.findById(req.params.id)
      .populate('user', 'username name profilePicture isVerified')
      .populate('emotions.emotion', 'name color icon category');

    if (!story) {
      return next(createError(404, 'Story not found'));
    }

    // Check if story has expired
    if (story.expiresAt < new Date()) {
      return next(createError(404, 'Story has expired'));
    }

    res.status(200).json({
      success: true,
      data: story
    });
  } catch (err) {
    console.error('Error getting story:', err);
    next(err);
  }
};

/**
 * Create a new story
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.createStory = async (req, res, next) => {
  try {
    console.log('Creating story with data:', req.body);

    // Check if media file is provided
    if (!req.file) {
      return next(createError(400, 'Media file is required'));
    }

    // Add user to request body
    req.body.user = req.user.id;

    // Create story data object
    const storyData = {
      user: req.user.id,
      media: {
        url: req.file.cloudinaryUrl || req.file.url,
        type: req.body.mediaType || req.file.fileType || (req.file.mimetype.startsWith('video/') ? 'video' : 'image'),
        publicId: req.file.cloudinaryPublicId
      },
      caption: req.body.caption
    };

    // Add emotions if provided
    if (req.body.emotions) {
      try {
        storyData.emotions = JSON.parse(req.body.emotions);
      } catch (error) {
        console.error('Error parsing emotions:', error);
        return next(createError(400, 'Invalid emotions data'));
      }
    }

    // Add AR effects if provided
    if (req.body.arEffects) {
      try {
        storyData.arEffects = JSON.parse(req.body.arEffects);
      } catch (error) {
        console.error('Error parsing AR effects:', error);
        return next(createError(400, 'Invalid AR effects data'));
      }
    }

    // Create story
    const story = await Story.create(storyData);

    // Populate user details
    await story.populate('user', 'username name profilePicture isVerified');
    await story.populate('emotions.emotion', 'name color icon category');

    res.status(201).json({
      success: true,
      data: story
    });
  } catch (err) {
    console.error('Error creating story:', err);
    next(err);
  }
};

/**
 * Delete a story
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.deleteStory = async (req, res, next) => {
  try {
    const story = await Story.findById(req.params.id);

    if (!story) {
      return next(createError(404, 'Story not found'));
    }

    // Check if user is story owner
    if (story.user.toString() !== req.user.id) {
      return next(createError(403, 'Not authorized to delete this story'));
    }

    // Delete story
    await story.remove();

    res.status(200).json({
      success: true,
      data: {}
    });
  } catch (err) {
    console.error('Error deleting story:', err);
    next(err);
  }
};

/**
 * Get stories by user ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.getUserStories = async (req, res, next) => {
  try {
    const userId = req.params.userId;

    // Find stories by user that haven't expired yet
    const stories = await Story.find({
      user: userId,
      expiresAt: { $gt: new Date() }
    })
      .sort({ createdAt: -1 })
      .populate('user', 'username name profilePicture isVerified')
      .populate('emotions.emotion', 'name color icon category');

    res.status(200).json({
      success: true,
      count: stories.length,
      data: stories
    });
  } catch (err) {
    console.error('Error getting user stories:', err);
    next(err);
  }
};

/**
 * Get stories from users the current user follows
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.getFollowingStories = async (req, res, next) => {
  try {
    // Find users that the current user follows
    const following = await Follow.find({ follower: req.user.id }).select('following');
    const followingIds = following.map(follow => follow.following);

    // Add current user's ID to include their own stories
    followingIds.push(req.user.id);

    // Find stories from followed users that haven't expired yet
    const stories = await Story.find({
      user: { $in: followingIds },
      expiresAt: { $gt: new Date() }
    })
      .sort({ createdAt: -1 })
      .populate('user', 'username name profilePicture isVerified')
      .populate('emotions.emotion', 'name color icon category');

    res.status(200).json({
      success: true,
      count: stories.length,
      data: stories
    });
  } catch (err) {
    console.error('Error getting following stories:', err);
    next(err);
  }
};

/**
 * Mark a story as viewed by the current user
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.viewStory = async (req, res, next) => {
  try {
    const story = await Story.findById(req.params.id);

    if (!story) {
      return next(createError(404, 'Story not found'));
    }

    // Check if story has expired
    if (story.expiresAt < new Date()) {
      return next(createError(404, 'Story has expired'));
    }

    // Check if user has already viewed this story
    const alreadyViewed = story.viewers.some(viewer =>
      viewer.user.toString() === req.user.id
    );

    if (!alreadyViewed) {
      // Add user to viewers
      story.viewers.push({
        user: req.user.id,
        viewedAt: new Date()
      });

      await story.save();
    }

    res.status(200).json({
      success: true,
      data: {
        viewed: true,
        viewedAt: new Date()
      }
    });
  } catch (err) {
    console.error('Error viewing story:', err);
    next(err);
  }
};

/**
 * Get viewers of a story
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.getStoryViewers = async (req, res, next) => {
  try {
    const story = await Story.findById(req.params.id)
      .populate('viewers.user', 'username name profilePicture isVerified');

    if (!story) {
      return next(createError(404, 'Story not found'));
    }

    // Check if user is story owner
    if (story.user.toString() !== req.user.id) {
      return next(createError(403, 'Not authorized to view story viewers'));
    }

    res.status(200).json({
      success: true,
      count: story.viewers.length,
      data: story.viewers
    });
  } catch (err) {
    console.error('Error getting story viewers:', err);
    next(err);
  }
};
