const express = require('express');
const router = express.Router();
const { protect } = require('../middleware/auth');
const {
  getAudioCues,
  getAudioCue,
  createAudioCue,
  updateAudioCue,
  deleteAudioCue,
} = require('../controllers/audioCueController');

// Public routes
router.get('/', getAudioCues);
router.get('/:id', getAudioCue);

// Protected routes
router.use(protect);
router.post('/', createAudioCue);
router.put('/:id', updateAudioCue);
router.delete('/:id', deleteAudioCue);

module.exports = router;
