const express = require('express');
const router = express.Router();
const { protect, optionalAuth } = require('../middleware/auth');
const {
  getForYouLiveFeed,
  getLocalLivesFeed,
  getRisingStarsFeed
} = require('../controllers/personalizedFeedController');

// For You LIVE feed (requires authentication)
router.get('/for-you-live', protect, getForYouLiveFeed);

// Local Lives feed (requires authentication for location)
router.get('/local-lives', protect, getLocalLivesFeed);

// Rising Stars feed (public with optional auth for personalization)
router.get('/rising-stars', optionalAuth, getRisingStarsFeed);

module.exports = router;
