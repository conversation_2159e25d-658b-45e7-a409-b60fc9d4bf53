const fs = require('fs');
const path = require('path');

console.log('🔍 Validating Authentication System Structure...\n');

// Check if required files exist
const requiredFiles = [
  'controllers/auth.js',
  'routes/auth.js',
  'models/User.js',
  'middleware/auth.js',
  '.env'
];

console.log('📁 Checking required files...');
let allFilesExist = true;

requiredFiles.forEach(file => {
  const filePath = path.join(__dirname, file);
  if (fs.existsSync(filePath)) {
    console.log(`✅ ${file} - exists`);
  } else {
    console.log(`❌ ${file} - missing`);
    allFilesExist = false;
  }
});

// Check if duplicate authController.js was removed
const duplicateFile = path.join(__dirname, 'controllers/authController.js');
if (!fs.existsSync(duplicateFile)) {
  console.log('✅ Duplicate authController.js - properly removed');
} else {
  console.log('⚠️  Duplicate authController.js - still exists (should be removed)');
}

console.log('\n📋 Checking controller exports...');

try {
  const authController = require('./controllers/auth.js');
  const requiredExports = ['register', 'login', 'logout', 'getMe', 'refreshToken'];
  
  requiredExports.forEach(exportName => {
    if (typeof authController[exportName] === 'function') {
      console.log(`✅ ${exportName} - exported as function`);
    } else {
      console.log(`❌ ${exportName} - missing or not a function`);
      allFilesExist = false;
    }
  });
} catch (error) {
  console.log(`❌ Error loading auth controller: ${error.message}`);
  allFilesExist = false;
}

console.log('\n🗄️  Checking User model...');

try {
  const User = require('./models/User.js');
  const userInstance = new User({
    name: 'Test',
    username: 'test',
    email: '<EMAIL>',
    password: 'password123'
  });
  
  // Check if required methods exist
  const requiredMethods = ['getSignedJwtToken', 'matchPassword', 'getPublicProfile'];
  
  requiredMethods.forEach(method => {
    if (typeof userInstance[method] === 'function') {
      console.log(`✅ ${method} - method exists`);
    } else {
      console.log(`❌ ${method} - method missing`);
      allFilesExist = false;
    }
  });
  
  // Check if required fields exist in schema
  const schema = User.schema;
  const requiredFields = ['name', 'username', 'email', 'password', 'fullName', 'isOnline', 'lastActive'];
  
  requiredFields.forEach(field => {
    if (schema.paths[field]) {
      console.log(`✅ ${field} - field exists in schema`);
    } else {
      console.log(`❌ ${field} - field missing from schema`);
      allFilesExist = false;
    }
  });
  
} catch (error) {
  console.log(`❌ Error loading User model: ${error.message}`);
  allFilesExist = false;
}

console.log('\n🛡️  Checking middleware...');

try {
  const authMiddleware = require('./middleware/auth.js');
  const requiredMiddleware = ['protect', 'authorize'];
  
  requiredMiddleware.forEach(middleware => {
    if (typeof authMiddleware[middleware] === 'function') {
      console.log(`✅ ${middleware} - middleware exists`);
    } else {
      console.log(`❌ ${middleware} - middleware missing`);
      allFilesExist = false;
    }
  });
} catch (error) {
  console.log(`❌ Error loading auth middleware: ${error.message}`);
  allFilesExist = false;
}

console.log('\n🔧 Checking environment configuration...');

try {
  require('dotenv').config();
  const requiredEnvVars = ['JWT_SECRET', 'JWT_EXPIRE', 'MONGO_URI', 'PORT'];
  
  requiredEnvVars.forEach(envVar => {
    if (process.env[envVar]) {
      console.log(`✅ ${envVar} - configured`);
    } else {
      console.log(`❌ ${envVar} - missing from .env`);
      allFilesExist = false;
    }
  });
} catch (error) {
  console.log(`❌ Error loading environment variables: ${error.message}`);
  allFilesExist = false;
}

console.log('\n' + '='.repeat(50));

if (allFilesExist) {
  console.log('🎉 Authentication system structure validation PASSED!');
  console.log('✅ All required files, exports, and configurations are present.');
  console.log('\n📝 Next steps:');
  console.log('   1. Start MongoDB: mongod');
  console.log('   2. Start backend: npm start');
  console.log('   3. Start frontend: cd ../frontend && npm run dev');
  console.log('   4. Test authentication in browser');
} else {
  console.log('❌ Authentication system structure validation FAILED!');
  console.log('⚠️  Please fix the issues above before proceeding.');
}

console.log('='.repeat(50));
