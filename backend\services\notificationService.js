const Notification = require('../models/Notification');
const User = require('../models/User');

// Import socket service with error handling
let socketService;
try {
  socketService = require('./socketService');
} catch (error) {
  console.warn('Socket service not available:', error.message);
  socketService = { emitNotification: () => {} };
}

class NotificationService {
  constructor() {
    this.notificationTypes = {
      ORDER_PLACED: 'order_placed',
      ORDER_CONFIRMED: 'order_confirmed',
      ORDER_SHIPPED: 'order_shipped',
      ORDER_DELIVERED: 'order_delivered',
      PAYMENT_RECEIVED: 'payment_received',
      PAYOUT_PROCESSED: 'payout_processed',
      VENDOR_APPROVED: 'vendor_approved',
      VENDOR_BANNED: 'vendor_banned',
      PROMOTION_CREATED: 'promotion_created',
      MESSAGE_RECEIVED: 'message_received',
      FOLLOW_REQUEST: 'follow_request',
      LIKE_RECEIVED: 'like_received',
      COMMENT_RECEIVED: 'comment_received',
      ADMIN_ANNOUNCEMENT: 'admin_announcement'
    };
  }

  // Send notification to user
  async sendNotification(userId, notificationData) {
    try {
      const { type, title, message, data = {}, priority = 'normal' } = notificationData;

      // Create notification in database
      const notification = new Notification({
        recipient: userId,
        type,
        title,
        message,
        data,
        priority,
        createdAt: new Date()
      });

      await notification.save();

      // Get user preferences
      const user = await User.findById(userId).select('notificationPreferences');
      
      if (user && this.shouldSendNotification(user, type)) {
        // Send real-time notification via socket
        socketService.emitNotification(userId, {
          id: notification._id,
          type,
          title,
          message,
          data,
          priority,
          createdAt: notification.createdAt
        });

        // Send push notification if enabled
        if (user.notificationPreferences?.pushNotifications) {
          await this.sendPushNotification(userId, notification);
        }

        // Send email notification if enabled
        if (user.notificationPreferences?.emailNotifications && priority === 'high') {
          await this.sendEmailNotification(userId, notification);
        }
      }

      return notification;
    } catch (error) {
      console.error('Error sending notification:', error);
      throw error;
    }
  }

  // Send bulk notifications
  async sendBulkNotifications(userIds, notificationData) {
    try {
      const notifications = userIds.map(userId => ({
        recipient: userId,
        ...notificationData,
        createdAt: new Date()
      }));

      const savedNotifications = await Notification.insertMany(notifications);

      // Send real-time notifications
      for (let i = 0; i < userIds.length; i++) {
        const userId = userIds[i];
        const notification = savedNotifications[i];
        
        socketService.emitNotification(userId, {
          id: notification._id,
          type: notification.type,
          title: notification.title,
          message: notification.message,
          data: notification.data,
          priority: notification.priority,
          createdAt: notification.createdAt
        });
      }

      return savedNotifications;
    } catch (error) {
      console.error('Error sending bulk notifications:', error);
      throw error;
    }
  }

  // Send notification to all users
  async broadcastNotification(notificationData) {
    try {
      const users = await User.find({ isActive: true }).select('_id');
      const userIds = users.map(user => user._id);
      
      return await this.sendBulkNotifications(userIds, notificationData);
    } catch (error) {
      console.error('Error broadcasting notification:', error);
      throw error;
    }
  }

  // Send notification to users by role
  async sendNotificationByRole(role, notificationData) {
    try {
      const users = await User.find({ role, isActive: true }).select('_id');
      const userIds = users.map(user => user._id);
      
      return await this.sendBulkNotifications(userIds, notificationData);
    } catch (error) {
      console.error('Error sending notification by role:', error);
      throw error;
    }
  }

  // Get user notifications
  async getUserNotifications(userId, options = {}) {
    try {
      const { page = 1, limit = 20, unreadOnly = false } = options;
      
      let query = { recipient: userId };
      if (unreadOnly) {
        query.isRead = false;
      }

      const notifications = await Notification.find(query)
        .sort({ createdAt: -1 })
        .limit(limit * 1)
        .skip((page - 1) * limit);

      const total = await Notification.countDocuments(query);
      const unreadCount = await Notification.countDocuments({ 
        recipient: userId, 
        isRead: false 
      });

      return {
        notifications,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit)
        },
        unreadCount
      };
    } catch (error) {
      console.error('Error getting user notifications:', error);
      throw error;
    }
  }

  // Mark notification as read
  async markAsRead(notificationId, userId) {
    try {
      const notification = await Notification.findOneAndUpdate(
        { _id: notificationId, recipient: userId },
        { isRead: true, readAt: new Date() },
        { new: true }
      );

      return notification;
    } catch (error) {
      console.error('Error marking notification as read:', error);
      throw error;
    }
  }

  // Mark all notifications as read
  async markAllAsRead(userId) {
    try {
      const result = await Notification.updateMany(
        { recipient: userId, isRead: false },
        { isRead: true, readAt: new Date() }
      );

      return result;
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
      throw error;
    }
  }

  // Delete notification
  async deleteNotification(notificationId, userId) {
    try {
      const notification = await Notification.findOneAndDelete({
        _id: notificationId,
        recipient: userId
      });

      return notification;
    } catch (error) {
      console.error('Error deleting notification:', error);
      throw error;
    }
  }

  // Check if notification should be sent based on user preferences
  shouldSendNotification(user, type) {
    const preferences = user.notificationPreferences || {};
    
    // Default to true if no preferences set
    if (!preferences) return true;

    // Check specific notification type preferences
    switch (type) {
      case this.notificationTypes.ORDER_PLACED:
      case this.notificationTypes.ORDER_CONFIRMED:
      case this.notificationTypes.ORDER_SHIPPED:
      case this.notificationTypes.ORDER_DELIVERED:
        return preferences.orderUpdates !== false;
      
      case this.notificationTypes.PAYMENT_RECEIVED:
      case this.notificationTypes.PAYOUT_PROCESSED:
        return preferences.paymentUpdates !== false;
      
      case this.notificationTypes.MESSAGE_RECEIVED:
        return preferences.messages !== false;
      
      case this.notificationTypes.FOLLOW_REQUEST:
      case this.notificationTypes.LIKE_RECEIVED:
      case this.notificationTypes.COMMENT_RECEIVED:
        return preferences.socialUpdates !== false;
      
      case this.notificationTypes.PROMOTION_CREATED:
        return preferences.promotions !== false;
      
      default:
        return true;
    }
  }

  // Send push notification (placeholder for actual implementation)
  async sendPushNotification(userId, notification) {
    try {
      // This would integrate with a push notification service like FCM, APNs, etc.
      console.log(`📱 Push notification sent to user ${userId}:`, notification.title);
      
      // Placeholder implementation
      return { success: true, provider: 'fcm' };
    } catch (error) {
      console.error('Error sending push notification:', error);
      return { success: false, error: error.message };
    }
  }

  // Send email notification (placeholder for actual implementation)
  async sendEmailNotification(userId, notification) {
    try {
      // This would integrate with an email service like SendGrid, Mailgun, etc.
      console.log(`📧 Email notification sent to user ${userId}:`, notification.title);
      
      // Placeholder implementation
      return { success: true, provider: 'sendgrid' };
    } catch (error) {
      console.error('Error sending email notification:', error);
      return { success: false, error: error.message };
    }
  }

  // Clean up old notifications
  async cleanupOldNotifications(daysOld = 30) {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysOld);

      const result = await Notification.deleteMany({
        createdAt: { $lt: cutoffDate },
        isRead: true
      });

      console.log(`🧹 Cleaned up ${result.deletedCount} old notifications`);
      return result;
    } catch (error) {
      console.error('Error cleaning up old notifications:', error);
      throw error;
    }
  }

  // Get notification statistics
  async getNotificationStats(userId) {
    try {
      const stats = await Notification.aggregate([
        { $match: { recipient: userId } },
        {
          $group: {
            _id: '$type',
            count: { $sum: 1 },
            unreadCount: { $sum: { $cond: ['$isRead', 0, 1] } }
          }
        }
      ]);

      const totalCount = await Notification.countDocuments({ recipient: userId });
      const unreadCount = await Notification.countDocuments({ 
        recipient: userId, 
        isRead: false 
      });

      return {
        total: totalCount,
        unread: unreadCount,
        byType: stats
      };
    } catch (error) {
      console.error('Error getting notification stats:', error);
      throw error;
    }
  }
}

// Create singleton instance
const notificationService = new NotificationService();

module.exports = notificationService;
