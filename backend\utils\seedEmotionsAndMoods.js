const mongoose = require('mongoose');
const dotenv = require('dotenv');
const Emotion = require('../models/Emotion');
const Mood = require('../models/Mood');

// Load environment variables
dotenv.config();

// Initial emotions data
const emotions = [
  {
    name: 'Happy',
    category: 'positive',
    description: 'Feeling of joy and contentment',
    intensity: 7,
    color: '#FFD700', // Gold
    gradient: {
      start: '#FFD700',
      end: '#FFA500'
    },
    icon: 'emoji-smile',
    soundEffect: '/sounds/emotions/happy.mp3',
    visualEffect: '/effects/emotions/happy.json',
    keywords: ['joy', 'cheerful', 'pleased', 'content', 'delighted'],
    isDefault: true,
    isActive: true
  },
  {
    name: 'Sad',
    category: 'negative',
    description: 'Feeling of sorrow or unhappiness',
    intensity: 6,
    color: '#6495ED', // Cornflower Blue
    gradient: {
      start: '#6495ED',
      end: '#4169E1'
    },
    icon: 'emoji-frown',
    soundEffect: '/sounds/emotions/sad.mp3',
    visualEffect: '/effects/emotions/sad.json',
    keywords: ['sorrow', 'unhappy', 'down', 'blue', 'melancholy'],
    isDefault: true,
    isActive: true
  },
  {
    name: 'Excited',
    category: 'positive',
    description: 'Feeling of enthusiasm and eagerness',
    intensity: 9,
    color: '#FF4500', // Orange Red
    gradient: {
      start: '#FF4500',
      end: '#FF8C00'
    },
    icon: 'emoji-laughing',
    soundEffect: '/sounds/emotions/excited.mp3',
    visualEffect: '/effects/emotions/excited.json',
    keywords: ['enthusiastic', 'eager', 'thrilled', 'animated', 'energetic'],
    isDefault: true,
    isActive: true
  },
  {
    name: 'Calm',
    category: 'positive',
    description: 'Feeling of peace and tranquility',
    intensity: 4,
    color: '#48D1CC', // Medium Turquoise
    gradient: {
      start: '#48D1CC',
      end: '#20B2AA'
    },
    icon: 'emoji-neutral',
    soundEffect: '/sounds/emotions/calm.mp3',
    visualEffect: '/effects/emotions/calm.json',
    keywords: ['peaceful', 'tranquil', 'serene', 'relaxed', 'composed'],
    isDefault: true,
    isActive: true
  },
  {
    name: 'Angry',
    category: 'negative',
    description: 'Feeling of strong displeasure or hostility',
    intensity: 8,
    color: '#DC143C', // Crimson
    gradient: {
      start: '#DC143C',
      end: '#B22222'
    },
    icon: 'emoji-angry',
    soundEffect: '/sounds/emotions/angry.mp3',
    visualEffect: '/effects/emotions/angry.json',
    keywords: ['mad', 'furious', 'enraged', 'irritated', 'annoyed'],
    isDefault: true,
    isActive: true
  },
  {
    name: 'Confused',
    category: 'neutral',
    description: 'Feeling of uncertainty or puzzlement',
    intensity: 5,
    color: '#9370DB', // Medium Purple
    gradient: {
      start: '#9370DB',
      end: '#8A2BE2'
    },
    icon: 'emoji-dizzy',
    soundEffect: '/sounds/emotions/confused.mp3',
    visualEffect: '/effects/emotions/confused.json',
    keywords: ['puzzled', 'perplexed', 'bewildered', 'uncertain', 'disoriented'],
    isDefault: true,
    isActive: true
  },
  {
    name: 'Love',
    category: 'positive',
    description: 'Feeling of deep affection and attachment',
    intensity: 9,
    color: '#FF1493', // Deep Pink
    gradient: {
      start: '#FF1493',
      end: '#FF69B4'
    },
    icon: 'heart-fill',
    soundEffect: '/sounds/emotions/love.mp3',
    visualEffect: '/effects/emotions/love.json',
    keywords: ['adoration', 'affection', 'fondness', 'attachment', 'devotion'],
    isDefault: true,
    isActive: true
  },
  {
    name: 'Anxious',
    category: 'negative',
    description: 'Feeling of worry or unease',
    intensity: 7,
    color: '#FF8C00', // Dark Orange
    gradient: {
      start: '#FF8C00',
      end: '#FFA07A'
    },
    icon: 'emoji-dizzy',
    soundEffect: '/sounds/emotions/anxious.mp3',
    visualEffect: '/effects/emotions/anxious.json',
    keywords: ['worried', 'uneasy', 'nervous', 'apprehensive', 'concerned'],
    isDefault: true,
    isActive: true
  }
];

// Initial moods data
const moods = [
  {
    name: 'Energetic',
    description: 'Full of energy and enthusiasm',
    relatedEmotions: [], // Will be populated after emotions are created
    color: '#FF4500', // Orange Red
    gradient: {
      start: '#FF4500',
      end: '#FFA500'
    },
    icon: 'lightning-fill',
    backgroundImage: '/images/moods/energetic.jpg',
    soundscape: '/sounds/moods/energetic.mp3',
    keywords: ['active', 'dynamic', 'lively', 'vigorous', 'spirited'],
    category: 'energetic',
    isDefault: true,
    isActive: true,
    suggestedContent: {
      contentTypes: ['post', 'reel', 'story'],
      emotions: [], // Will be populated after emotions are created
      hashtags: ['energy', 'active', 'motivation', 'workout', 'fitness']
    }
  },
  {
    name: 'Relaxed',
    description: 'Free from tension and anxiety',
    relatedEmotions: [], // Will be populated after emotions are created
    color: '#48D1CC', // Medium Turquoise
    gradient: {
      start: '#48D1CC',
      end: '#20B2AA'
    },
    icon: 'cloud',
    backgroundImage: '/images/moods/relaxed.jpg',
    soundscape: '/sounds/moods/relaxed.mp3',
    keywords: ['calm', 'peaceful', 'tranquil', 'serene', 'composed'],
    category: 'calm',
    isDefault: true,
    isActive: true,
    suggestedContent: {
      contentTypes: ['post', 'story'],
      emotions: [], // Will be populated after emotions are created
      hashtags: ['relax', 'peace', 'calm', 'mindfulness', 'meditation']
    }
  },
  {
    name: 'Creative',
    description: 'Feeling imaginative and inspired',
    relatedEmotions: [], // Will be populated after emotions are created
    color: '#9370DB', // Medium Purple
    gradient: {
      start: '#9370DB',
      end: '#8A2BE2'
    },
    icon: 'palette-fill',
    backgroundImage: '/images/moods/creative.jpg',
    soundscape: '/sounds/moods/creative.mp3',
    keywords: ['inspired', 'imaginative', 'artistic', 'innovative', 'original'],
    category: 'creative',
    isDefault: true,
    isActive: true,
    suggestedContent: {
      contentTypes: ['post', 'reel', 'story'],
      emotions: [], // Will be populated after emotions are created
      hashtags: ['create', 'art', 'inspiration', 'design', 'imagination']
    }
  },
  {
    name: 'Reflective',
    description: 'In a thoughtful and contemplative state',
    relatedEmotions: [], // Will be populated after emotions are created
    color: '#6495ED', // Cornflower Blue
    gradient: {
      start: '#6495ED',
      end: '#4169E1'
    },
    icon: 'moon-stars-fill',
    backgroundImage: '/images/moods/reflective.jpg',
    soundscape: '/sounds/moods/reflective.mp3',
    keywords: ['thoughtful', 'contemplative', 'introspective', 'meditative', 'pensive'],
    category: 'reflective',
    isDefault: true,
    isActive: true,
    suggestedContent: {
      contentTypes: ['post', 'story'],
      emotions: [], // Will be populated after emotions are created
      hashtags: ['thoughts', 'reflection', 'mindfulness', 'philosophy', 'introspection']
    }
  }
];

// Connect to MongoDB
mongoose.connect(process.env.MONGO_URI || 'mongodb://localhost:27017/letstalk')
  .then(async () => {
    console.log('Connected to MongoDB');
    
    try {
      // Clear existing data
      await Emotion.deleteMany({});
      await Mood.deleteMany({});
      console.log('Cleared existing emotions and moods');
      
      // Create emotions
      const createdEmotions = await Emotion.insertMany(emotions);
      console.log(`Created ${createdEmotions.length} emotions`);
      
      // Map emotion names to IDs
      const emotionMap = {};
      createdEmotions.forEach(emotion => {
        emotionMap[emotion.name] = emotion._id;
      });
      
      // Update moods with related emotions
      moods[0].relatedEmotions = [emotionMap['Happy'], emotionMap['Excited']];
      moods[0].suggestedContent.emotions = [emotionMap['Happy'], emotionMap['Excited']];
      
      moods[1].relatedEmotions = [emotionMap['Calm']];
      moods[1].suggestedContent.emotions = [emotionMap['Calm']];
      
      moods[2].relatedEmotions = [emotionMap['Happy'], emotionMap['Excited'], emotionMap['Confused']];
      moods[2].suggestedContent.emotions = [emotionMap['Happy'], emotionMap['Excited']];
      
      moods[3].relatedEmotions = [emotionMap['Calm'], emotionMap['Sad'], emotionMap['Confused']];
      moods[3].suggestedContent.emotions = [emotionMap['Calm']];
      
      // Create moods
      const createdMoods = await Mood.insertMany(moods);
      console.log(`Created ${createdMoods.length} moods`);
      
      console.log('Seed completed successfully');
    } catch (error) {
      console.error('Error seeding data:', error);
    } finally {
      mongoose.disconnect();
      console.log('Disconnected from MongoDB');
    }
  })
  .catch(err => {
    console.error('MongoDB connection error:', err.message);
    process.exit(1);
  });
