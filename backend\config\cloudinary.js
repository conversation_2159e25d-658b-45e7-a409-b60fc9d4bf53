const cloudinary = require('cloudinary').v2;
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// Configure Cloudinary with values from environment variables
const cloudinaryConfig = {
  cloud_name: process.env.CLOUDINARY_CLOUD_NAME || 'droja6ntk',
  api_key: process.env.CLOUDINARY_API_KEY || '366288452711478',
  api_secret: process.env.CLOUDINARY_API_SECRET || 'IApH9iLAbpU1eYLTQjwtXRPoytw',
  secure: true
};

// Verify the configuration is valid
if (!cloudinaryConfig.cloud_name || !cloudinaryConfig.api_key || !cloudinaryConfig.api_secret) {
  console.error('❌ ERROR: Cloudinary configuration is incomplete!');
  throw new Error('Cloudinary configuration is incomplete');
}

// Apply the configuration
cloudinary.config(cloudinaryConfig);

// Log the configuration that's actually being used (for debugging)
console.log('Using Cloudinary configuration:', {
  cloud_name: cloudinaryConfig.cloud_name,
  api_key: cloudinaryConfig.api_key ? `${cloudinaryConfig.api_key.substring(0, 5)}...` : 'Not set',
  api_secret: cloudinaryConfig.api_secret ? 'Secret is set' : 'Secret is not set',
});

// Log Cloudinary configuration for debugging (without showing the full API secret)
const sanitizedConfig = {
  cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
  api_key: process.env.CLOUDINARY_API_KEY ? `${process.env.CLOUDINARY_API_KEY.substring(0, 5)}...` : 'Not set',
  api_secret: process.env.CLOUDINARY_API_SECRET ? 'Secret is set' : 'Secret is not set',
};
console.log('Cloudinary Configuration:', sanitizedConfig);

// Helper function to upload a file to Cloudinary
const uploadToCloudinary = async (filePath, folder = 'letstalk', options = {}) => {
  try {
    // Create a timestamp for the signature
    const timestamp = Math.floor(Date.now() / 1000);

    // Use absolute minimal options to avoid signature issues
    const uploadOptions = {
      folder,
      timestamp,
      resource_type: 'auto'
    };

    // Only add resource_type if provided (essential for videos)
    if (options.resource_type) {
      uploadOptions.resource_type = options.resource_type;
    }

    console.log(`Uploading file to Cloudinary folder: ${folder}`);
    console.log('Upload options:', {
      folder: uploadOptions.folder,
      resource_type: uploadOptions.resource_type,
      timestamp: uploadOptions.timestamp
    });

    // Upload file to Cloudinary with minimal options
    const result = await cloudinary.uploader.upload(filePath, uploadOptions);
    console.log(`Successfully uploaded to Cloudinary: ${result.public_id}`);
    return result;
  } catch (error) {
    console.error('Error uploading to Cloudinary:', error);
    console.error('Error details:', {
      message: error.message,
      name: error.name,
      code: error.code,
      statusCode: error.http_code || error.statusCode
    });

    // Try one more time with absolute minimal options and fresh timestamp
    try {
      console.log('Retrying with absolute minimal options...');

      // Force refresh Cloudinary configuration
      cloudinary.config(cloudinaryConfig);

      // Create a fresh timestamp
      const timestamp = Math.floor(Date.now() / 1000);

      // Use absolute minimal options
      const minimalOptions = {
        folder,
        timestamp,
        resource_type: options.resource_type || 'auto'
      };

      console.log('Retry upload options:', {
        folder: minimalOptions.folder,
        resource_type: minimalOptions.resource_type,
        timestamp: minimalOptions.timestamp
      });

      const result = await cloudinary.uploader.upload(filePath, minimalOptions);
      console.log(`Successfully uploaded to Cloudinary on retry: ${result.public_id}`);
      return result;
    } catch (retryError) {
      console.error('Retry upload failed:', retryError);
      throw retryError;
    }
  }
};

// Helper function to delete a file from Cloudinary
const deleteFromCloudinary = async (publicId, resource_type = 'image') => {
  try {
    const result = await cloudinary.uploader.destroy(publicId, { resource_type });
    return result;
  } catch (error) {
    console.error('Error deleting from Cloudinary:', error);
    throw error;
  }
};

// Helper function to generate a Cloudinary URL with transformations
const generateUrl = (publicId, options = {}) => {
  return cloudinary.url(publicId, options);
};

module.exports = {
  cloudinary,
  uploadToCloudinary,
  deleteFromCloudinary,
  generateUrl
};
