const mongoose = require('mongoose');
const Currency = require('../models/Currency');

const currencies = [
  {
    code: 'USD',
    name: 'US Dollar',
    symbol: '$',
    symbolPosition: 'before',
    decimalPlaces: 2,
    exchangeRates: [
      { toCurrency: 'EUR', rate: 0.85, provider: 'manual', isActive: true },
      { toCurrency: 'GBP', rate: 0.73, provider: 'manual', isActive: true },
      { toCurrency: 'JPY', rate: 110.0, provider: 'manual', isActive: true }
    ],
    taxConfiguration: {
      vatRate: 0,
      salesTaxRate: 8.5,
      withholdingTaxRate: 0,
      taxRegion: 'US',
      taxInclusive: false
    },
    payoutSupport: {
      bankTransfer: {
        supported: true,
        minimumAmount: 10,
        processingFee: 5,
        processingTime: '1-3 business days'
      },
      paypal: {
        supported: true,
        minimumAmount: 1,
        processingFee: 0.30,
        processingTime: 'Instant'
      },
      mobileMoney: {
        supported: false,
        minimumAmount: 5,
        processingFee: 0,
        processingTime: 'Instant'
      },
      crypto: {
        supported: true,
        supportedCurrencies: ['BTC', 'ETH', 'USDC', 'USDT'],
        minimumAmount: 20,
        processingFee: 0,
        processingTime: '10-60 minutes'
      }
    },
    isActive: true,
    isBaseCurrency: true,
    supportedCountries: ['US', 'CA']
  },
  {
    code: 'EUR',
    name: 'Euro',
    symbol: '€',
    symbolPosition: 'before',
    decimalPlaces: 2,
    exchangeRates: [
      { toCurrency: 'USD', rate: 1.18, provider: 'manual', isActive: true },
      { toCurrency: 'GBP', rate: 0.86, provider: 'manual', isActive: true }
    ],
    taxConfiguration: {
      vatRate: 20,
      salesTaxRate: 0,
      withholdingTaxRate: 0,
      taxRegion: 'EU',
      taxInclusive: true
    },
    payoutSupport: {
      bankTransfer: {
        supported: true,
        minimumAmount: 10,
        processingFee: 3,
        processingTime: '1-2 business days'
      },
      paypal: {
        supported: true,
        minimumAmount: 1,
        processingFee: 0.35,
        processingTime: 'Instant'
      }
    },
    isActive: true,
    supportedCountries: ['DE', 'FR', 'IT', 'ES', 'NL']
  },
  {
    code: 'GBP',
    name: 'British Pound',
    symbol: '£',
    symbolPosition: 'before',
    decimalPlaces: 2,
    exchangeRates: [
      { toCurrency: 'USD', rate: 1.37, provider: 'manual', isActive: true },
      { toCurrency: 'EUR', rate: 1.16, provider: 'manual', isActive: true }
    ],
    taxConfiguration: {
      vatRate: 20,
      salesTaxRate: 0,
      withholdingTaxRate: 0,
      taxRegion: 'UK',
      taxInclusive: true
    },
    payoutSupport: {
      bankTransfer: {
        supported: true,
        minimumAmount: 10,
        processingFee: 2,
        processingTime: '1-2 business days'
      },
      paypal: {
        supported: true,
        minimumAmount: 1,
        processingFee: 0.30,
        processingTime: 'Instant'
      }
    },
    isActive: true,
    supportedCountries: ['GB']
  },
  {
    code: 'KES',
    name: 'Kenyan Shilling',
    symbol: 'KSh',
    symbolPosition: 'before',
    decimalPlaces: 2,
    exchangeRates: [
      { toCurrency: 'USD', rate: 0.0091, provider: 'manual', isActive: true }
    ],
    taxConfiguration: {
      vatRate: 16,
      salesTaxRate: 0,
      withholdingTaxRate: 5,
      taxRegion: 'KE',
      taxInclusive: true
    },
    payoutSupport: {
      bankTransfer: {
        supported: true,
        minimumAmount: 100,
        processingFee: 50,
        processingTime: '1-3 business days'
      },
      mobileMoney: {
        supported: true,
        providers: ['mpesa', 'airtel_money'],
        minimumAmount: 10,
        processingFee: 0,
        processingTime: 'Instant'
      }
    },
    isActive: true,
    supportedCountries: ['KE']
  },
  {
    code: 'NGN',
    name: 'Nigerian Naira',
    symbol: '₦',
    symbolPosition: 'before',
    decimalPlaces: 2,
    exchangeRates: [
      { toCurrency: 'USD', rate: 0.0024, provider: 'manual', isActive: true }
    ],
    taxConfiguration: {
      vatRate: 7.5,
      salesTaxRate: 0,
      withholdingTaxRate: 10,
      taxRegion: 'NG',
      taxInclusive: false
    },
    payoutSupport: {
      bankTransfer: {
        supported: true,
        minimumAmount: 1000,
        processingFee: 100,
        processingTime: '1-3 business days'
      }
    },
    isActive: true,
    supportedCountries: ['NG']
  }
];

const seedCurrencies = async () => {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGO_URI || 'mongodb://localhost:27017/letstalk');
    console.log('Connected to MongoDB');

    // Clear existing currencies
    await Currency.deleteMany({});
    console.log('Cleared existing currencies');

    // Insert new currencies
    await Currency.insertMany(currencies);
    console.log('Seeded currencies successfully');

    process.exit(0);
  } catch (error) {
    console.error('Error seeding currencies:', error);
    process.exit(1);
  }
};

// Run if called directly
if (require.main === module) {
  seedCurrencies();
}

module.exports = seedCurrencies;
