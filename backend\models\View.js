const mongoose = require('mongoose');

const ViewSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
  },
  reel: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Reel',
  },
  post: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Post',
  },
  viewDuration: {
    type: Number, // in seconds
    default: 0,
  },
  completedView: {
    type: Boolean,
    default: false,
  },
  ipAddress: {
    type: String,
  },
  userAgent: {
    type: String,
  },
}, {
  timestamps: true,
});

// Custom validator to ensure either post or reel is provided
ViewSchema.pre('validate', function(next) {
  if (!this.post && !this.reel) {
    next(new Error('Either post or reel must be provided'));
  } else if (this.post && this.reel) {
    next(new Error('Only one of post or reel should be provided'));
  } else {
    next();
  }
});

// Compound index to track unique views
ViewSchema.index({ reel: 1, user: 1 }, { unique: true, sparse: true });
ViewSchema.index({ reel: 1, ipAddress: 1, userAgent: 1 }, { unique: true, sparse: true });
ViewSchema.index({ post: 1, user: 1 }, { unique: true, sparse: true });
ViewSchema.index({ post: 1, ipAddress: 1, userAgent: 1 }, { unique: true, sparse: true });

module.exports = mongoose.model('View', ViewSchema);
