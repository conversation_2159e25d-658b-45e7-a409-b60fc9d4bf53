const mongoose = require('mongoose');

const LoyaltyProgramSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Please add a program name'],
    trim: true,
    maxlength: [100, 'Name cannot be more than 100 characters']
  },
  description: {
    type: String,
    maxlength: [500, 'Description cannot be more than 500 characters']
  },
  type: {
    type: String,
    enum: ['points', 'tiers', 'cashback', 'referral'],
    required: true
  },
  isActive: {
    type: Boolean,
    default: true
  },
  pointsConfig: {
    earnRate: {
      type: Number,
      default: 1 // points per dollar spent
    },
    redemptionRate: {
      type: Number,
      default: 0.01 // dollar value per point
    },
    bonusEvents: [{
      event: {
        type: String,
        enum: ['signup', 'birthday', 'review', 'referral', 'social_share']
      },
      points: Number,
      description: String
    }],
    expirationDays: {
      type: Number,
      default: 365
    }
  },
  tiersConfig: {
    tiers: [{
      name: String,
      minSpent: Number,
      benefits: [{
        type: String,
        value: String,
        description: String
      }],
      pointsMultiplier: {
        type: Number,
        default: 1
      },
      color: String,
      icon: String
    }]
  },
  cashbackConfig: {
    rate: {
      type: Number,
      default: 0.05 // 5% cashback
    },
    minRedemption: {
      type: Number,
      default: 10
    },
    maxCashback: {
      type: Number,
      default: 1000
    }
  },
  referralConfig: {
    referrerReward: {
      type: String,
      enum: ['points', 'cashback', 'discount'],
      default: 'points'
    },
    referrerValue: Number,
    refereeReward: {
      type: String,
      enum: ['points', 'cashback', 'discount'],
      default: 'discount'
    },
    refereeValue: Number,
    maxReferrals: {
      type: Number,
      default: 100
    }
  },
  gamificationConfig: {
    badges: [{
      name: String,
      description: String,
      icon: String,
      criteria: {
        type: String,
        value: Number
      },
      reward: {
        type: String,
        value: Number
      }
    }],
    challenges: [{
      name: String,
      description: String,
      startDate: Date,
      endDate: Date,
      target: {
        type: String,
        value: Number
      },
      reward: {
        type: String,
        value: Number
      },
      participants: [{
        user: {
          type: mongoose.Schema.ObjectId,
          ref: 'User'
        },
        progress: Number,
        completed: Boolean,
        completedAt: Date
      }]
    }],
    leaderboard: {
      enabled: Boolean,
      period: {
        type: String,
        enum: ['weekly', 'monthly', 'yearly'],
        default: 'monthly'
      },
      rewards: [{
        position: Number,
        reward: {
          type: String,
          value: Number
        }
      }]
    }
  },
  creator: {
    type: mongoose.Schema.ObjectId,
    ref: 'User',
    required: true
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// User Loyalty Account Schema
const UserLoyaltySchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.ObjectId,
    ref: 'User',
    required: true,
    unique: true
  },
  program: {
    type: mongoose.Schema.ObjectId,
    ref: 'LoyaltyProgram',
    required: true
  },
  points: {
    available: {
      type: Number,
      default: 0
    },
    earned: {
      type: Number,
      default: 0
    },
    redeemed: {
      type: Number,
      default: 0
    },
    expired: {
      type: Number,
      default: 0
    }
  },
  cashback: {
    available: {
      type: Number,
      default: 0
    },
    earned: {
      type: Number,
      default: 0
    },
    redeemed: {
      type: Number,
      default: 0
    }
  },
  tier: {
    current: String,
    totalSpent: {
      type: Number,
      default: 0
    },
    nextTier: String,
    nextTierRequirement: Number
  },
  referrals: {
    count: {
      type: Number,
      default: 0
    },
    successful: {
      type: Number,
      default: 0
    },
    earnings: {
      type: Number,
      default: 0
    },
    referredUsers: [{
      user: {
        type: mongoose.Schema.ObjectId,
        ref: 'User'
      },
      status: {
        type: String,
        enum: ['pending', 'completed'],
        default: 'pending'
      },
      reward: Number,
      createdAt: {
        type: Date,
        default: Date.now
      }
    }]
  },
  badges: [{
    badge: String,
    earnedAt: {
      type: Date,
      default: Date.now
    },
    reward: {
      type: String,
      value: Number
    }
  }],
  transactions: [{
    type: {
      type: String,
      enum: ['earned', 'redeemed', 'expired', 'bonus', 'referral'],
      required: true
    },
    amount: Number,
    description: String,
    order: {
      type: mongoose.Schema.ObjectId,
      ref: 'Order'
    },
    expiresAt: Date,
    createdAt: {
      type: Date,
      default: Date.now
    }
  }],
  joinedAt: {
    type: Date,
    default: Date.now
  },
  lastActivity: {
    type: Date,
    default: Date.now
  }
});

// Update the updatedAt field before saving
LoyaltyProgramSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

// Instance method to calculate user tier
UserLoyaltySchema.methods.calculateTier = async function() {
  const program = await mongoose.model('LoyaltyProgram').findById(this.program);
  
  if (!program || !program.tiersConfig.tiers) {
    return this;
  }

  const tiers = program.tiersConfig.tiers.sort((a, b) => a.minSpent - b.minSpent);
  let currentTier = tiers[0];
  let nextTier = null;

  for (let i = 0; i < tiers.length; i++) {
    if (this.tier.totalSpent >= tiers[i].minSpent) {
      currentTier = tiers[i];
      nextTier = tiers[i + 1] || null;
    }
  }

  this.tier.current = currentTier.name;
  this.tier.nextTier = nextTier ? nextTier.name : null;
  this.tier.nextTierRequirement = nextTier ? nextTier.minSpent - this.tier.totalSpent : 0;

  return this;
};

// Instance method to add points
UserLoyaltySchema.methods.addPoints = function(amount, description, orderId = null) {
  this.points.available += amount;
  this.points.earned += amount;
  
  this.transactions.push({
    type: 'earned',
    amount,
    description,
    order: orderId,
    expiresAt: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000) // 1 year
  });

  this.lastActivity = new Date();
  return this.save();
};

// Instance method to redeem points
UserLoyaltySchema.methods.redeemPoints = function(amount, description) {
  if (this.points.available < amount) {
    throw new Error('Insufficient points');
  }

  this.points.available -= amount;
  this.points.redeemed += amount;
  
  this.transactions.push({
    type: 'redeemed',
    amount: -amount,
    description
  });

  this.lastActivity = new Date();
  return this.save();
};

// Instance method to add referral
UserLoyaltySchema.methods.addReferral = function(referredUserId, reward) {
  this.referrals.count += 1;
  this.referrals.referredUsers.push({
    user: referredUserId,
    reward
  });

  return this.save();
};

// Static method to get leaderboard
UserLoyaltySchema.statics.getLeaderboard = function(programId, period = 'monthly', limit = 10) {
  const startDate = new Date();
  
  switch (period) {
    case 'weekly':
      startDate.setDate(startDate.getDate() - 7);
      break;
    case 'yearly':
      startDate.setFullYear(startDate.getFullYear() - 1);
      break;
    default: // monthly
      startDate.setMonth(startDate.getMonth() - 1);
  }

  return this.aggregate([
    {
      $match: {
        program: mongoose.Types.ObjectId(programId),
        'transactions.createdAt': { $gte: startDate }
      }
    },
    {
      $unwind: '$transactions'
    },
    {
      $match: {
        'transactions.createdAt': { $gte: startDate },
        'transactions.type': 'earned'
      }
    },
    {
      $group: {
        _id: '$user',
        totalPoints: { $sum: '$transactions.amount' },
        totalSpent: { $first: '$tier.totalSpent' },
        currentTier: { $first: '$tier.current' }
      }
    },
    {
      $sort: { totalPoints: -1 }
    },
    {
      $limit: limit
    },
    {
      $lookup: {
        from: 'users',
        localField: '_id',
        foreignField: '_id',
        as: 'userInfo'
      }
    },
    {
      $unwind: '$userInfo'
    },
    {
      $project: {
        user: {
          _id: '$userInfo._id',
          name: '$userInfo.name',
          username: '$userInfo.username',
          profilePicture: '$userInfo.profilePicture'
        },
        totalPoints: 1,
        totalSpent: 1,
        currentTier: 1
      }
    }
  ]);
};

// Create indexes for better performance
LoyaltyProgramSchema.index({ creator: 1, isActive: 1 });
LoyaltyProgramSchema.index({ type: 1, isActive: 1 });

UserLoyaltySchema.index({ user: 1 }, { unique: true });
UserLoyaltySchema.index({ program: 1 });
UserLoyaltySchema.index({ 'tier.totalSpent': -1 });
UserLoyaltySchema.index({ 'points.available': -1 });
UserLoyaltySchema.index({ lastActivity: -1 });

const LoyaltyProgram = mongoose.model('LoyaltyProgram', LoyaltyProgramSchema);
const UserLoyalty = mongoose.model('UserLoyalty', UserLoyaltySchema);

module.exports = { LoyaltyProgram, UserLoyalty };
