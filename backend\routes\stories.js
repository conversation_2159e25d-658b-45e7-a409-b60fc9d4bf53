const express = require('express');
const router = express.Router();
const { protect } = require('../middleware/auth');
const {
  getStories,
  getStory,
  createStory,
  deleteStory,
  getUserStories,
  getFollowingStories,
  viewStory,
  getStoryViewers,
  getStoriesFeed,
} = require('../controllers/storyController');

// Protected routes
router.use(protect);
router.get('/', getStories);
router.get('/feed', getStoriesFeed);
router.get('/following', getFollowingStories);
router.get('/:id', getStory);
router.post('/', createStory);
router.delete('/:id', deleteStory);
router.get('/user/:userId', getUserStories);
router.post('/:id/view', viewStory);
router.get('/:id/viewers', getStoryViewers);

module.exports = router;
