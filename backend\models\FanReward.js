const mongoose = require('mongoose');

const FanRewardSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  points: {
    type: Number,
    default: 0,
  },
  level: {
    type: Number,
    default: 1,
  },
  streamer: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  badges: [{
    name: {
      type: String,
      required: true,
    },
    description: {
      type: String,
    },
    imageUrl: {
      type: String,
    },
    earnedAt: {
      type: Date,
      default: Date.now,
    },
    category: {
      type: String,
      enum: ['attendance', 'engagement', 'support', 'achievement', 'special'],
      default: 'engagement',
    },
    tier: {
      type: Number,
      default: 1,
      min: 1,
      max: 5,
    },
    isActive: {
      type: Boolean,
      default: true,
    },
  }],
  rewards: [{
    name: {
      type: String,
      required: true,
    },
    description: {
      type: String,
    },
    imageUrl: {
      type: String,
    },
    pointCost: {
      type: Number,
      required: true,
    },
    type: {
      type: String,
      enum: ['emote', 'badge', 'effect', 'access', 'discount', 'physical', 'custom'],
      default: 'custom',
    },
    redeemedAt: {
      type: Date,
      default: Date.now,
    },
    expiresAt: {
      type: Date,
    },
    isRedeemed: {
      type: Boolean,
      default: false,
    },
    redemptionCode: {
      type: String,
    },
  }],
  pointsHistory: [{
    amount: {
      type: Number,
      required: true,
    },
    reason: {
      type: String,
      required: true,
    },
    source: {
      type: String,
      enum: ['attendance', 'chat', 'gift', 'subscription', 'follow', 'share', 'reaction', 'redemption', 'bonus', 'other'],
      default: 'other',
    },
    timestamp: {
      type: Date,
      default: Date.now,
    },
    relatedStream: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'LiveStream',
    },
  }],
  streakData: {
    currentStreak: {
      type: Number,
      default: 0,
    },
    longestStreak: {
      type: Number,
      default: 0,
    },
    lastAttendance: {
      type: Date,
    },
  },
  settings: {
    notifyOnPointsEarned: {
      type: Boolean,
      default: true,
    },
    notifyOnLevelUp: {
      type: Boolean,
      default: true,
    },
    notifyOnRewardAvailable: {
      type: Boolean,
      default: true,
    },
    displayBadgesOnProfile: {
      type: Boolean,
      default: true,
    },
    displayLevelOnProfile: {
      type: Boolean,
      default: true,
    },
  }
}, { timestamps: true });

// Create indexes for efficient queries
FanRewardSchema.index({ user: 1, streamer: 1 }, { unique: true });
FanRewardSchema.index({ streamer: 1, points: -1 });
FanRewardSchema.index({ streamer: 1, level: -1 });

module.exports = mongoose.model('FanReward', FanRewardSchema);
