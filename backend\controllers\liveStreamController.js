const LiveStream = require('../models/LiveStream');
const LiveStreamChat = require('../models/LiveStreamChat');
const LiveStreamViewer = require('../models/LiveStreamViewer');
const LiveStreamReaction = require('../models/LiveStreamReaction');
const User = require('../models/User');
const Follow = require('../models/Follow');
const asyncHandler = require('../middleware/async');
const ErrorResponse = require('../utils/errorResponse');
const socketEmitter = require('../utils/socketEmitter');
const { cloudinary, uploadToCloudinary } = require('../config/cloudinary');
const crypto = require('crypto');

/**
 * Get all live streams
 * @route GET /api/live-streams
 * @access Public
 */
exports.getLiveStreams = asyncHandler(async (req, res, next) => {
  const { page = 1, limit = 10, status = 'live', category, search, upcoming } = req.query;
  const skip = (page - 1) * limit;

  // Build query
  let query = {};

  // Handle status filtering
  if (status === 'scheduled') {
    query.status = 'scheduled';
    // Only show future scheduled streams by default
    if (upcoming !== 'false') {
      query.scheduledFor = { $gt: new Date() };
    }
  } else if (status === 'ended') {
    query.status = 'ended';
  } else if (status === 'all') {
    // No status filter
  } else {
    query.status = 'live';
  }

  // Add category filter if provided
  if (category) {
    query.category = category;
  }

  // Add search filter if provided
  if (search) {
    query.$or = [
      { title: { $regex: search, $options: 'i' } },
      { description: { $regex: search, $options: 'i' } },
      { tags: { $regex: search, $options: 'i' } },
    ];
  }

  // If user is authenticated, exclude private streams they're not allowed to view
  if (req.user) {
    query.$or = [
      { isPrivate: false },
      { isPrivate: true, allowedViewers: req.user._id },
      { isPrivate: true, user: req.user._id },
    ];
  } else {
    query.isPrivate = false;
  }

  // Execute query
  const streams = await LiveStream.find(query)
    .sort(query.status === 'scheduled' ? { scheduledFor: 1 } : { createdAt: -1 })
    .skip(skip)
    .limit(parseInt(limit))
    .populate('user', 'username name profilePicture isVerified')
    .populate('emotions.emotion', 'name color icon category');

  // Get total count
  const total = await LiveStream.countDocuments(query);

  res.status(200).json({
    success: true,
    count: streams.length,
    total,
    pagination: {
      page: parseInt(page),
      limit: parseInt(limit),
      totalPages: Math.ceil(total / limit),
    },
    data: streams,
  });
});

/**
 * Get a single live stream
 * @route GET /api/live-streams/:id
 * @access Public
 */
exports.getLiveStream = asyncHandler(async (req, res, next) => {
  const stream = await LiveStream.findById(req.params.id)
    .populate('user', 'username name profilePicture isVerified')
    .populate('emotions.emotion', 'name color icon category')
    .populate('arEffects.effect', 'name description type url');

  if (!stream) {
    return next(new ErrorResponse(`Live stream not found with id of ${req.params.id}`, 404));
  }

  // Check if stream is private and user is allowed to view
  if (stream.isPrivate) {
    if (!req.user) {
      return next(new ErrorResponse('Not authorized to view this stream', 401));
    }

    const isAllowed = stream.user._id.toString() === req.user._id.toString() ||
                      stream.allowedViewers.includes(req.user._id);

    if (!isAllowed) {
      return next(new ErrorResponse('Not authorized to view this stream', 403));
    }
  }

  // Get active viewers count
  const activeViewers = await LiveStreamViewer.countDocuments({
    stream: stream._id,
    isActive: true,
  });

  // Add active viewers count to response
  const streamWithViewers = {
    ...stream.toObject(),
    activeViewers,
  };

  res.status(200).json({
    success: true,
    data: streamWithViewers,
  });
});

/**
 * Create a new live stream
 * @route POST /api/live-streams
 * @access Private
 */
exports.createLiveStream = asyncHandler(async (req, res, next) => {
  // Add user to request body
  req.body.user = req.user.id;

  // Generate a unique stream key
  const streamKey = crypto.randomBytes(16).toString('hex');
  req.body.streamKey = streamKey;

  // Upload thumbnail if provided
  if (req.files && req.files.thumbnail) {
    const file = req.files.thumbnail;

    // Upload to Cloudinary
    const result = await cloudinary.uploader.upload(file.tempFilePath, {
      folder: 'letstalk/livestreams/thumbnails',
      resource_type: 'image',
    });

    // Add thumbnail to request body
    req.body.thumbnail = {
      url: result.secure_url,
      publicId: result.public_id,
    };
  }

  // Create stream
  const stream = await LiveStream.create(req.body);

  // Populate user and emotions
  await stream.populate('user', 'username name profilePicture isVerified');
  await stream.populate('emotions.emotion', 'name color icon category');

  // Emit socket event for new stream
  socketEmitter.emitNewLiveStream(stream);

  // If this is a scheduled stream, notify followers
  if (stream.status === 'scheduled' && stream.scheduledFor) {
    // Get user's followers
    const Follow = require('../models/Follow');
    const followers = await Follow.find({ following: req.user.id });
    const followerIds = followers.map(follow => follow.follower.toString());

    if (followerIds.length > 0) {
      // Get notification preferences for each follower
      const NotificationPreference = require('../models/NotificationPreference');
      const preferences = await NotificationPreference.find({
        user: { $in: followerIds }
      });

      // Filter followers based on notification preferences
      const notifyFollowerIds = followerIds.filter(followerId => {
        const preference = preferences.find(p => p.user.toString() === followerId);
        return !preference || preference.liveStream.scheduledStreamStart;
      });

      if (notifyFollowerIds.length > 0) {
        // Send notifications
        socketEmitter.emitStreamScheduledNotification(
          stream._id.toString(),
          {
            _id: stream._id,
            title: stream.title,
            thumbnail: stream.thumbnail,
            scheduledFor: stream.scheduledFor,
            user: {
              _id: stream.user._id,
              name: stream.user.name,
              username: stream.user.username,
              profilePicture: stream.user.profilePicture
            }
          },
          notifyFollowerIds
        );
      }
    }
  }

  res.status(201).json({
    success: true,
    data: stream,
  });
});

/**
 * Update a live stream
 * @route PUT /api/live-streams/:id
 * @access Private
 */
exports.updateLiveStream = asyncHandler(async (req, res, next) => {
  let stream = await LiveStream.findById(req.params.id);

  if (!stream) {
    return next(new ErrorResponse(`Live stream not found with id of ${req.params.id}`, 404));
  }

  // Make sure user is the stream owner
  if (stream.user.toString() !== req.user.id) {
    return next(new ErrorResponse('Not authorized to update this stream', 403));
  }

  // Upload new thumbnail if provided
  if (req.files && req.files.thumbnail) {
    const file = req.files.thumbnail;

    // Delete old thumbnail from Cloudinary if exists
    if (stream.thumbnail && stream.thumbnail.publicId) {
      await cloudinary.uploader.destroy(stream.thumbnail.publicId);
    }

    // Upload to Cloudinary
    const result = await cloudinary.uploader.upload(file.tempFilePath, {
      folder: 'letstalk/livestreams/thumbnails',
      resource_type: 'image',
    });

    // Add thumbnail to request body
    req.body.thumbnail = {
      url: result.secure_url,
      publicId: result.public_id,
    };
  }

  // Update stream
  stream = await LiveStream.findByIdAndUpdate(req.params.id, req.body, {
    new: true,
    runValidators: true,
  });

  // Populate user and emotions
  await stream.populate('user', 'username name profilePicture isVerified');
  await stream.populate('emotions.emotion', 'name color icon category');

  // Emit socket event for updated stream
  socketEmitter.emitLiveStreamUpdated(stream);

  res.status(200).json({
    success: true,
    data: stream,
  });
});

/**
 * Delete a live stream
 * @route DELETE /api/live-streams/:id
 * @access Private
 */
exports.deleteLiveStream = asyncHandler(async (req, res, next) => {
  const stream = await LiveStream.findById(req.params.id);

  if (!stream) {
    return next(new ErrorResponse(`Live stream not found with id of ${req.params.id}`, 404));
  }

  // Make sure user is the stream owner
  if (stream.user.toString() !== req.user.id) {
    return next(new ErrorResponse('Not authorized to delete this stream', 403));
  }

  // Delete thumbnail from Cloudinary if exists
  if (stream.thumbnail && stream.thumbnail.publicId) {
    await cloudinary.uploader.destroy(stream.thumbnail.publicId);
  }

  // Delete stream
  await stream.remove();

  // Emit socket event for deleted stream
  socketEmitter.emitLiveStreamDeleted(stream._id);

  res.status(200).json({
    success: true,
    data: {},
  });
});

/**
 * Start a live stream
 * @route PUT /api/live-streams/:id/start
 * @access Private
 */
exports.startLiveStream = asyncHandler(async (req, res, next) => {
  let stream = await LiveStream.findById(req.params.id);

  if (!stream) {
    return next(new ErrorResponse(`Live stream not found with id of ${req.params.id}`, 404));
  }

  // Make sure user is the stream owner
  if (stream.user.toString() !== req.user.id) {
    return next(new ErrorResponse('Not authorized to start this stream', 403));
  }

  // Update stream status and start time
  stream = await LiveStream.findByIdAndUpdate(
    req.params.id,
    {
      status: 'live',
      startedAt: Date.now(),
    },
    {
      new: true,
      runValidators: true,
    }
  );

  // Populate user and emotions
  await stream.populate('user', 'username name profilePicture isVerified');
  await stream.populate('emotions.emotion', 'name color icon category');

  // Emit socket event for stream started
  socketEmitter.emitLiveStreamStarted(stream);

  // Notify followers about stream start
  const Follow = require('../models/Follow');
  const followers = await Follow.find({ following: req.user.id });
  const followerIds = followers.map(follow => follow.follower.toString());

  if (followerIds.length > 0) {
    // Get notification preferences for each follower
    const NotificationPreference = require('../models/NotificationPreference');
    const preferences = await NotificationPreference.find({
      user: { $in: followerIds }
    });

    // Filter followers based on notification preferences
    const notifyFollowerIds = followerIds.filter(followerId => {
      const preference = preferences.find(p => p.user.toString() === followerId);
      return !preference || preference.liveStream.followedCreatorStart;
    });

    if (notifyFollowerIds.length > 0) {
      // Send notifications
      socketEmitter.emitStreamStartNotification(
        stream._id.toString(),
        {
          _id: stream._id,
          title: stream.title,
          thumbnail: stream.thumbnail,
          user: {
            _id: stream.user._id,
            name: stream.user.name,
            username: stream.user.username,
            profilePicture: stream.user.profilePicture
          }
        },
        notifyFollowerIds
      );
    }
  }

  // Send reminders to users who have set reminders for this stream
  if (stream.status === 'scheduled' && stream.scheduledFor) {
    const StreamReminder = require('../models/StreamReminder');
    const reminders = await StreamReminder.find({
      stream: stream._id,
      notified: false
    }).populate('user', 'name');

    // Mark reminders as notified and send notifications
    for (const reminder of reminders) {
      reminder.notified = true;
      await reminder.save();

      socketEmitter.emitStreamReminderNotification(
        reminder.user._id.toString(),
        {
          _id: reminder._id,
          stream: {
            _id: stream._id,
            title: stream.title,
            thumbnail: stream.thumbnail,
            user: {
              _id: stream.user._id,
              name: stream.user.name,
              username: stream.user.username,
              profilePicture: stream.user.profilePicture
            }
          }
        }
      );
    }
  }

  res.status(200).json({
    success: true,
    data: stream,
  });
});

/**
 * Save user viewing preferences
 * @route PUT /api/live-streams/viewing-preferences
 * @access Private
 */
exports.saveViewingPreferences = asyncHandler(async (req, res, next) => {
  const { defaultViewingMode, audioOnlyQuality, backgroundModeEnabled, miniPlayerPosition, creatorModeSettings } = req.body;

  // Get user
  const user = await User.findById(req.user.id);

  if (!user) {
    return next(new ErrorResponse('User not found', 404));
  }

  // Update user's viewing preferences
  user.liveStreamPreferences = {
    ...user.liveStreamPreferences || {},
    defaultViewingMode: defaultViewingMode || user.liveStreamPreferences?.defaultViewingMode || 'default',
    audioOnlyQuality: audioOnlyQuality || user.liveStreamPreferences?.audioOnlyQuality || 'medium',
    backgroundModeEnabled: backgroundModeEnabled !== undefined ? backgroundModeEnabled : (user.liveStreamPreferences?.backgroundModeEnabled !== undefined ? user.liveStreamPreferences.backgroundModeEnabled : true),
    miniPlayerPosition: miniPlayerPosition || user.liveStreamPreferences?.miniPlayerPosition || { x: 20, y: 20 },
    creatorModeSettings: creatorModeSettings || user.liveStreamPreferences?.creatorModeSettings || {}
  };

  await user.save();

  res.status(200).json({
    success: true,
    data: user.liveStreamPreferences
  });
});

/**
 * Get user viewing preferences
 * @route GET /api/live-streams/viewing-preferences
 * @access Private
 */
exports.getViewingPreferences = asyncHandler(async (req, res, next) => {
  // Get user
  const user = await User.findById(req.user.id);

  if (!user) {
    return next(new ErrorResponse('User not found', 404));
  }

  // Return user's viewing preferences or default values
  const preferences = user.liveStreamPreferences || {
    defaultViewingMode: 'default',
    audioOnlyQuality: 'medium',
    backgroundModeEnabled: true,
    miniPlayerPosition: { x: 20, y: 20 },
    creatorModeSettings: {}
  };

  res.status(200).json({
    success: true,
    data: preferences
  });
});

/**
 * End a live stream
 * @route PUT /api/live-streams/:id/end
 * @access Private
 */
exports.endLiveStream = asyncHandler(async (req, res, next) => {
  let stream = await LiveStream.findById(req.params.id);

  if (!stream) {
    return next(new ErrorResponse(`Live stream not found with id of ${req.params.id}`, 404));
  }

  // Make sure user is the stream owner
  if (stream.user.toString() !== req.user.id) {
    return next(new ErrorResponse('Not authorized to end this stream', 403));
  }

  // Update stream status and end time
  stream = await LiveStream.findByIdAndUpdate(
    req.params.id,
    {
      status: 'ended',
      endedAt: Date.now(),
    },
    {
      new: true,
      runValidators: true,
    }
  );

  // Populate user and emotions
  await stream.populate('user', 'username name profilePicture isVerified');
  await stream.populate('emotions.emotion', 'name color icon category');

  // Emit socket event for stream ended
  socketEmitter.emitLiveStreamEnded(stream);

  // Mark all viewers as inactive
  await LiveStreamViewer.updateMany(
    { stream: stream._id, isActive: true },
    {
      isActive: false,
      leftAt: Date.now(),
      $expr: {
        $set: {
          duration: {
            $divide: [{ $subtract: [Date.now(), '$joinedAt'] }, 1000],
          },
        },
      },
    }
  );

  res.status(200).json({
    success: true,
    data: stream,
  });
});

/**
 * Get all hosts for a live stream
 * @route GET /api/live-streams/:id/hosts
 * @access Private
 */
exports.getLiveStreamHosts = asyncHandler(async (req, res, next) => {
  const stream = await LiveStream.findById(req.params.id)
    .populate('user', 'username name profilePicture isVerified')
    .populate('coHosts', 'username name profilePicture isVerified');

  if (!stream) {
    return next(new ErrorResponse(`Live stream not found with id of ${req.params.id}`, 404));
  }

  // Combine main host and co-hosts
  const mainHost = {
    _id: stream.user._id,
    name: stream.user.name,
    username: stream.user.username,
    profilePicture: stream.user.profilePicture,
    isVerified: stream.user.isVerified,
    isMainHost: true,
    isMuted: false,
    isVideoOff: false,
    isScreenSharing: false
  };

  const coHosts = stream.coHosts.map(coHost => ({
    _id: coHost._id,
    name: coHost.name,
    username: coHost.username,
    profilePicture: coHost.profilePicture,
    isVerified: coHost.isVerified,
    isMainHost: false,
    isMuted: false,
    isVideoOff: false,
    isScreenSharing: false
  }));

  const hosts = [mainHost, ...coHosts];

  res.status(200).json({
    success: true,
    count: hosts.length,
    data: hosts
  });
});

/**
 * Add a co-host to a live stream
 * @route POST /api/live-streams/:id/hosts
 * @access Private
 */
exports.addCoHost = asyncHandler(async (req, res, next) => {
  const { userId } = req.body;

  if (!userId) {
    return next(new ErrorResponse('Please provide a user ID', 400));
  }

  let stream = await LiveStream.findById(req.params.id);

  if (!stream) {
    return next(new ErrorResponse(`Live stream not found with id of ${req.params.id}`, 404));
  }

  // Make sure user is the stream owner
  if (stream.user.toString() !== req.user.id) {
    return next(new ErrorResponse('Not authorized to add co-hosts to this stream', 403));
  }

  // Check if user exists
  const user = await User.findById(userId);

  if (!user) {
    return next(new ErrorResponse(`User not found with id of ${userId}`, 404));
  }

  // Check if user is already a co-host
  if (stream.coHosts.includes(userId)) {
    return next(new ErrorResponse('User is already a co-host', 400));
  }

  // Add user to co-hosts
  stream = await LiveStream.findByIdAndUpdate(
    req.params.id,
    { $addToSet: { coHosts: userId } },
    { new: true }
  )
    .populate('user', 'username name profilePicture isVerified')
    .populate('coHosts', 'username name profilePicture isVerified');

  res.status(200).json({
    success: true,
    data: stream
  });
});

/**
 * Remove a co-host from a live stream
 * @route DELETE /api/live-streams/:id/hosts/:userId
 * @access Private
 */
exports.removeCoHost = asyncHandler(async (req, res, next) => {
  let stream = await LiveStream.findById(req.params.id);

  if (!stream) {
    return next(new ErrorResponse(`Live stream not found with id of ${req.params.id}`, 404));
  }

  // Make sure user is the stream owner
  if (stream.user.toString() !== req.user.id) {
    return next(new ErrorResponse('Not authorized to remove co-hosts from this stream', 403));
  }

  // Remove user from co-hosts
  stream = await LiveStream.findByIdAndUpdate(
    req.params.id,
    { $pull: { coHosts: req.params.userId } },
    { new: true }
  )
    .populate('user', 'username name profilePicture isVerified')
    .populate('coHosts', 'username name profilePicture isVerified');

  res.status(200).json({
    success: true,
    data: stream
  });
});

/**
 * Invite a user to be a co-host
 * @route POST /api/live-streams/:id/invite
 * @access Private
 */
exports.inviteCoHost = asyncHandler(async (req, res, next) => {
  const { email, message } = req.body;

  if (!email) {
    return next(new ErrorResponse('Please provide an email', 400));
  }

  const stream = await LiveStream.findById(req.params.id);

  if (!stream) {
    return next(new ErrorResponse(`Live stream not found with id of ${req.params.id}`, 404));
  }

  // Make sure user is the stream owner or co-host if allowed
  const isOwner = stream.user.toString() === req.user.id;
  const isCoHost = stream.coHosts.some(id => id.toString() === req.user.id);

  if (!isOwner && !(isCoHost && stream.settings.multiHost.allowCoHostInvites)) {
    return next(new ErrorResponse('Not authorized to invite co-hosts to this stream', 403));
  }

  // Check if multi-host is enabled
  if (!stream.settings.multiHost.enabled) {
    return next(new ErrorResponse('Multi-host feature is not enabled for this stream', 400));
  }

  // Check if max hosts limit is reached
  const currentHostsCount = 1 + stream.coHosts.length; // Main host + co-hosts
  if (currentHostsCount >= stream.settings.multiHost.maxHosts) {
    return next(new ErrorResponse(`Maximum number of hosts (${stream.settings.multiHost.maxHosts}) reached`, 400));
  }

  // Find user by email
  const user = await User.findOne({ email });

  if (!user) {
    return next(new ErrorResponse(`User not found with email ${email}`, 404));
  }

  // Check if user is already a co-host
  if (stream.coHosts.includes(user._id)) {
    return next(new ErrorResponse('User is already a co-host', 400));
  }

  // Check if there's already a pending invitation
  const CoHostInvitation = require('../models/CoHostInvitation');
  const existingInvitation = await CoHostInvitation.findOne({
    stream: stream._id,
    invitee: user._id,
    status: 'pending'
  });

  if (existingInvitation) {
    return next(new ErrorResponse('An invitation has already been sent to this user', 400));
  }

  // Create invitation
  const invitation = await CoHostInvitation.create({
    stream: stream._id,
    inviter: req.user.id,
    invitee: user._id,
    message: message || `${req.user.name} has invited you to co-host their live stream.`,
    expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours from now
  });

  // If auto-accept is enabled, automatically add user as co-host
  if (stream.settings.multiHost.autoAcceptInvites) {
    // Update invitation status
    invitation.status = 'accepted';
    invitation.respondedAt = Date.now();
    await invitation.save();

    // Add user to co-hosts
    await LiveStream.findByIdAndUpdate(
      req.params.id,
      { $addToSet: { coHosts: user._id } },
      { new: true }
    );

    // Send notification to user
    // TODO: Implement notification system
  } else {
    // Send notification to user about the invitation
    // TODO: Implement notification system
  }

  res.status(200).json({
    success: true,
    data: invitation,
    message: `Invitation sent to ${email}`
  });
});

/**
 * Get all co-host invitations for a user
 * @route GET /api/live-streams/invitations
 * @access Private
 */
exports.getCoHostInvitations = asyncHandler(async (req, res, next) => {
  const CoHostInvitation = require('../models/CoHostInvitation');

  // Get all pending invitations for the user
  const invitations = await CoHostInvitation.find({
    invitee: req.user.id,
    status: 'pending'
  })
    .populate('stream', 'title thumbnail status')
    .populate('inviter', 'name username profilePicture')
    .sort('-createdAt');

  res.status(200).json({
    success: true,
    count: invitations.length,
    data: invitations
  });
});

/**
 * Respond to a co-host invitation
 * @route PUT /api/live-streams/invitations/:id
 * @access Private
 */
exports.respondToInvitation = asyncHandler(async (req, res, next) => {
  const { accept } = req.body;

  if (accept === undefined) {
    return next(new ErrorResponse('Please specify whether to accept or decline the invitation', 400));
  }

  const CoHostInvitation = require('../models/CoHostInvitation');

  // Find the invitation
  const invitation = await CoHostInvitation.findById(req.params.id);

  if (!invitation) {
    return next(new ErrorResponse(`Invitation not found with id of ${req.params.id}`, 404));
  }

  // Check if the invitation is for the current user
  if (invitation.invitee.toString() !== req.user.id) {
    return next(new ErrorResponse('Not authorized to respond to this invitation', 403));
  }

  // Check if the invitation is still pending
  if (invitation.status !== 'pending') {
    return next(new ErrorResponse(`Invitation has already been ${invitation.status}`, 400));
  }

  // Check if the invitation has expired
  if (invitation.expiresAt < Date.now()) {
    invitation.status = 'expired';
    await invitation.save();
    return next(new ErrorResponse('Invitation has expired', 400));
  }

  // Update invitation status
  invitation.status = accept ? 'accepted' : 'declined';
  invitation.respondedAt = Date.now();
  await invitation.save();

  // If accepted, add user as co-host
  if (accept) {
    // Find the stream
    const stream = await LiveStream.findById(invitation.stream);

    if (!stream) {
      return next(new ErrorResponse('Stream no longer exists', 404));
    }

    // Check if multi-host is still enabled
    if (!stream.settings.multiHost.enabled) {
      return next(new ErrorResponse('Multi-host feature is no longer enabled for this stream', 400));
    }

    // Check if max hosts limit is reached
    const currentHostsCount = 1 + stream.coHosts.length; // Main host + co-hosts
    if (currentHostsCount >= stream.settings.multiHost.maxHosts) {
      return next(new ErrorResponse(`Maximum number of hosts (${stream.settings.multiHost.maxHosts}) reached`, 400));
    }

    // Add user to co-hosts
    await LiveStream.findByIdAndUpdate(
      invitation.stream,
      { $addToSet: { coHosts: req.user.id } },
      { new: true }
    );

    // Send notification to stream owner
    // TODO: Implement notification system
  }

  res.status(200).json({
    success: true,
    data: invitation,
    message: accept ? 'Invitation accepted' : 'Invitation declined'
  });
});

/**
 * Cancel a co-host invitation
 * @route DELETE /api/live-streams/invitations/:id
 * @access Private
 */
exports.cancelInvitation = asyncHandler(async (req, res, next) => {
  const CoHostInvitation = require('../models/CoHostInvitation');

  // Find the invitation
  const invitation = await CoHostInvitation.findById(req.params.id);

  if (!invitation) {
    return next(new ErrorResponse(`Invitation not found with id of ${req.params.id}`, 404));
  }

  // Check if the user is the inviter
  if (invitation.inviter.toString() !== req.user.id) {
    return next(new ErrorResponse('Not authorized to cancel this invitation', 403));
  }

  // Check if the invitation is still pending
  if (invitation.status !== 'pending') {
    return next(new ErrorResponse(`Invitation has already been ${invitation.status}`, 400));
  }

  // Delete the invitation
  await invitation.remove();

  res.status(200).json({
    success: true,
    data: {},
    message: 'Invitation cancelled'
  });
});