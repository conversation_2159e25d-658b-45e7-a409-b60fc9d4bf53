const mongoose = require('mongoose');

const SponsorSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Please add a sponsor name'],
    trim: true,
    maxlength: [100, 'Name cannot be more than 100 characters']
  },
  logo: {
    type: String,
    required: [true, 'Please add a logo URL']
  },
  url: {
    type: String,
    required: [true, 'Please add a website URL'],
    match: [
      /https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)/,
      'Please use a valid URL with HTTP or HTTPS'
    ]
  },
  message: {
    type: String,
    maxlength: [200, 'Message cannot be more than 200 characters']
  },
  stream: {
    type: mongoose.Schema.ObjectId,
    ref: 'LiveStream',
    required: true
  },
  user: {
    type: mongoose.Schema.ObjectId,
    ref: 'User',
    required: true
  },
  startDate: {
    type: Date,
    default: Date.now
  },
  endDate: {
    type: Date
  },
  isActive: {
    type: Boolean,
    default: true
  },
  createdAt: {
    type: Date,
    default: Date.now
  }
});

// Create index for faster queries
SponsorSchema.index({ stream: 1, isActive: 1 });
SponsorSchema.index({ user: 1 });

module.exports = mongoose.model('Sponsor', SponsorSchema);
