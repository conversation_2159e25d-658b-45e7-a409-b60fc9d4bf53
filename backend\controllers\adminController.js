const User = require('../models/User');
const Post = require('../models/Post');
const Report = require('../models/Report');
const Vendor = require('../models/Vendor');
const Order = require('../models/Order');
const Commission = require('../models/Commission');
const PayoutRequest = require('../models/PayoutRequest');
const Promotion = require('../models/Promotion');
const Product = require('../models/Product');
const Category = require('../models/Category');
const Cart = require('../models/Cart');
const Wishlist = require('../models/Wishlist');

// Import services with error handling
let socketService, notificationService;
try {
  socketService = require('../services/socketService');
  notificationService = require('../services/notificationService');
} catch (error) {
  console.warn('Socket/Notification services not available:', error.message);
  socketService = { emit: () => {}, broadcast: () => {} };
  notificationService = { sendNotification: () => Promise.resolve() };
}

// @desc    Get comprehensive admin dashboard stats
// @route   GET /api/admin/dashboard
// @access  Private/Admin
exports.getDashboardStats = async (req, res) => {
  try {
    // User Statistics
    const totalUsers = await User.countDocuments();
    const activeUsers = await User.countDocuments({ isActive: true });
    const bannedUsers = await User.countDocuments({ isBanned: true });
    const newUsersToday = await User.countDocuments({
      createdAt: { $gte: new Date(Date.now() - 24 * 60 * 60 * 1000) }
    });

    // Vendor Statistics
    const totalVendors = await Vendor.countDocuments();
    const activeVendors = await Vendor.countDocuments({ status: 'active' });
    const pendingVendors = await Vendor.countDocuments({ status: 'pending' });
    const bannedVendors = await Vendor.countDocuments({ status: 'banned' });

    // Order & Revenue Statistics
    const totalOrders = await Order.countDocuments();
    const todaysOrders = await Order.countDocuments({
      createdAt: { $gte: new Date(Date.now() - 24 * 60 * 60 * 1000) }
    });

    const revenueStats = await Order.aggregate([
      { $match: { paymentStatus: 'paid' } },
      {
        $group: {
          _id: null,
          totalRevenue: { $sum: '$total' },
          averageOrderValue: { $avg: '$total' }
        }
      }
    ]);

    // Commission Statistics
    const commissionStats = await Commission.aggregate([
      {
        $group: {
          _id: null,
          totalCommissions: { $sum: '$amounts.platformCommission' },
          totalVendorEarnings: { $sum: '$amounts.vendorEarnings' },
          pendingPayouts: { $sum: { $cond: [{ $eq: ['$payoutStatus', 'pending'] }, '$amounts.vendorEarnings', 0] } }
        }
      }
    ]);

    // Content Statistics
    const totalPosts = await Post.countDocuments();
    const totalReports = await Report.countDocuments({ status: 'pending' });

    // Product Statistics
    const totalProducts = await Product.countDocuments();
    const activeProducts = await Product.countDocuments({ status: 'active' });
    const pendingProducts = await Product.countDocuments({ status: 'pending' });
    const outOfStockProducts = await Product.countDocuments({ stock: 0 });
    const totalCategories = await Category.countDocuments();

    // Promotion Statistics
    const activePromotions = await Promotion.countDocuments({
      status: 'active',
      startDate: { $lte: new Date() },
      endDate: { $gte: new Date() }
    });

    const stats = {
      users: {
        total: totalUsers,
        active: activeUsers,
        banned: bannedUsers,
        newToday: newUsersToday
      },
      vendors: {
        total: totalVendors,
        active: activeVendors,
        pending: pendingVendors,
        banned: bannedVendors
      },
      orders: {
        total: totalOrders,
        today: todaysOrders
      },
      revenue: {
        total: revenueStats[0]?.totalRevenue || 0,
        averageOrderValue: revenueStats[0]?.averageOrderValue || 0,
        totalCommissions: commissionStats[0]?.totalCommissions || 0,
        totalVendorEarnings: commissionStats[0]?.totalVendorEarnings || 0,
        pendingPayouts: commissionStats[0]?.pendingPayouts || 0
      },
      content: {
        totalPosts,
        pendingReports: totalReports
      },
      products: {
        total: totalProducts,
        active: activeProducts,
        pending: pendingProducts,
        outOfStock: outOfStockProducts,
        categories: totalCategories
      },
      promotions: {
        active: activePromotions
      }
    };

    res.status(200).json({
      success: true,
      data: stats
    });
  } catch (error) {
    console.error('Error getting dashboard stats:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Get all vendors with filtering and pagination
// @route   GET /api/admin/vendors
// @access  Private/Admin
exports.getVendors = async (req, res) => {
  try {
    const { page = 1, limit = 20, status, search, sortBy = 'createdAt', sortOrder = 'desc' } = req.query;

    // Build query
    let query = {};
    if (status) query.status = status;
    if (search) {
      query.$or = [
        { businessName: { $regex: search, $options: 'i' } },
        { 'contactInfo.email': { $regex: search, $options: 'i' } }
      ];
    }

    // Build sort object
    const sort = {};
    sort[sortBy] = sortOrder === 'desc' ? -1 : 1;

    // Execute query with pagination
    const vendors = await Vendor.find(query)
      .populate('user', 'name email profilePicture')
      .sort(sort)
      .limit(limit * 1)
      .skip((page - 1) * limit);

    const total = await Vendor.countDocuments(query);

    res.status(200).json({
      success: true,
      data: vendors,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error getting vendors:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Approve vendor
// @route   PUT /api/admin/vendors/:id/approve
// @access  Private/Admin
exports.approveVendor = async (req, res) => {
  try {
    const vendor = await Vendor.findById(req.params.id).populate('user');

    if (!vendor) {
      return res.status(404).json({
        success: false,
        message: 'Vendor not found'
      });
    }

    vendor.status = 'active';
    vendor.approvedAt = new Date();
    vendor.approvedBy = req.user.id;
    await vendor.save();

    // Send notification to vendor
    await notificationService.sendNotification(vendor.user._id, {
      type: 'vendor_approved',
      title: 'Vendor Application Approved',
      message: 'Congratulations! Your vendor application has been approved.',
      data: { vendorId: vendor._id }
    });

    // Broadcast real-time update
    socketService.emit(`user_${vendor.user._id}`, 'vendor_approved', {
      vendorId: vendor._id,
      status: 'active'
    });

    res.status(200).json({
      success: true,
      data: vendor,
      message: 'Vendor approved successfully'
    });
  } catch (error) {
    console.error('Error approving vendor:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Ban vendor
// @route   PUT /api/admin/vendors/:id/ban
// @access  Private/Admin
exports.banVendor = async (req, res) => {
  try {
    const { reason } = req.body;
    const vendor = await Vendor.findById(req.params.id).populate('user');

    if (!vendor) {
      return res.status(404).json({
        success: false,
        message: 'Vendor not found'
      });
    }

    vendor.status = 'banned';
    vendor.bannedAt = new Date();
    vendor.bannedBy = req.user.id;
    vendor.banReason = reason;
    await vendor.save();

    // Send notification to vendor
    await notificationService.sendNotification(vendor.user._id, {
      type: 'vendor_banned',
      title: 'Vendor Account Suspended',
      message: `Your vendor account has been suspended. Reason: ${reason}`,
      data: { vendorId: vendor._id, reason }
    });

    // Broadcast real-time update
    socketService.emit(`user_${vendor.user._id}`, 'vendor_banned', {
      vendorId: vendor._id,
      status: 'banned',
      reason
    });

    res.status(200).json({
      success: true,
      data: vendor,
      message: 'Vendor banned successfully'
    });
  } catch (error) {
    console.error('Error banning vendor:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Get revenue analytics
// @route   GET /api/admin/analytics/revenue
// @access  Private/Admin
exports.getRevenueAnalytics = async (req, res) => {
  try {
    const { startDate, endDate, period = 'daily' } = req.query;

    let matchStage = {};
    if (startDate && endDate) {
      matchStage.createdAt = {
        $gte: new Date(startDate),
        $lte: new Date(endDate)
      };
    }

    // Group by period
    let groupStage;
    switch (period) {
      case 'hourly':
        groupStage = {
          $group: {
            _id: {
              year: { $year: '$createdAt' },
              month: { $month: '$createdAt' },
              day: { $dayOfMonth: '$createdAt' },
              hour: { $hour: '$createdAt' }
            },
            revenue: { $sum: '$total' },
            orders: { $sum: 1 },
            averageOrderValue: { $avg: '$total' }
          }
        };
        break;
      case 'weekly':
        groupStage = {
          $group: {
            _id: {
              year: { $year: '$createdAt' },
              week: { $week: '$createdAt' }
            },
            revenue: { $sum: '$total' },
            orders: { $sum: 1 },
            averageOrderValue: { $avg: '$total' }
          }
        };
        break;
      case 'monthly':
        groupStage = {
          $group: {
            _id: {
              year: { $year: '$createdAt' },
              month: { $month: '$createdAt' }
            },
            revenue: { $sum: '$total' },
            orders: { $sum: 1 },
            averageOrderValue: { $avg: '$total' }
          }
        };
        break;
      default: // daily
        groupStage = {
          $group: {
            _id: {
              year: { $year: '$createdAt' },
              month: { $month: '$createdAt' },
              day: { $dayOfMonth: '$createdAt' }
            },
            revenue: { $sum: '$total' },
            orders: { $sum: 1 },
            averageOrderValue: { $avg: '$total' }
          }
        };
    }

    const analytics = await Order.aggregate([
      { $match: { ...matchStage, paymentStatus: 'paid' } },
      groupStage,
      { $sort: { '_id': 1 } }
    ]);

    res.status(200).json({
      success: true,
      data: analytics
    });
  } catch (error) {
    console.error('Error getting revenue analytics:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Get commission analytics
// @route   GET /api/admin/analytics/commissions
// @access  Private/Admin
exports.getCommissionAnalytics = async (req, res) => {
  try {
    const { startDate, endDate, vendorId } = req.query;

    let matchStage = {};
    if (startDate && endDate) {
      matchStage.createdAt = {
        $gte: new Date(startDate),
        $lte: new Date(endDate)
      };
    }
    if (vendorId) {
      matchStage.vendor = vendorId;
    }

    const analytics = await Commission.aggregate([
      { $match: matchStage },
      {
        $group: {
          _id: null,
          totalCommissions: { $sum: '$amounts.platformCommission' },
          totalVendorEarnings: { $sum: '$amounts.vendorEarnings' },
          totalTaxes: { $sum: '$amounts.taxes' },
          totalTransactions: { $sum: 1 },
          averageCommission: { $avg: '$amounts.platformCommission' },
          averageOrderValue: { $avg: '$amounts.grossAmount' }
        }
      }
    ]);

    // Commission by status
    const statusBreakdown = await Commission.aggregate([
      { $match: matchStage },
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 },
          totalAmount: { $sum: '$amounts.platformCommission' }
        }
      }
    ]);

    // Top vendors by commission
    const topVendors = await Commission.aggregate([
      { $match: matchStage },
      {
        $group: {
          _id: '$vendor',
          totalCommissions: { $sum: '$amounts.platformCommission' },
          totalEarnings: { $sum: '$amounts.vendorEarnings' },
          transactionCount: { $sum: 1 }
        }
      },
      { $sort: { totalCommissions: -1 } },
      { $limit: 10 },
      {
        $lookup: {
          from: 'vendors',
          localField: '_id',
          foreignField: '_id',
          as: 'vendor'
        }
      },
      { $unwind: '$vendor' }
    ]);

    res.status(200).json({
      success: true,
      data: {
        summary: analytics[0] || {},
        statusBreakdown,
        topVendors
      }
    });
  } catch (error) {
    console.error('Error getting commission analytics:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Get all promotions
// @route   GET /api/admin/promotions
// @access  Private/Admin
exports.getPromotions = async (req, res) => {
  try {
    const { page = 1, limit = 20, status, type } = req.query;

    let query = {};
    if (status) query.status = status;
    if (type) query.type = type;

    const promotions = await Promotion.find(query)
      .populate('createdBy', 'name email')
      .sort({ createdAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit);

    const total = await Promotion.countDocuments(query);

    res.status(200).json({
      success: true,
      data: promotions,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error getting promotions:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Create promotion
// @route   POST /api/admin/promotions
// @access  Private/Admin
exports.createPromotion = async (req, res) => {
  try {
    const promotion = new Promotion({
      ...req.body,
      createdBy: req.user.id
    });

    await promotion.save();

    // Broadcast promotion to all users
    socketService.broadcast('new_promotion', {
      promotionId: promotion._id,
      title: promotion.title,
      description: promotion.description,
      discountPercentage: promotion.discountPercentage
    });

    res.status(201).json({
      success: true,
      data: promotion,
      message: 'Promotion created successfully'
    });
  } catch (error) {
    console.error('Error creating promotion:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Update promotion
// @route   PUT /api/admin/promotions/:id
// @access  Private/Admin
exports.updatePromotion = async (req, res) => {
  try {
    const promotion = await Promotion.findByIdAndUpdate(
      req.params.id,
      { ...req.body, updatedBy: req.user.id },
      { new: true, runValidators: true }
    );

    if (!promotion) {
      return res.status(404).json({
        success: false,
        message: 'Promotion not found'
      });
    }

    res.status(200).json({
      success: true,
      data: promotion,
      message: 'Promotion updated successfully'
    });
  } catch (error) {
    console.error('Error updating promotion:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Delete promotion
// @route   DELETE /api/admin/promotions/:id
// @access  Private/Admin
exports.deletePromotion = async (req, res) => {
  try {
    const promotion = await Promotion.findByIdAndDelete(req.params.id);

    if (!promotion) {
      return res.status(404).json({
        success: false,
        message: 'Promotion not found'
      });
    }

    res.status(200).json({
      success: true,
      message: 'Promotion deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting promotion:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Get advanced reporting data
// @route   GET /api/admin/reports/advanced
// @access  Private/Admin
exports.getAdvancedReports = async (req, res) => {
  try {
    const { reportType, startDate, endDate } = req.query;

    let reportData = {};

    switch (reportType) {
      case 'user_growth':
        reportData = await getUserGrowthReport(startDate, endDate);
        break;
      case 'revenue_breakdown':
        reportData = await getRevenueBreakdownReport(startDate, endDate);
        break;
      case 'vendor_performance':
        reportData = await getVendorPerformanceReport(startDate, endDate);
        break;
      case 'content_analytics':
        reportData = await getContentAnalyticsReport(startDate, endDate);
        break;
      default:
        return res.status(400).json({
          success: false,
          message: 'Invalid report type'
        });
    }

    res.status(200).json({
      success: true,
      data: reportData
    });
  } catch (error) {
    console.error('Error generating advanced report:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// Helper functions for advanced reports
const getUserGrowthReport = async (startDate, endDate) => {
  const matchStage = {};
  if (startDate && endDate) {
    matchStage.createdAt = {
      $gte: new Date(startDate),
      $lte: new Date(endDate)
    };
  }

  return await User.aggregate([
    { $match: matchStage },
    {
      $group: {
        _id: {
          year: { $year: '$createdAt' },
          month: { $month: '$createdAt' },
          day: { $dayOfMonth: '$createdAt' }
        },
        newUsers: { $sum: 1 },
        activeUsers: { $sum: { $cond: ['$isActive', 1, 0] } }
      }
    },
    { $sort: { '_id': 1 } }
  ]);
};

const getRevenueBreakdownReport = async (startDate, endDate) => {
  const matchStage = { paymentStatus: 'paid' };
  if (startDate && endDate) {
    matchStage.createdAt = {
      $gte: new Date(startDate),
      $lte: new Date(endDate)
    };
  }

  return await Order.aggregate([
    { $match: matchStage },
    {
      $group: {
        _id: '$currency',
        totalRevenue: { $sum: '$total' },
        orderCount: { $sum: 1 },
        averageOrderValue: { $avg: '$total' }
      }
    }
  ]);
};

const getVendorPerformanceReport = async (startDate, endDate) => {
  const matchStage = {};
  if (startDate && endDate) {
    matchStage.createdAt = {
      $gte: new Date(startDate),
      $lte: new Date(endDate)
    };
  }

  return await Commission.aggregate([
    { $match: matchStage },
    {
      $group: {
        _id: '$vendor',
        totalEarnings: { $sum: '$amounts.vendorEarnings' },
        totalCommissions: { $sum: '$amounts.platformCommission' },
        transactionCount: { $sum: 1 },
        averageOrderValue: { $avg: '$amounts.grossAmount' }
      }
    },
    { $sort: { totalEarnings: -1 } },
    { $limit: 50 },
    {
      $lookup: {
        from: 'vendors',
        localField: '_id',
        foreignField: '_id',
        as: 'vendor'
      }
    },
    { $unwind: '$vendor' }
  ]);
};

const getContentAnalyticsReport = async (startDate, endDate) => {
  const matchStage = {};
  if (startDate && endDate) {
    matchStage.createdAt = {
      $gte: new Date(startDate),
      $lte: new Date(endDate)
    };
  }

  return await Post.aggregate([
    { $match: matchStage },
    {
      $group: {
        _id: {
          year: { $year: '$createdAt' },
          month: { $month: '$createdAt' }
        },
        totalPosts: { $sum: 1 },
        totalLikes: { $sum: '$likesCount' },
        totalComments: { $sum: '$commentsCount' },
        averageEngagement: { $avg: { $add: ['$likesCount', '$commentsCount'] } }
      }
    },
    { $sort: { '_id': 1 } }
  ]);
};

// @desc    Get all products with filtering and pagination
// @route   GET /api/admin/products
// @access  Private/Admin
exports.getProducts = async (req, res) => {
  try {
    const { page = 1, limit = 20, status, category, vendor, search, sortBy = 'createdAt', sortOrder = 'desc' } = req.query;

    // Build query
    let query = {};
    if (status) query.status = status;
    if (category) query.category = category;
    if (vendor) query.vendor = vendor;
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { sku: { $regex: search, $options: 'i' } }
      ];
    }

    // Build sort object
    const sort = {};
    sort[sortBy] = sortOrder === 'desc' ? -1 : 1;

    // Execute query with pagination
    const products = await Product.find(query)
      .populate('vendor', 'businessName user')
      .populate('category', 'name')
      .sort(sort)
      .limit(limit * 1)
      .skip((page - 1) * limit);

    const total = await Product.countDocuments(query);

    res.status(200).json({
      success: true,
      data: products,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error getting products:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Get single product details
// @route   GET /api/admin/products/:id
// @access  Private/Admin
exports.getProduct = async (req, res) => {
  try {
    const product = await Product.findById(req.params.id)
      .populate('vendor', 'businessName user contactInfo')
      .populate('category', 'name description')
      .populate('reviews.user', 'name profilePicture');

    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }

    // Get product analytics
    const orderStats = await Order.aggregate([
      { $unwind: '$items' },
      { $match: { 'items.product': product._id } },
      {
        $group: {
          _id: null,
          totalSold: { $sum: '$items.quantity' },
          totalRevenue: { $sum: '$items.total' },
          averageOrderValue: { $avg: '$items.total' }
        }
      }
    ]);

    const analytics = orderStats[0] || { totalSold: 0, totalRevenue: 0, averageOrderValue: 0 };

    res.status(200).json({
      success: true,
      data: {
        product,
        analytics
      }
    });
  } catch (error) {
    console.error('Error getting product:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Update product status
// @route   PUT /api/admin/products/:id/status
// @access  Private/Admin
exports.updateProductStatus = async (req, res) => {
  try {
    const { status, reason } = req.body;
    const product = await Product.findById(req.params.id).populate('vendor');

    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }

    const oldStatus = product.status;
    product.status = status;
    product.statusHistory = product.statusHistory || [];
    product.statusHistory.push({
      status,
      reason,
      changedBy: req.user.id,
      changedAt: new Date()
    });

    await product.save();

    // Send notification to vendor
    await notificationService.sendNotification(product.vendor.user, {
      type: 'product_status_changed',
      title: 'Product Status Updated',
      message: `Your product "${product.name}" status changed from ${oldStatus} to ${status}`,
      data: { productId: product._id, oldStatus, newStatus: status, reason }
    });

    // Broadcast real-time update
    socketService.emit(`user_${product.vendor.user}`, 'product_status_changed', {
      productId: product._id,
      status,
      reason
    });

    res.status(200).json({
      success: true,
      data: product,
      message: 'Product status updated successfully'
    });
  } catch (error) {
    console.error('Error updating product status:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Delete product
// @route   DELETE /api/admin/products/:id
// @access  Private/Admin
exports.deleteProduct = async (req, res) => {
  try {
    const { reason } = req.body;
    const product = await Product.findById(req.params.id).populate('vendor');

    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }

    // Remove from carts and wishlists
    await Cart.updateMany(
      { 'items.product': product._id },
      { $pull: { items: { product: product._id } } }
    );

    await Wishlist.updateMany(
      { products: product._id },
      { $pull: { products: product._id } }
    );

    // Soft delete - mark as deleted instead of removing
    product.status = 'deleted';
    product.deletedAt = new Date();
    product.deletedBy = req.user.id;
    product.deletionReason = reason;
    await product.save();

    // Send notification to vendor
    await notificationService.sendNotification(product.vendor.user, {
      type: 'product_deleted',
      title: 'Product Removed',
      message: `Your product "${product.name}" has been removed from the platform. Reason: ${reason}`,
      data: { productId: product._id, reason }
    });

    res.status(200).json({
      success: true,
      message: 'Product deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting product:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Get all categories
// @route   GET /api/admin/categories
// @access  Private/Admin
exports.getCategories = async (req, res) => {
  try {
    const { page = 1, limit = 20, search } = req.query;

    let query = {};
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } }
      ];
    }

    const categories = await Category.find(query)
      .sort({ createdAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit);

    const total = await Category.countDocuments(query);

    // Get product count for each category
    const categoriesWithStats = await Promise.all(
      categories.map(async (category) => {
        const productCount = await Product.countDocuments({ category: category._id });
        return {
          ...category.toObject(),
          productCount
        };
      })
    );

    res.status(200).json({
      success: true,
      data: categoriesWithStats,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error getting categories:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Create category
// @route   POST /api/admin/categories
// @access  Private/Admin
exports.createCategory = async (req, res) => {
  try {
    const category = new Category({
      ...req.body,
      createdBy: req.user.id
    });

    await category.save();

    res.status(201).json({
      success: true,
      data: category,
      message: 'Category created successfully'
    });
  } catch (error) {
    console.error('Error creating category:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Update category
// @route   PUT /api/admin/categories/:id
// @access  Private/Admin
exports.updateCategory = async (req, res) => {
  try {
    const category = await Category.findByIdAndUpdate(
      req.params.id,
      { ...req.body, updatedBy: req.user.id },
      { new: true, runValidators: true }
    );

    if (!category) {
      return res.status(404).json({
        success: false,
        message: 'Category not found'
      });
    }

    res.status(200).json({
      success: true,
      data: category,
      message: 'Category updated successfully'
    });
  } catch (error) {
    console.error('Error updating category:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Delete category
// @route   DELETE /api/admin/categories/:id
// @access  Private/Admin
exports.deleteCategory = async (req, res) => {
  try {
    const category = await Category.findById(req.params.id);

    if (!category) {
      return res.status(404).json({
        success: false,
        message: 'Category not found'
      });
    }

    // Check if category has products
    const productCount = await Product.countDocuments({ category: category._id });
    if (productCount > 0) {
      return res.status(400).json({
        success: false,
        message: `Cannot delete category. It has ${productCount} products. Please move or delete products first.`
      });
    }

    await Category.findByIdAndDelete(req.params.id);

    res.status(200).json({
      success: true,
      message: 'Category deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting category:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Get order management data
// @route   GET /api/admin/orders
// @access  Private/Admin
exports.getOrders = async (req, res) => {
  try {
    const { page = 1, limit = 20, status, vendor, search, sortBy = 'createdAt', sortOrder = 'desc' } = req.query;

    let query = {};
    if (status) query.status = status;
    if (vendor) query['items.vendor'] = vendor;
    if (search) {
      query.$or = [
        { orderNumber: { $regex: search, $options: 'i' } },
        { 'shippingAddress.fullName': { $regex: search, $options: 'i' } }
      ];
    }

    const sort = {};
    sort[sortBy] = sortOrder === 'desc' ? -1 : 1;

    const orders = await Order.find(query)
      .populate('user', 'name email profilePicture')
      .populate('items.product', 'name images price')
      .populate('items.vendor', 'businessName')
      .sort(sort)
      .limit(limit * 1)
      .skip((page - 1) * limit);

    const total = await Order.countDocuments(query);

    res.status(200).json({
      success: true,
      data: orders,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error getting orders:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Update order status
// @route   PUT /api/admin/orders/:id/status
// @access  Private/Admin
exports.updateOrderStatus = async (req, res) => {
  try {
    const { status, reason } = req.body;
    const order = await Order.findById(req.params.id).populate('user');

    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Order not found'
      });
    }

    const oldStatus = order.status;
    order.status = status;
    order.statusHistory = order.statusHistory || [];
    order.statusHistory.push({
      status,
      reason,
      changedBy: req.user.id,
      changedAt: new Date()
    });

    await order.save();

    // Send notification to user
    await notificationService.sendNotification(order.user._id, {
      type: 'order_status_changed',
      title: 'Order Status Updated',
      message: `Your order #${order.orderNumber} status changed to ${status}`,
      data: { orderId: order._id, orderNumber: order.orderNumber, status, reason }
    });

    // Broadcast real-time update
    socketService.emit(`user_${order.user._id}`, 'order_status_changed', {
      orderId: order._id,
      orderNumber: order.orderNumber,
      status,
      oldStatus
    });

    res.status(200).json({
      success: true,
      data: order,
      message: 'Order status updated successfully'
    });
  } catch (error) {
    console.error('Error updating order status:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};
