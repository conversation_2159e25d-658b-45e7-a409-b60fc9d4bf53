const express = require('express');
const router = express.Router();

const {
  analyzeContent,
  getModerationStatus,
  updateModerationStatus
} = require('../controllers/contentModerationController');

const {
  getBlockedWords,
  getScopedBlockedWords,
  createBlockedWord,
  updateBlockedWord,
  deleteBlockedWord
} = require('../controllers/blockedWordController');

const {
  getModerationSettings,
  updateModerationSettings,
  addModerator,
  removeModerator,
  updateModeratorPermissions
} = require('../controllers/moderationSettingsController');

const { protect } = require('../middleware/auth');

// Content moderation routes
router.route('/analyze').post(protect, analyzeContent);
router.route('/:contentType/:contentId').get(protect, getModerationStatus);
router.route('/:id').put(protect, updateModerationStatus);

// Blocked words routes
router.route('/blocked-words').get(protect, getBlockedWords).post(protect, createBlockedWord);
router.route('/blocked-words/:id').put(protect, updateBlockedWord).delete(protect, deleteBlockedWord);
router.route('/blocked-words/:scope/:scopeId').get(protect, getScopedBlockedWords);

// Moderation settings routes
router.route('/settings/:ownerType/:ownerId')
  .get(protect, getModerationSettings)
  .put(protect, updateModerationSettings);

router.route('/settings/:ownerType/:ownerId/moderators')
  .post(protect, addModerator);

router.route('/settings/:ownerType/:ownerId/moderators/:userId')
  .put(protect, updateModeratorPermissions)
  .delete(protect, removeModerator);

module.exports = router;
