const express = require('express');
const router = express.Router();
const { protect, authorize } = require('../middleware/auth');
const {
  getEmotions,
  getEmotion,
  createEmotion,
  updateEmotion,
  deleteEmotion,
} = require('../controllers/emotionController');

// Public routes
router.get('/', getEmotions);
router.get('/:id', getEmotion);

// Admin routes
router.use(protect);
router.use(authorize('admin'));
router.post('/', createEmotion);
router.put('/:id', updateEmotion);
router.delete('/:id', deleteEmotion);

module.exports = router;
