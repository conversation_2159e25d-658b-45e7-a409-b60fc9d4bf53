{"name": "letstalk-backend", "version": "1.0.0", "description": "Backend for Let's Talk social media application", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["social-media", "nodejs", "express", "mongodb", "socket.io"], "author": "", "license": "MIT", "dependencies": {"axios": "^1.9.0", "bcryptjs": "^3.0.2", "cloudinary": "^2.6.1", "cors": "^2.8.5", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "express": "^5.1.0", "express-fileupload": "^1.5.1", "express-rate-limit": "^7.5.0", "express-validator": "^7.2.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.14.2", "morgan": "^1.10.0", "multer": "^1.4.5-lts.2", "node-schedule": "^2.1.1", "socket.io": "^4.8.1", "stripe": "^18.1.1", "uuid": "^11.1.0"}, "devDependencies": {"nodemon": "^3.1.10"}}