const express = require('express');
const router = express.Router();
const { protect } = require('../middleware/auth');
const {
  getUserMoods,
  getUserMood,
  createUserMood,
  updateUserMood,
  deleteUserMood,
} = require('../controllers/userMoodController');

const {
  getMoodComments,
  createMoodComment,
  updateMoodComment,
  deleteMoodComment,
} = require('../controllers/moodCommentController');

// Public routes
router.get('/', getUserMoods);
router.get('/:id', getUserMood);
router.get('/:id/comments', getMoodComments);

// Protected routes
router.use(protect);
router.post('/', createUserMood);
router.put('/:id', updateUserMood);
router.delete('/:id', deleteUserMood);
router.post('/:id/comments', createMoodComment);
router.put('/comments/:id', updateMoodComment);
router.delete('/comments/:id', deleteMoodComment);

module.exports = router;
