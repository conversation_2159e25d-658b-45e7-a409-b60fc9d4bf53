/**
 * Utility to generate dynamic configuration for the frontend
 * This allows the frontend to connect to the correct backend port
 * even if the backend had to use a different port than expected
 */
const fs = require('fs');
const path = require('path');

/**
 * Generate a dynamic configuration file for the frontend
 * @param {number} port - The port the backend is running on
 */
const generateDynamicConfig = (port) => {
  try {
    // Validate and normalize port
    let validPort = parseInt(port, 10);
    if (isNaN(validPort) || validPort <= 0 || validPort > 65535) {
      console.warn(`Invalid port number: ${port}, using default port 10000`);
      validPort = 10000;
    }

    // Create the config object with simple URLs
    const apiBaseUrl = `http://localhost:${validPort}`;
    const socketUrl = `http://localhost:${validPort}`;

    const config = {
      apiBaseUrl,
      socketUrl,
      timestamp: new Date().toISOString()
    };

    // Convert to JSON
    const configJson = JSON.stringify(config, null, 2);

    // Determine the path to save the config
    // We'll save it in the frontend/public directory so it's accessible via HTTP
    const configPath = path.join(__dirname, '../../frontend/public/dynamic-config.json');

    // Write the file
    fs.writeFileSync(configPath, configJson);

    console.log(`✅ Dynamic configuration generated at ${configPath}`);
    console.log(`📡 API Base URL: ${config.apiBaseUrl}`);
    console.log(`🔌 Socket URL: ${config.socketUrl}`);
  } catch (err) {
    console.error(`❌ Failed to generate dynamic configuration: ${err.message}`);
  }
};

module.exports = { generateDynamicConfig };
