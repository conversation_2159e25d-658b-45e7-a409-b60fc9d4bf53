const mongoose = require('mongoose');

const ClipSchema = new mongoose.Schema({
  title: {
    type: String,
    required: [true, 'Please add a title'],
    trim: true,
    maxlength: [100, 'Title cannot be more than 100 characters']
  },
  description: {
    type: String,
    maxlength: [500, 'Description cannot be more than 500 characters']
  },
  stream: {
    type: mongoose.Schema.ObjectId,
    ref: 'LiveStream',
    required: true
  },
  user: {
    type: mongoose.Schema.ObjectId,
    ref: 'User',
    required: true
  },
  url: {
    type: String,
    required: [true, 'Please add a video URL']
  },
  thumbnailUrl: {
    type: String,
    required: [true, 'Please add a thumbnail URL']
  },
  duration: {
    type: Number,
    required: [true, 'Please add a duration in seconds'],
    min: [1, 'Duration must be at least 1 second']
  },
  startTime: {
    type: Number,
    required: [true, 'Please add a start time in seconds']
  },
  endTime: {
    type: Number,
    required: [true, 'Please add an end time in seconds']
  },
  views: {
    type: Number,
    default: 0
  },
  likes: {
    type: Number,
    default: 0
  },
  isPublic: {
    type: Boolean,
    default: true
  },
  isMinted: {
    type: Boolean,
    default: false
  },
  createdAt: {
    type: Date,
    default: Date.now
  }
});

// Create index for faster queries
ClipSchema.index({ stream: 1, createdAt: -1 });
ClipSchema.index({ user: 1, createdAt: -1 });
ClipSchema.index({ isPublic: 1 });

module.exports = mongoose.model('Clip', ClipSchema);
