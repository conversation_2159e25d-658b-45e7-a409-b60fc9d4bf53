const mongoose = require('mongoose');

const LiveStreamSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  title: {
    type: String,
    required: [true, 'Please provide a title for your stream'],
    trim: true,
    maxlength: [100, 'Title cannot be more than 100 characters'],
  },
  description: {
    type: String,
    trim: true,
    maxlength: [2200, 'Description cannot be more than 2200 characters'],
  },
  thumbnail: {
    url: {
      type: String,
    },
    publicId: {
      type: String,
    },
  },
  status: {
    type: String,
    enum: ['scheduled', 'live', 'ended'],
    default: 'scheduled',
  },
  streamKey: {
    type: String,
    required: true,
    unique: true,
  },
  playbackId: {
    type: String,
  },
  streamUrl: {
    type: String,
  },
  scheduledFor: {
    type: Date,
  },
  startedAt: {
    type: Date,
  },
  endedAt: {
    type: Date,
  },
  category: {
    type: String,
    enum: ['gaming', 'music', 'chatting', 'art', 'beauty', 'fitness', 'education', 'food', 'travel', 'other'],
    default: 'other',
  },
  tags: [
    {
      type: String,
      trim: true,
    },
  ],
  isPrivate: {
    type: Boolean,
    default: false,
  },
  allowedViewers: [
    {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
    },
  ],
  coHosts: [
    {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
    },
  ],
  settings: {
    chat: {
      enabled: {
        type: Boolean,
        default: true,
      },
      followersOnly: {
        type: Boolean,
        default: false,
      },
      slowMode: {
        type: Boolean,
        default: false,
      },
      slowModeInterval: {
        type: Number,
        default: 5, // seconds
      },
    },
    reactions: {
      enabled: {
        type: Boolean,
        default: true,
      },
    },
    recording: {
      enabled: {
        type: Boolean,
        default: true,
      },
      autoPublish: {
        type: Boolean,
        default: false,
      },
    },
    multiHost: {
      enabled: {
        type: Boolean,
        default: true,
      },
      maxHosts: {
        type: Number,
        default: 4,
      },
      layout: {
        type: String,
        enum: ['grid', 'spotlight', 'sideBySide'],
        default: 'grid',
      },
      allowCoHostInvites: {
        type: Boolean,
        default: false,
      },
      allowViewerPromotions: {
        type: Boolean,
        default: false,
      },
      autoAcceptInvites: {
        type: Boolean,
        default: false,
      },
    },
    aiClipRemixing: {
      enabled: {
        type: Boolean,
        default: true,
      },
      autoGenerateClips: {
        type: Boolean,
        default: true,
      },
      clipDuration: {
        type: Number,
        default: 30, // seconds
      },
      maxClipsPerStream: {
        type: Number,
        default: 5,
      },
      detectionSensitivity: {
        type: Number,
        min: 0,
        max: 1,
        default: 0.7,
      },
      preferredStyle: {
        type: String,
        enum: ['dynamic', 'cinematic', 'minimal', 'energetic', 'emotional'],
        default: 'dynamic',
      },
      autoAddMusic: {
        type: Boolean,
        default: false,
      },
      autoAddCaptions: {
        type: Boolean,
        default: true,
      },
      autoShareToSocial: {
        type: Boolean,
        default: false,
      },
      detectionTypes: {
        type: [String],
        default: ['engagement_spike', 'emotional_moment', 'action_detected', 'conversation_highlight'],
      },
    },
    fanRewards: {
      enabled: {
        type: Boolean,
        default: true,
      },
      bonusMultiplier: {
        type: Number,
        default: 1,
      },
      specialRewards: [{
        name: String,
        description: String,
        imageUrl: String,
        triggerType: {
          type: String,
          enum: ['time_watched', 'chat_count', 'reaction_count', 'gift_amount', 'random'],
        },
        triggerValue: Number,
        rewardType: {
          type: String,
          enum: ['points', 'badge', 'emote', 'effect', 'access'],
        },
        rewardValue: mongoose.Schema.Types.Mixed,
      }],
    },
    voiceCommands: {
      enabled: {
        type: Boolean,
        default: false,
      },
      allowViewerCommands: {
        type: Boolean,
        default: false,
      },
      viewerCommandPermissions: {
        type: [String],
        default: ['highlight_clip', 'send_reaction'],
      },
    },
    monetization: {
      gifting: {
        enabled: {
          type: Boolean,
          default: true,
        },
      },
      shopping: {
        enabled: {
          type: Boolean,
          default: false,
        },
      },
      nft: {
        enabled: {
          type: Boolean,
          default: false,
        },
        mintableClips: {
          type: Boolean,
          default: true,
        },
        exclusiveAccess: {
          enabled: {
            type: Boolean,
            default: false,
          },
          requiredCollection: String,
          requiredTokenIds: [String],
          accessType: {
            type: String,
            enum: ['full', 'vip', 'early', 'exclusive', 'limited'],
            default: 'full',
          },
          fallbackAccess: {
            type: String,
            enum: ['none', 'delayed', 'limited', 'preview'],
            default: 'none',
          },
        },
      },
      fanClub: {
        enabled: {
          type: Boolean,
          default: false,
        },
        requiredTier: {
          type: Number,
          default: 0, // 0 means no tier required
        },
      },
      sponsorships: {
        enabled: {
          type: Boolean,
          default: true,
        },
        displayMode: {
          type: String,
          enum: ['banner', 'overlay', 'sidebar', 'intermission'],
          default: 'overlay',
        },
      },
    },
  },
  emotions: [
    {
      emotion: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Emotion',
      },
      intensity: {
        type: Number,
        min: 1,
        max: 10,
        default: 5,
      },
      position: {
        x: {
          type: Number,
          min: 0,
          max: 100,
          default: 50,
        },
        y: {
          type: Number,
          min: 0,
          max: 100,
          default: 50,
        },
      },
      visualEffect: {
        enabled: {
          type: Boolean,
          default: false,
        },
        type: {
          type: String,
          enum: ['pulse', 'glow', 'particles', 'color', 'none'],
          default: 'none',
        },
        intensity: {
          type: Number,
          min: 1,
          max: 10,
          default: 5,
        },
      },
    },
  ],
  arEffects: [
    {
      effect: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'AREffect',
      },
      active: {
        type: Boolean,
        default: true,
      },
      position: {
        x: {
          type: Number,
          default: 0,
        },
        y: {
          type: Number,
          default: 0,
        },
        z: {
          type: Number,
          default: 0,
        },
      },
      scale: {
        x: {
          type: Number,
          default: 1,
        },
        y: {
          type: Number,
          default: 1,
        },
        z: {
          type: Number,
          default: 1,
        },
      },
      rotation: {
        x: {
          type: Number,
          default: 0,
        },
        y: {
          type: Number,
          default: 0,
        },
        z: {
          type: Number,
          default: 0,
        },
      },
    },
  ],
  viewCount: {
    type: Number,
    default: 0,
  },
  peakViewers: {
    type: Number,
    default: 0,
  },
  totalViews: {
    type: Number,
    default: 0,
  },
  likesCount: {
    type: Number,
    default: 0,
  },
  commentsCount: {
    type: Number,
    default: 0,
  },
  recordings: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'StreamRecording'
  }],
}, { timestamps: true });

// Create indexes for efficient queries
LiveStreamSchema.index({ user: 1, status: 1 });
LiveStreamSchema.index({ status: 1, createdAt: -1 });
LiveStreamSchema.index({ category: 1, status: 1 });
LiveStreamSchema.index({ 'tags': 1 });

module.exports = mongoose.model('LiveStream', LiveStreamSchema);
