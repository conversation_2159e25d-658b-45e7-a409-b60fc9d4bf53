const MoodComment = require('../models/MoodComment');
const UserMood = require('../models/UserMood');
const Like = require('../models/Like');
const User = require('../models/User');
const Notification = require('../models/Notification');
const { createError } = require('../utils/error');
const socketEmitter = require('../utils/socketEmitter');
const mongoose = require('mongoose');

/**
 * Get comments for a user mood
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.getMoodComments = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { parent, limit = 20, page = 1 } = req.query;

    // Find mood
    const mood = await UserMood.findById(id);
    if (!mood) {
      return next(createError(404, 'Mood not found'));
    }

    // Calculate pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);

    // Build query
    const query = {
      mood: id,
      ...(parent ? { parent } : { parentComment: { $exists: false } }),
    };

    // Find comments
    const comments = await MoodComment.find(query)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit))
      .populate('user', 'username name profilePicture')
      .populate({
        path: 'parentComment',
        select: 'user text',
        populate: {
          path: 'user',
          select: 'username',
        },
      });

    // Get total count
    const total = await MoodComment.countDocuments(query);

    // Check if user has liked each comment
    const commentsWithLikeStatus = await Promise.all(
      comments.map(async (comment) => {
        const commentObj = comment.toObject();

        if (req.user) {
          const isLiked = await Like.exists({
            user: req.user.id,
            moodComment: comment._id,
          });

          commentObj.isLiked = !!isLiked;
        } else {
          commentObj.isLiked = false;
        }

        return commentObj;
      })
    );

    res.status(200).json({
      success: true,
      count: comments.length,
      total,
      totalPages: Math.ceil(total / parseInt(limit)),
      currentPage: parseInt(page),
      data: commentsWithLikeStatus,
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Create a new comment on a user mood
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.createMoodComment = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { text, emotions, parentComment } = req.body;

    // Find mood
    const mood = await UserMood.findById(id);
    if (!mood) {
      return next(createError(404, 'Mood not found'));
    }

    // Check if parent comment exists if provided
    if (parentComment) {
      const parentCommentExists = await MoodComment.findById(parentComment);
      if (!parentCommentExists) {
        return next(createError(404, 'Parent comment not found'));
      }
    }

    // Create comment
    const comment = await MoodComment.create({
      user: req.user.id,
      mood: id,
      text,
      emotions: emotions || [],
      ...(parentComment && { parentComment }),
    });

    // Populate user details
    await comment.populate('user', 'username name profilePicture');

    // Update mood comments count
    mood.commentsCount = (mood.commentsCount || 0) + 1;
    await mood.save();

    // Create notification for mood owner if different from commenter
    if (mood.user.toString() !== req.user.id) {
      const notification = await Notification.create({
        recipient: mood.user,
        type: 'comment_mood',
        sender: req.user.id,
        text: text.substring(0, 100),
        mood: mood._id,
      });

      // Populate sender details for real-time notification
      await notification.populate('sender', 'username name profilePicture');

      // Emit notification via Socket.IO
      socketEmitter.emitNotification(mood.user.toString(), notification);
    }

    // Check for mentions in the comment text
    const mentionRegex = /@(\w+)/g;
    const mentions = text.match(mentionRegex);

    if (mentions) {
      const uniqueMentions = [...new Set(mentions.map(m => m.substring(1)))];

      // Find mentioned users
      const mentionedUsers = await User.find({
        username: { $in: uniqueMentions },
      });

      // Create notifications for mentioned users
      for (const mentionedUser of mentionedUsers) {
        // Skip if mentioned user is the commenter or the mood owner
        if (
          mentionedUser._id.toString() === req.user.id ||
          mentionedUser._id.toString() === mood.user.toString()
        ) {
          continue;
        }

        const mentionNotification = await Notification.create({
          recipient: mentionedUser._id,
          type: 'mention',
          sender: req.user.id,
          text: text.substring(0, 100),
          mood: mood._id,
        });

        // Populate sender details for real-time notification
        await mentionNotification.populate('sender', 'username name profilePicture');

        // Emit notification via Socket.IO
        socketEmitter.emitNotification(mentionedUser._id.toString(), mentionNotification);
      }
    }

    res.status(201).json({
      success: true,
      data: comment,
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Update a mood comment
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.updateMoodComment = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { text } = req.body;

    // Find comment
    const comment = await MoodComment.findById(id);

    if (!comment) {
      return next(createError(404, 'Comment not found'));
    }

    // Check if user is the owner of the comment
    if (comment.user.toString() !== req.user.id) {
      return next(createError(403, 'You are not authorized to update this comment'));
    }

    // Update comment
    comment.text = text;
    await comment.save();

    // Populate user details
    await comment.populate('user', 'username name profilePicture');

    res.status(200).json({
      success: true,
      data: comment,
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Delete a mood comment
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.deleteMoodComment = async (req, res, next) => {
  try {
    const { id } = req.params;

    // Find comment
    const comment = await MoodComment.findById(id);

    if (!comment) {
      return next(createError(404, 'Comment not found'));
    }

    // Check if user is the owner of the comment or the mood
    const mood = await UserMood.findById(comment.mood);
    
    if (comment.user.toString() !== req.user.id && mood.user.toString() !== req.user.id) {
      return next(createError(403, 'You are not authorized to delete this comment'));
    }

    // Delete all likes associated with this comment
    await Like.deleteMany({ moodComment: id });

    // Delete the comment
    await comment.deleteOne();

    // Update mood comments count
    mood.commentsCount = Math.max((mood.commentsCount || 0) - 1, 0);
    await mood.save();

    res.status(200).json({
      success: true,
      data: {},
    });
  } catch (err) {
    next(err);
  }
};
