import { execSync } from 'child_process';

/**
 * Utility script to find and kill processes using a specific port
 * Usage: node kill-port.js [port]
 * Example: node kill-port.js 50000
 */

// Get port from command line arguments or use default
const port = process.argv[2] || 50000;

try {
  console.log(`Attempting to find process using port ${port}...`);

  // For Windows
  if (process.platform === 'win32') {
    // Find the process ID using the port
    const findCommand = `netstat -ano | findstr :${port}`;
    console.log(`Running command: ${findCommand}`);

    const output = execSync(findCommand, { encoding: 'utf8' });
    console.log('Output:', output);

    // Extract PID from the output
    const lines = output.split('\n').filter(line => line.trim() !== '');

    if (lines.length === 0) {
      console.log(`No process found using port ${port}`);
      process.exit(0);
    }

    // The PID is the last column in the netstat output
    const pids = new Set();
    lines.forEach(line => {
      const parts = line.trim().split(/\s+/);
      if (parts.length >= 5) {
        pids.add(parts[parts.length - 1]);
      }
    });

    if (pids.size === 0) {
      console.log(`Could not extract PID from output`);
      process.exit(1);
    }

    // Kill each process
    for (const pid of pids) {
      console.log(`Killing process with PID: ${pid}`);
      try {
        execSync(`taskkill /F /PID ${pid}`);
        console.log(`Successfully killed process ${pid}`);
      } catch (killError) {
        console.error(`Failed to kill process ${pid}:`, killError.message);
      }
    }
  }
  // For Unix-based systems (Linux, macOS)
  else {
    // Find and kill the process in one command
    const command = `lsof -i :${port} | grep LISTEN | awk '{print $2}' | xargs kill -9`;
    console.log(`Running command: ${command}`);

    try {
      execSync(command, { encoding: 'utf8' });
      console.log(`Successfully killed process using port ${port}`);
    } catch (error) {
      // lsof might not find any process, which would cause an error
      console.log(`No process found using port ${port} or failed to kill it`);
    }
  }

  console.log(`Port ${port} should now be free to use`);
} catch (error) {
  console.error('Error:', error.message);
  process.exit(1);
}
