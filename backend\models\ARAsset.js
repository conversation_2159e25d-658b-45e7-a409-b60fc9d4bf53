const mongoose = require('mongoose');

/**
 * AR Asset Model
 * 
 * Represents augmented reality assets that can be used in posts, reels, and stories.
 * These assets can be filters, effects, objects, or backgrounds.
 */
const ARAssetSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Please provide an AR asset name'],
    trim: true
  },
  description: {
    type: String,
    maxlength: [200, 'Description cannot exceed 200 characters']
  },
  type: {
    type: String,
    enum: ['filter', 'effect', 'object', 'background', 'animation'],
    required: [true, 'Please provide an AR asset type']
  },
  url: {
    type: String,
    required: [true, 'Please provide an AR asset URL']
  },
  thumbnail: {
    type: String,
    default: null
  },
  previewVideo: {
    type: String,
    default: null
  },
  category: {
    type: String,
    enum: ['mood', 'emotion', 'general', 'seasonal', 'branded', 'creative', 'fun'],
    default: 'general'
  },
  relatedEmotions: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Emotion'
  }],
  relatedMoods: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Mood'
  }],
  tags: [{
    type: String,
    trim: true
  }],
  creator: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    default: null
  },
  isVerified: {
    type: Boolean,
    default: false
  },
  isActive: {
    type: Boolean,
    default: true
  },
  usageCount: {
    type: Number,
    default: 0
  },
  // Technical properties for AR implementation
  format: {
    type: String,
    enum: ['gltf', 'usdz', 'webxr', 'spark-ar', 'custom'],
    default: 'webxr'
  },
  properties: {
    scale: {
      type: Number,
      default: 1.0
    },
    position: {
      x: {
        type: Number,
        default: 0
      },
      y: {
        type: Number,
        default: 0
      },
      z: {
        type: Number,
        default: 0
      }
    },
    rotation: {
      x: {
        type: Number,
        default: 0
      },
      y: {
        type: Number,
        default: 0
      },
      z: {
        type: Number,
        default: 0
      }
    },
    interactive: {
      type: Boolean,
      default: false
    },
    animationTrigger: {
      type: String,
      enum: ['tap', 'proximity', 'time', 'emotion', 'sound', 'none'],
      default: 'none'
    }
  }
}, {
  timestamps: true
});

// Index for faster queries
ARAssetSchema.index({ name: 1 });
ARAssetSchema.index({ type: 1 });
ARAssetSchema.index({ category: 1 });
ARAssetSchema.index({ isActive: 1 });
ARAssetSchema.index({ usageCount: -1 });

module.exports = mongoose.model('ARAsset', ARAssetSchema);
