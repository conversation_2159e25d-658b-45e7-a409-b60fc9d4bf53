const mongoose = require('mongoose');

const CampaignSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Please add a campaign name'],
    trim: true,
    maxlength: [100, 'Name cannot be more than 100 characters']
  },
  description: {
    type: String,
    maxlength: [500, 'Description cannot be more than 500 characters']
  },
  type: {
    type: String,
    enum: ['flash_sale', 'coupon', 'bogo', 'seasonal', 'product_boost', 'welcome_offer', 'exit_intent'],
    required: true
  },
  status: {
    type: String,
    enum: ['draft', 'active', 'paused', 'completed', 'cancelled'],
    default: 'draft'
  },
  startDate: {
    type: Date,
    required: true
  },
  endDate: {
    type: Date,
    required: true
  },
  targetAudience: {
    type: {
      type: String,
      enum: ['all', 'new_customers', 'returning_customers', 'vip_customers', 'custom'],
      default: 'all'
    },
    customSegments: [String],
    locations: [String],
    ageRange: {
      min: Number,
      max: Number
    },
    interests: [String]
  },
  discountConfig: {
    type: {
      type: String,
      enum: ['percentage', 'fixed_amount', 'free_shipping', 'bogo']
    },
    value: Number,
    maxDiscount: Number,
    minOrderValue: Number,
    applicableProducts: [{
      type: mongoose.Schema.ObjectId,
      ref: 'Product'
    }],
    applicableCategories: [String]
  },
  couponConfig: {
    code: String,
    usageLimit: Number,
    usageCount: {
      type: Number,
      default: 0
    },
    userLimit: {
      type: Number,
      default: 1
    },
    isPublic: {
      type: Boolean,
      default: false
    }
  },
  bogoConfig: {
    buyQuantity: Number,
    getQuantity: Number,
    buyProducts: [{
      type: mongoose.Schema.ObjectId,
      ref: 'Product'
    }],
    getProducts: [{
      type: mongoose.Schema.ObjectId,
      ref: 'Product'
    }]
  },
  productBoostConfig: {
    boostedProducts: [{
      product: {
        type: mongoose.Schema.ObjectId,
        ref: 'Product'
      },
      position: Number,
      budget: Number,
      spent: {
        type: Number,
        default: 0
      }
    }],
    totalBudget: Number,
    costPerClick: Number,
    targetLocations: [String]
  },
  bannerConfig: {
    template: String,
    backgroundColor: String,
    textColor: String,
    buttonColor: String,
    images: [String],
    headline: String,
    subheadline: String,
    buttonText: String,
    position: {
      type: String,
      enum: ['top', 'bottom', 'popup', 'sidebar', 'inline'],
      default: 'top'
    }
  },
  emailConfig: {
    subject: String,
    template: String,
    sendToSegment: String,
    scheduledTime: Date,
    sent: {
      type: Boolean,
      default: false
    }
  },
  smsConfig: {
    message: String,
    sendToSegment: String,
    scheduledTime: Date,
    sent: {
      type: Boolean,
      default: false
    }
  },
  analytics: {
    impressions: {
      type: Number,
      default: 0
    },
    clicks: {
      type: Number,
      default: 0
    },
    conversions: {
      type: Number,
      default: 0
    },
    revenue: {
      type: Number,
      default: 0
    },
    ctr: {
      type: Number,
      default: 0
    },
    conversionRate: {
      type: Number,
      default: 0
    },
    roas: {
      type: Number,
      default: 0
    }
  },
  creator: {
    type: mongoose.Schema.ObjectId,
    ref: 'User',
    required: true
  },
  isActive: {
    type: Boolean,
    default: true
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Update the updatedAt field before saving
CampaignSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

// Calculate analytics before saving
CampaignSchema.pre('save', function(next) {
  if (this.analytics.clicks > 0 && this.analytics.impressions > 0) {
    this.analytics.ctr = (this.analytics.clicks / this.analytics.impressions) * 100;
  }
  
  if (this.analytics.conversions > 0 && this.analytics.clicks > 0) {
    this.analytics.conversionRate = (this.analytics.conversions / this.analytics.clicks) * 100;
  }
  
  if (this.analytics.revenue > 0 && this.productBoostConfig?.totalBudget > 0) {
    this.analytics.roas = this.analytics.revenue / this.productBoostConfig.totalBudget;
  }
  
  next();
});

// Instance method to check if campaign is currently active
CampaignSchema.methods.isCurrentlyActive = function() {
  const now = new Date();
  return this.status === 'active' && 
         this.startDate <= now && 
         this.endDate >= now;
};

// Instance method to get campaign performance
CampaignSchema.methods.getPerformance = function() {
  return {
    impressions: this.analytics.impressions,
    clicks: this.analytics.clicks,
    conversions: this.analytics.conversions,
    revenue: this.analytics.revenue,
    ctr: this.analytics.ctr,
    conversionRate: this.analytics.conversionRate,
    roas: this.analytics.roas,
    status: this.status,
    daysRemaining: Math.ceil((this.endDate - new Date()) / (1000 * 60 * 60 * 24))
  };
};

// Static method to get active campaigns
CampaignSchema.statics.getActiveCampaigns = function(type = null) {
  const now = new Date();
  let query = {
    status: 'active',
    startDate: { $lte: now },
    endDate: { $gte: now }
  };
  
  if (type) {
    query.type = type;
  }
  
  return this.find(query).populate('creator', 'name username');
};

// Create indexes for better performance
CampaignSchema.index({ creator: 1, createdAt: -1 });
CampaignSchema.index({ type: 1, status: 1 });
CampaignSchema.index({ startDate: 1, endDate: 1 });
CampaignSchema.index({ 'couponConfig.code': 1 });
CampaignSchema.index({ status: 1, startDate: 1, endDate: 1 });

module.exports = mongoose.model('Campaign', CampaignSchema);
