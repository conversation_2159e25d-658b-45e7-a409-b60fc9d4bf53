const Reel = require('../models/Reel');
const View = require('../models/View');
const Comment = require('../models/Comment');
const User = require('../models/User');
const Follow = require('../models/Follow');
const { createError } = require('../utils/error');
const mongoose = require('mongoose');

/**
 * Get all reels with pagination and filtering
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.getReels = async (req, res, next) => {
  try {
    const {
      page = 1,
      limit = 10,
      includeUserDetails = true,
      includeLikes = false,
      moodFilter
    } = req.query;

    // Build query
    let query = {};

    // Add mood filter if provided
    if (moodFilter) {
      query['emotions.emotion'] = moodFilter;
    }

    // Execute query with pagination
    const reels = await Reel.find(query)
      .sort({ createdAt: -1 })
      .skip((parseInt(page) - 1) * parseInt(limit))
      .limit(parseInt(limit))
      .populate(includeUserDetails ? 'user' : '', 'username name profilePicture isVerified')
      .populate('emotions.emotion', 'name color icon category');

    // Get total count for pagination
    const total = await Reel.countDocuments(query);

    res.status(200).json({
      success: true,
      count: reels.length,
      total,
      data: reels,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages: Math.ceil(total / parseInt(limit))
      }
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Get a single reel by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.getReel = async (req, res, next) => {
  try {
    const reel = await Reel.findById(req.params.id)
      .populate('user', 'username name profilePicture isVerified')
      .populate('emotions.emotion', 'name color icon category');

    if (!reel) {
      return next(createError(404, 'Reel not found'));
    }

    // Increment view count
    reel.viewsCount += 1;
    await reel.save();

    res.status(200).json({
      success: true,
      data: reel
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Create a new reel
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.createReel = async (req, res, next) => {
  try {
    // Add user to request body
    req.body.user = req.user.id;

    // Create reel
    const reel = await Reel.create(req.body);

    // Populate user details
    await reel.populate('user', 'username name profilePicture isVerified');
    await reel.populate('emotions.emotion', 'name color icon category');

    res.status(201).json({
      success: true,
      data: reel
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Update a reel
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.updateReel = async (req, res, next) => {
  try {
    let reel = await Reel.findById(req.params.id);

    if (!reel) {
      return next(createError(404, 'Reel not found'));
    }

    // Check if user is reel owner
    if (reel.user.toString() !== req.user.id) {
      return next(createError(403, 'Not authorized to update this reel'));
    }

    // Update reel
    reel = await Reel.findByIdAndUpdate(req.params.id, req.body, {
      new: true,
      runValidators: true
    });

    // Populate user details
    await reel.populate('user', 'username name profilePicture isVerified');
    await reel.populate('emotions.emotion', 'name color icon category');

    res.status(200).json({
      success: true,
      data: reel
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Delete a reel
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.deleteReel = async (req, res, next) => {
  try {
    const reel = await Reel.findById(req.params.id);

    if (!reel) {
      return next(createError(404, 'Reel not found'));
    }

    // Check if user is reel owner
    if (reel.user.toString() !== req.user.id) {
      return next(createError(403, 'Not authorized to delete this reel'));
    }

    await reel.remove();

    res.status(200).json({
      success: true,
      data: {}
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Get reels by user ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.getUserReels = async (req, res, next) => {
  try {
    const { page = 1, limit = 10 } = req.query;

    const reels = await Reel.find({ user: req.params.userId })
      .sort({ createdAt: -1 })
      .skip((parseInt(page) - 1) * parseInt(limit))
      .limit(parseInt(limit))
      .populate('user', 'username name profilePicture isVerified')
      .populate('emotions.emotion', 'name color icon category');

    const total = await Reel.countDocuments({ user: req.params.userId });

    res.status(200).json({
      success: true,
      count: reels.length,
      total,
      data: reels,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages: Math.ceil(total / parseInt(limit))
      }
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Like a reel
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.likeReel = async (req, res, next) => {
  try {
    const reel = await Reel.findById(req.params.id);

    if (!reel) {
      return next(createError(404, 'Reel not found'));
    }

    // Check if reel has already been liked by this user
    if (reel.likes.includes(req.user.id)) {
      return res.status(400).json({
        success: false,
        error: 'Reel already liked'
      });
    }

    // Add user ID to likes array
    reel.likes.push(req.user.id);
    reel.likesCount = reel.likes.length;

    await reel.save();

    // Emit socket event for real-time updates
    const socketEmitter = require('../utils/socketEmitter');
    socketEmitter.emitLike(req.user.id, 'reel', reel._id.toString(), reel.user.toString());

    res.status(200).json({
      success: true,
      data: reel
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Unlike a reel
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.unlikeReel = async (req, res, next) => {
  try {
    const reel = await Reel.findById(req.params.id);

    if (!reel) {
      return next(createError(404, 'Reel not found'));
    }

    // Check if reel has been liked by this user
    if (!reel.likes.includes(req.user.id)) {
      return res.status(400).json({
        success: false,
        error: 'Reel not liked yet'
      });
    }

    // Remove user ID from likes array
    reel.likes = reel.likes.filter(like => like.toString() !== req.user.id);
    reel.likesCount = reel.likes.length;

    await reel.save();

    res.status(200).json({
      success: true,
      data: reel
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Get users who liked a reel
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.getReelLikes = async (req, res, next) => {
  try {
    const reel = await Reel.findById(req.params.id).populate('likes', 'username name profilePicture isVerified');

    if (!reel) {
      return next(createError(404, 'Reel not found'));
    }

    res.status(200).json({
      success: true,
      count: reel.likes.length,
      data: reel.likes
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Add a comment to a reel
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.addComment = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { text, emotions } = req.body;

    // Find the reel
    const reel = await Reel.findById(id);
    if (!reel) {
      return next(createError(404, 'Reel not found'));
    }

    // Create comment
    const comment = new Comment({
      user: req.user.id,
      reel: id,
      text,
      emotions: emotions || []
    });

    await comment.save();

    // Increment comment count on reel
    reel.commentsCount = (reel.commentsCount || 0) + 1;
    await reel.save();

    // Populate user details
    await comment.populate('user', 'username name profilePicture isVerified');

    // Emit socket event for real-time updates
    const socketEmitter = require('../utils/socketEmitter');
    socketEmitter.emitComment(req.user.id, 'reel', id, comment._id.toString(), reel.user.toString());

    res.status(201).json({
      success: true,
      data: comment
    });
  } catch (err) {
    next(err);
  }
};

exports.getReelComments = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { limit = 20, page = 1 } = req.query;

    // Find the reel
    const reel = await Reel.findById(id);
    if (!reel) {
      return next(createError(404, 'Reel not found'));
    }

    // Calculate pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);

    // Find comments for this reel
    const comments = await Comment.find({
      reel: id,
      parentComment: { $exists: false } // Only get top-level comments
    })
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit))
      .populate('user', 'username name profilePicture isVerified');

    // Get total count
    const total = await Comment.countDocuments({
      reel: id,
      parentComment: { $exists: false }
    });

    // Check if user has liked each comment
    const commentsWithLikeStatus = [];
    for (const comment of comments) {
      const commentObj = comment.toObject();

      // Add reply count
      commentObj.repliesCount = await Comment.countDocuments({
        parentComment: comment._id
      });

      // Add like status if user is authenticated
      if (req.user) {
        const Like = require('../models/Like');
        const isLiked = await Like.exists({
          user: req.user.id,
          comment: comment._id,
        });

        commentObj.isLiked = !!isLiked;
      } else {
        commentObj.isLiked = false;
      }

      commentsWithLikeStatus.push(commentObj);
    }

    res.status(200).json({
      success: true,
      count: comments.length,
      total,
      totalPages: Math.ceil(total / parseInt(limit)),
      currentPage: parseInt(page),
      data: commentsWithLikeStatus,
    });
  } catch (err) {
    next(err);
  }
};

exports.deleteComment = async (req, res, next) => {
  try {
    res.status(200).json({ success: true, data: {} });
  } catch (err) {
    next(err);
  }
};

/**
 * Get trending reels
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.getTrendingReels = async (req, res, next) => {
  try {
    const { limit = 12, timeFrame = 'week', includeUserDetails = true } = req.query;

    // Build time filter based on timeFrame
    const timeFilter = {};
    if (timeFrame !== 'all') {
      const now = new Date();
      let startDate;

      switch (timeFrame) {
        case 'day':
          startDate = new Date(now.setDate(now.getDate() - 1));
          break;
        case 'week':
          startDate = new Date(now.setDate(now.getDate() - 7));
          break;
        case 'month':
          startDate = new Date(now.setMonth(now.getMonth() - 1));
          break;
        default:
          startDate = new Date(now.setDate(now.getDate() - 7)); // Default to week
      }

      timeFilter.createdAt = { $gte: startDate };
    }

    // Find reels with the most likes, comments, and views from the database
    const reels = await Reel.find(timeFilter)
      .sort({ likesCount: -1, commentsCount: -1, viewsCount: -1, createdAt: -1 })
      .limit(parseInt(limit))
      .populate('user', 'username name profilePicture isVerified')
      .populate('emotions.emotion', 'name color icon category');

    // Return real reels from the database
    return res.status(200).json({
      success: true,
      count: reels.length,
      data: reels
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Record a view for a reel
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.viewReel = async (req, res, next) => {
  try {
    const { id } = req.params;

    // Find the reel
    const reel = await Reel.findById(id);
    if (!reel) {
      return next(createError(404, 'Reel not found'));
    }

    // Get IP and user agent
    const ipAddress = req.ip || req.connection.remoteAddress;
    const userAgent = req.headers['user-agent'];

    // Create or update view
    let view;
    if (req.user) {
      // If user is logged in, check for existing view by user
      view = await View.findOne({ reel: id, user: req.user.id });

      if (view) {
        // Update existing view
        view.viewDuration += 1; // Increment duration
        view.completedView = true;
        await view.save();
      } else {
        // Create new view
        view = await View.create({
          reel: id,
          user: req.user.id,
          viewDuration: 1,
          completedView: true,
          ipAddress,
          userAgent
        });
      }
    } else {
      // For anonymous users, check by IP and user agent
      view = await View.findOne({
        reel: id,
        ipAddress,
        userAgent,
        user: { $exists: false }
      });

      if (view) {
        // Update existing view
        view.viewDuration += 1;
        view.completedView = true;
        await view.save();
      } else {
        // Create new view
        view = await View.create({
          reel: id,
          viewDuration: 1,
          completedView: true,
          ipAddress,
          userAgent
        });
      }
    }

    res.status(200).json({
      success: true,
      data: { viewed: true }
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Get feed reels with pagination and filtering
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.getFeedReels = async (req, res, next) => {
  try {
    const {
      page = 1,
      limit = 5,
      includeUserDetails = true,
      includeLikes = false,
      moodFilter
    } = req.query;

    // Build query
    let query = {};

    // Add mood filter if provided
    if (moodFilter) {
      query['emotions.emotion'] = moodFilter;
    }

    // If user is authenticated, prioritize reels from followed users
    let followedUsers = [];
    if (req.user) {
      const follows = await Follow.find({ follower: req.user.id, status: 'accepted' });
      followedUsers = follows.map(follow => follow.following);

      // Include the user's own reels in the feed
      followedUsers.push(req.user.id);
    }

    // Execute query with pagination
    let reelsQuery = Reel.find(query);

    // If we have followed users, prioritize their reels
    if (followedUsers.length > 0) {
      reelsQuery = reelsQuery.sort({
        // First sort by whether the reel is from a followed user
        user: { $in: followedUsers.map(id => id.toString()) } ? -1 : 1,
        // Then by creation date
        createdAt: -1
      });
    } else {
      // Otherwise just sort by creation date
      reelsQuery = reelsQuery.sort({ createdAt: -1 });
    }

    const reels = await reelsQuery
      .skip((parseInt(page) - 1) * parseInt(limit))
      .limit(parseInt(limit))
      .populate(includeUserDetails ? 'user' : '', 'username name profilePicture isVerified')
      .populate('emotions.emotion', 'name color icon category');

    // Get total count for pagination
    const total = await Reel.countDocuments(query);

    res.status(200).json({
      success: true,
      count: reels.length,
      total,
      data: reels,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages: Math.ceil(total / parseInt(limit))
      }
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Get related reels
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.getRelatedReels = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { limit = 10 } = req.query;

    // Find the source reel
    const sourceReel = await Reel.findById(id).populate('emotions.emotion');
    if (!sourceReel) {
      return next(createError(404, 'Reel not found'));
    }

    // Get emotion IDs from the source reel
    const emotionIds = sourceReel.emotions.map(e => e.emotion._id);

    // Find reels with similar emotions, excluding the source reel
    const relatedReels = await Reel.find({
      _id: { $ne: id },
      'emotions.emotion': { $in: emotionIds }
    })
      .sort({ createdAt: -1 })
      .limit(parseInt(limit))
      .populate('user', 'username name profilePicture isVerified')
      .populate('emotions.emotion', 'name color icon category');

    // If not enough related reels, get some recent reels
    if (relatedReels.length < parseInt(limit)) {
      const additionalReels = await Reel.find({
        _id: { $ne: id },
        'emotions.emotion': { $nin: emotionIds }
      })
        .sort({ createdAt: -1 })
        .limit(parseInt(limit) - relatedReels.length)
        .populate('user', 'username name profilePicture isVerified')
        .populate('emotions.emotion', 'name color icon category');

      relatedReels.push(...additionalReels);
    }

    res.status(200).json({
      success: true,
      count: relatedReels.length,
      data: relatedReels
    });
  } catch (err) {
    next(err);
  }
};
