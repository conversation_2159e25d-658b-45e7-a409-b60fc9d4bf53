import React, { useState, useEffect } from 'react'
import {
  Container,
  Typo<PERSON>,
  <PERSON>,
  Card,
  CardContent,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  Switch,
  Button,
  Divider,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  CircularProgress,
  IconButton,
  Paper
} from '@mui/material'
import {
  AccountCircle,
  Security,
  Notifications,
  Palette,
  Language,
  Lock,
  Help,
  Info,
  Delete,
  Download,
  Edit,
  Visibility,
  VisibilityOff,
  Save
} from '@mui/icons-material'
import { useSnackbar } from 'notistack'
import { useAuth } from '../context/AuthContext'
import axios from '../utils/fixedAxios'

const SettingsPage = () => {
  const [settings, setSettings] = useState({
    notifications: {
      email: true,
      push: true,
      likes: true,
      comments: true,
      follows: true,
      messages: true
    },
    privacy: {
      profileVisibility: 'public',
      showEmail: false,
      showPhone: false,
      allowMessages: 'everyone'
    },
    preferences: {
      language: 'en',
      timezone: 'UTC',
      darkMode: false
    }
  })

  const [passwordDialog, setPasswordDialog] = useState(false)
  const [deleteDialog, setDeleteDialog] = useState(false)
  const [loading, setLoading] = useState(false)
  const [passwordForm, setPasswordForm] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  })
  const [showPasswords, setShowPasswords] = useState({
    current: false,
    new: false,
    confirm: false
  })

  const { user, logout } = useAuth()
  const { enqueueSnackbar } = useSnackbar()

  useEffect(() => {
    fetchSettings()
  }, [])

  const fetchSettings = async () => {
    try {
      const response = await axios.get('/api/users/settings')
      if (response.data.settings) {
        setSettings(prev => ({ ...prev, ...response.data.settings }))
      }
    } catch (error) {
      console.error('Error fetching settings:', error)
    }
  }

  const updateSettings = async (newSettings) => {
    try {
      setLoading(true)
      await axios.put('/api/users/settings', newSettings)
      setSettings(newSettings)
      enqueueSnackbar('Settings updated successfully', { variant: 'success' })
    } catch (error) {
      console.error('Error updating settings:', error)
      enqueueSnackbar('Failed to update settings', { variant: 'error' })
    } finally {
      setLoading(false)
    }
  }

  const handleNotificationChange = (key) => {
    const newSettings = {
      ...settings,
      notifications: {
        ...settings.notifications,
        [key]: !settings.notifications[key]
      }
    }
    updateSettings(newSettings)
  }

  const handlePrivacyChange = (key, value) => {
    const newSettings = {
      ...settings,
      privacy: {
        ...settings.privacy,
        [key]: value
      }
    }
    updateSettings(newSettings)
  }

  const handlePreferenceChange = (key, value) => {
    const newSettings = {
      ...settings,
      preferences: {
        ...settings.preferences,
        [key]: value
      }
    }
    updateSettings(newSettings)
  }

  const handlePasswordChange = async () => {
    if (passwordForm.newPassword !== passwordForm.confirmPassword) {
      enqueueSnackbar('Passwords do not match', { variant: 'error' })
      return
    }

    if (passwordForm.newPassword.length < 6) {
      enqueueSnackbar('Password must be at least 6 characters', { variant: 'error' })
      return
    }

    try {
      setLoading(true)
      await axios.put('/api/users/password', {
        currentPassword: passwordForm.currentPassword,
        newPassword: passwordForm.newPassword
      })

      setPasswordDialog(false)
      setPasswordForm({ currentPassword: '', newPassword: '', confirmPassword: '' })
      enqueueSnackbar('Password updated successfully', { variant: 'success' })
    } catch (error) {
      console.error('Error updating password:', error)
      enqueueSnackbar(error.response?.data?.message || 'Failed to update password', { variant: 'error' })
    } finally {
      setLoading(false)
    }
  }

  const handleDeleteAccount = async () => {
    try {
      setLoading(true)
      await axios.delete('/api/users/account')
      enqueueSnackbar('Account deleted successfully', { variant: 'info' })
      logout()
    } catch (error) {
      console.error('Error deleting account:', error)
      enqueueSnackbar('Failed to delete account', { variant: 'error' })
    } finally {
      setLoading(false)
      setDeleteDialog(false)
    }
  }

  const handleDataExport = async () => {
    try {
      setLoading(true)
      const response = await axios.get('/api/users/export-data', { responseType: 'blob' })

      const url = window.URL.createObjectURL(new Blob([response.data]))
      const link = document.createElement('a')
      link.href = url
      link.setAttribute('download', `${user.username}_data.json`)
      document.body.appendChild(link)
      link.click()
      link.remove()

      enqueueSnackbar('Data exported successfully', { variant: 'success' })
    } catch (error) {
      console.error('Error exporting data:', error)
      enqueueSnackbar('Failed to export data', { variant: 'error' })
    } finally {
      setLoading(false)
    }
  }

  const toggleDarkMode = () => {
    const newDarkMode = !settings.preferences.darkMode
    handlePreferenceChange('darkMode', newDarkMode)

    // Apply theme change to document
    if (newDarkMode) {
      document.documentElement.setAttribute('data-theme', 'dark')
    } else {
      document.documentElement.setAttribute('data-theme', 'light')
    }
  }

  const settingSections = [
    {
      title: 'Account',
      icon: <AccountCircle />,
      items: [
        {
          title: 'Change Password',
          subtitle: 'Update your password',
          action: 'dialog',
          dialog: 'password'
        }
      ]
    },
    {
      title: 'Notifications',
      icon: <Notifications />,
      items: [
        {
          title: 'Email Notifications',
          subtitle: 'Receive notifications via email',
          action: 'toggle',
          key: 'email',
          value: settings.notifications.email
        },
        {
          title: 'Push Notifications',
          subtitle: 'Receive push notifications',
          action: 'toggle',
          key: 'push',
          value: settings.notifications.push
        },
        {
          title: 'Likes',
          subtitle: 'Notify when someone likes your content',
          action: 'toggle',
          key: 'likes',
          value: settings.notifications.likes
        },
        {
          title: 'Comments',
          subtitle: 'Notify when someone comments',
          action: 'toggle',
          key: 'comments',
          value: settings.notifications.comments
        },
        {
          title: 'New Followers',
          subtitle: 'Notify when someone follows you',
          action: 'toggle',
          key: 'follows',
          value: settings.notifications.follows
        },
        {
          title: 'Messages',
          subtitle: 'Notify when you receive messages',
          action: 'toggle',
          key: 'messages',
          value: settings.notifications.messages
        }
      ]
    },
    {
      title: 'Privacy',
      icon: <Lock />,
      items: [
        {
          title: 'Profile Visibility',
          subtitle: 'Who can see your profile',
          action: 'select',
          key: 'profileVisibility',
          value: settings.privacy.profileVisibility,
          options: [
            { value: 'public', label: 'Public' },
            { value: 'followers', label: 'Followers Only' },
            { value: 'private', label: 'Private' }
          ]
        },
        {
          title: 'Show Email',
          subtitle: 'Display email on profile',
          action: 'toggle',
          key: 'showEmail',
          value: settings.privacy.showEmail
        },
        {
          title: 'Allow Messages',
          subtitle: 'Who can send you messages',
          action: 'select',
          key: 'allowMessages',
          value: settings.privacy.allowMessages,
          options: [
            { value: 'everyone', label: 'Everyone' },
            { value: 'followers', label: 'Followers Only' },
            { value: 'none', label: 'No One' }
          ]
        }
      ]
    },
    {
      title: 'Appearance',
      icon: <Palette />,
      items: [
        {
          title: 'Dark Mode',
          subtitle: 'Use dark theme',
          action: 'toggle',
          key: 'darkMode',
          value: settings.preferences.darkMode,
          handler: toggleDarkMode
        }
      ]
    },
    {
      title: 'Preferences',
      icon: <Language />,
      items: [
        {
          title: 'Language',
          subtitle: 'Choose your language',
          action: 'select',
          key: 'language',
          value: settings.preferences.language,
          options: [
            { value: 'en', label: 'English' },
            { value: 'es', label: 'Spanish' },
            { value: 'fr', label: 'French' },
            { value: 'de', label: 'German' }
          ]
        },
        {
          title: 'Timezone',
          subtitle: 'Your timezone',
          action: 'select',
          key: 'timezone',
          value: settings.preferences.timezone,
          options: [
            { value: 'UTC', label: 'UTC' },
            { value: 'America/New_York', label: 'Eastern Time' },
            { value: 'America/Chicago', label: 'Central Time' },
            { value: 'America/Denver', label: 'Mountain Time' },
            { value: 'America/Los_Angeles', label: 'Pacific Time' }
          ]
        }
      ]
    },
    {
      title: 'Data & Privacy',
      icon: <Security />,
      items: [
        {
          title: 'Export Data',
          subtitle: 'Download your data',
          action: 'button',
          handler: handleDataExport
        },
        {
          title: 'Delete Account',
          subtitle: 'Permanently delete your account',
          action: 'dialog',
          dialog: 'delete',
          danger: true
        }
      ]
    }
  ]

  return (
    <Container maxWidth="md" sx={{ py: 3 }}>
      <Typography variant="h4" component="h1" gutterBottom fontWeight="bold">
        Settings
      </Typography>

      {settingSections.map((section, sectionIndex) => (
        <Card key={sectionIndex} sx={{ mb: 3 }}>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              {section.icon}
              <Typography variant="h6" sx={{ ml: 1 }}>
                {section.title}
              </Typography>
            </Box>

            <List>
              {section.items.map((item, itemIndex) => (
                <React.Fragment key={itemIndex}>
                  <ListItem>
                    <ListItemText
                      primary={item.title}
                      secondary={item.subtitle}
                      sx={{ color: item.danger ? 'error.main' : 'inherit' }}
                    />
                    <ListItemSecondaryAction>
                      {item.action === 'toggle' && (
                        <Switch
                          checked={item.value}
                          onChange={() => {
                            if (item.handler) {
                              item.handler()
                            } else if (section.title === 'Notifications') {
                              handleNotificationChange(item.key)
                            } else if (section.title === 'Privacy') {
                              handlePrivacyChange(item.key, !item.value)
                            }
                          }}
                          disabled={loading}
                        />
                      )}

                      {item.action === 'select' && (
                        <FormControl size="small" sx={{ minWidth: 120 }}>
                          <Select
                            value={item.value}
                            onChange={(e) => {
                              if (section.title === 'Privacy') {
                                handlePrivacyChange(item.key, e.target.value)
                              } else if (section.title === 'Preferences') {
                                handlePreferenceChange(item.key, e.target.value)
                              }
                            }}
                            disabled={loading}
                          >
                            {item.options.map((option) => (
                              <MenuItem key={option.value} value={option.value}>
                                {option.label}
                              </MenuItem>
                            ))}
                          </Select>
                        </FormControl>
                      )}

                      {item.action === 'button' && (
                        <Button
                          variant="outlined"
                          onClick={item.handler}
                          disabled={loading}
                          startIcon={loading ? <CircularProgress size={16} /> : <Download />}
                        >
                          Export
                        </Button>
                      )}

                      {item.action === 'dialog' && (
                        <Button
                          variant={item.danger ? "outlined" : "contained"}
                          color={item.danger ? "error" : "primary"}
                          onClick={() => {
                            if (item.dialog === 'password') {
                              setPasswordDialog(true)
                            } else if (item.dialog === 'delete') {
                              setDeleteDialog(true)
                            }
                          }}
                          startIcon={item.danger ? <Delete /> : <Edit />}
                        >
                          {item.danger ? 'Delete' : 'Change'}
                        </Button>
                      )}
                    </ListItemSecondaryAction>
                  </ListItem>
                  {itemIndex < section.items.length - 1 && <Divider />}
                </React.Fragment>
              ))}
            </List>
          </CardContent>
        </Card>
      ))}

      {/* Password Change Dialog */}
      <Dialog open={passwordDialog} onClose={() => setPasswordDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Change Password</DialogTitle>
        <DialogContent>
          <TextField
            fullWidth
            label="Current Password"
            type={showPasswords.current ? 'text' : 'password'}
            value={passwordForm.currentPassword}
            onChange={(e) => setPasswordForm({ ...passwordForm, currentPassword: e.target.value })}
            margin="normal"
            InputProps={{
              endAdornment: (
                <IconButton
                  onClick={() => setShowPasswords({ ...showPasswords, current: !showPasswords.current })}
                >
                  {showPasswords.current ? <VisibilityOff /> : <Visibility />}
                </IconButton>
              )
            }}
          />
          <TextField
            fullWidth
            label="New Password"
            type={showPasswords.new ? 'text' : 'password'}
            value={passwordForm.newPassword}
            onChange={(e) => setPasswordForm({ ...passwordForm, newPassword: e.target.value })}
            margin="normal"
            InputProps={{
              endAdornment: (
                <IconButton
                  onClick={() => setShowPasswords({ ...showPasswords, new: !showPasswords.new })}
                >
                  {showPasswords.new ? <VisibilityOff /> : <Visibility />}
                </IconButton>
              )
            }}
          />
          <TextField
            fullWidth
            label="Confirm New Password"
            type={showPasswords.confirm ? 'text' : 'password'}
            value={passwordForm.confirmPassword}
            onChange={(e) => setPasswordForm({ ...passwordForm, confirmPassword: e.target.value })}
            margin="normal"
            InputProps={{
              endAdornment: (
                <IconButton
                  onClick={() => setShowPasswords({ ...showPasswords, confirm: !showPasswords.confirm })}
                >
                  {showPasswords.confirm ? <VisibilityOff /> : <Visibility />}
                </IconButton>
              )
            }}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setPasswordDialog(false)}>Cancel</Button>
          <Button
            onClick={handlePasswordChange}
            variant="contained"
            disabled={loading || !passwordForm.currentPassword || !passwordForm.newPassword || !passwordForm.confirmPassword}
            startIcon={loading ? <CircularProgress size={16} /> : <Save />}
          >
            Update Password
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Account Dialog */}
      <Dialog open={deleteDialog} onClose={() => setDeleteDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Delete Account</DialogTitle>
        <DialogContent>
          <Alert severity="error" sx={{ mb: 2 }}>
            This action cannot be undone. All your data will be permanently deleted.
          </Alert>
          <Typography variant="body1">
            Are you sure you want to delete your account? This will permanently remove:
          </Typography>
          <Box component="ul" sx={{ mt: 1 }}>
            <li>Your profile and all personal information</li>
            <li>All your posts, comments, and media</li>
            <li>Your followers and following connections</li>
            <li>All messages and conversations</li>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialog(false)}>Cancel</Button>
          <Button
            onClick={handleDeleteAccount}
            color="error"
            variant="contained"
            disabled={loading}
            startIcon={loading ? <CircularProgress size={16} /> : <Delete />}
          >
            Delete Account
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  )
}

export default SettingsPage
