const express = require('express');
const router = express.Router();
const { protect, optionalAuth } = require('../middleware/auth');
const {
  getFeed,
  getMoodBasedFeed,
  getTrendingFeed,
  getFollowingFeed,
  getForYouFeed,
} = require('../controllers/feedController');

// Public routes
router.get('/', getFeed);
router.get('/trending', getTrendingFeed);

// Routes that work with or without authentication
router.get('/for-you', optionalAuth, getForYouFeed);

// Protected routes
router.use(protect);
router.get('/mood', getMoodBasedFeed);
router.get('/following', getFollowingFeed);

module.exports = router;
