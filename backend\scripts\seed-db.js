const mongoose = require('mongoose');
const dotenv = require('dotenv');
const path = require('path');
const bcrypt = require('bcryptjs');

// Load environment variables
dotenv.config({ path: path.join(__dirname, '..', '.env') });

// Import models
const User = require('../models/User');
const Post = require('../models/Post');
const Reel = require('../models/Reel');
const Comment = require('../models/Comment');
const Like = require('../models/Like');
const Emotion = require('../models/Emotion');
const AREffect = require('../models/AREffect');
const Tag = require('../models/Tag');

// Connect to MongoDB
const connectDB = async () => {
  try {
    const conn = await mongoose.connect(process.env.MONGO_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log(`MongoDB Connected: ${conn.connection.host}`);
    return conn;
  } catch (error) {
    console.error(`Error: ${error.message}`);
    process.exit(1);
  }
};

// Create sample data
const seedDatabase = async () => {
  try {
    // Clear existing data
    await mongoose.connection.db.dropDatabase();
    console.log('Database cleared');

    // Create emotions
    const emotions = [
      {
        name: 'Happy',
        description: 'Feeling of joy and contentment',
        category: 'positive',
        icon: '😊',
        color: '#FFD700',
      },
      {
        name: 'Sad',
        description: 'Feeling of sorrow or unhappiness',
        category: 'negative',
        icon: '😢',
        color: '#4169E1',
      },
      {
        name: 'Excited',
        description: 'Feeling of enthusiasm and eagerness',
        category: 'positive',
        icon: '😃',
        color: '#FF69B4',
      },
      {
        name: 'Angry',
        description: 'Feeling of strong displeasure or hostility',
        category: 'negative',
        icon: '😠',
        color: '#FF0000',
      },
      {
        name: 'Calm',
        description: 'Feeling of peace and tranquility',
        category: 'positive',
        icon: '😌',
        color: '#87CEEB',
      },
    ];

    const createdEmotions = await Emotion.insertMany(emotions);
    console.log(`${createdEmotions.length} emotions created`);

    // Create users
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash('password123', salt);

    const users = [
      {
        username: 'johndoe',
        email: '<EMAIL>',
        password: hashedPassword,
        name: 'John Doe',
        bio: 'Software developer and tech enthusiast',
        website: 'https://johndoe.com',
        profilePicture: 'https://randomuser.me/api/portraits/men/1.jpg',
        isVerified: true,
      },
      {
        username: 'janedoe',
        email: '<EMAIL>',
        password: hashedPassword,
        name: 'Jane Doe',
        bio: 'Digital artist and photographer',
        website: 'https://janedoe.com',
        profilePicture: 'https://randomuser.me/api/portraits/women/1.jpg',
        isVerified: true,
      },
      {
        username: 'bobsmith',
        email: '<EMAIL>',
        password: hashedPassword,
        name: 'Bob Smith',
        bio: 'Travel enthusiast and foodie',
        profilePicture: 'https://randomuser.me/api/portraits/men/2.jpg',
      },
      {
        username: 'alicejones',
        email: '<EMAIL>',
        password: hashedPassword,
        name: 'Alice Jones',
        bio: 'Fitness coach and wellness advocate',
        profilePicture: 'https://randomuser.me/api/portraits/women/2.jpg',
      },
      {
        username: 'mikebrown',
        email: '<EMAIL>',
        password: hashedPassword,
        name: 'Mike Brown',
        bio: 'Music producer and DJ',
        profilePicture: 'https://randomuser.me/api/portraits/men/3.jpg',
      },
    ];

    const createdUsers = await User.insertMany(users);
    console.log(`${createdUsers.length} users created`);

    // Create AR effects
    const arEffects = [
      {
        name: 'Golden Glow',
        description: 'A warm golden filter for your content',
        type: 'filter',
        category: 'beauty',
        thumbnail: 'https://picsum.photos/id/237/300/300',
        assetUrl: 'https://example.com/ar-effects/golden-glow.glb',
        creator: createdUsers[0]._id,
        isPublic: true,
        usageCount: 1250,
        tags: ['gold', 'warm', 'filter'],
        relatedEmotions: [createdEmotions[0]._id, createdEmotions[2]._id],
      },
      {
        name: 'Party Hat',
        description: '3D party hat for celebrations',
        type: 'object',
        category: 'fun',
        thumbnail: 'https://picsum.photos/id/238/300/300',
        assetUrl: 'https://example.com/ar-effects/party-hat.glb',
        creator: createdUsers[1]._id,
        isPublic: true,
        usageCount: 980,
        tags: ['party', 'celebration', '3d'],
        relatedEmotions: [createdEmotions[0]._id, createdEmotions[2]._id],
      },
      {
        name: 'Rainy Day',
        description: 'Rain animation overlay',
        type: 'animation',
        category: 'mood',
        thumbnail: 'https://picsum.photos/id/239/300/300',
        assetUrl: 'https://example.com/ar-effects/rainy-day.glb',
        creator: createdUsers[2]._id,
        isPublic: true,
        usageCount: 750,
        tags: ['rain', 'weather', 'mood'],
        relatedEmotions: [createdEmotions[1]._id, createdEmotions[4]._id],
      },
    ];

    const createdAREffects = await AREffect.insertMany(arEffects);
    console.log(`${createdAREffects.length} AR effects created`);

    // Create posts
    const posts = [];
    for (let i = 0; i < 20; i++) {
      const user = createdUsers[i % createdUsers.length];
      const emotion = createdEmotions[i % createdEmotions.length];
      const arEffect = i % 3 === 0 ? createdAREffects[i % createdAREffects.length] : null;

      posts.push({
        user: user._id,
        caption: `This is post ${i + 1} by ${user.username}. #post #sample`,
        media: [
          {
            url: `https://picsum.photos/id/${(i * 10) + 1}/500/500`,
            type: 'image',
            publicId: `sample_${i + 1}`,
          },
        ],
        location: i % 3 === 0 ? 'New York, NY' : null,
        tags: ['sample', 'post', `tag${i + 1}`],
        emotions: i % 2 === 0 ? [{
          emotion: emotion._id,
          intensity: Math.floor(Math.random() * 10) + 1,
          position: { x: 50, y: 50 },
        }] : [],
        arEffects: arEffect ? [{
          type: arEffect.type,
          effectId: arEffect._id.toString(),
          position: { x: 50, y: 50, z: 0 },
          scale: { x: 1, y: 1, z: 1 },
          rotation: { x: 0, y: 0, z: 0 },
        }] : [],
      });
    }

    const createdPosts = await Post.insertMany(posts);
    console.log(`${createdPosts.length} posts created`);

    // Create reels
    const reels = [];
    for (let i = 0; i < 15; i++) {
      const user = createdUsers[i % createdUsers.length];
      const emotion = createdEmotions[i % createdEmotions.length];
      const arEffect = i % 3 === 0 ? createdAREffects[i % createdAREffects.length] : null;

      reels.push({
        user: user._id,
        caption: `This is reel ${i + 1} by ${user.username}. #reel #sample`,
        video: {
          url: `https://example.com/videos/sample_${i + 1}.mp4`,
          publicId: `sample_video_${i + 1}`,
          duration: 15 + (i % 15), // 15-30 seconds
        },
        thumbnail: {
          url: `https://picsum.photos/id/${(i * 10) + 5}/500/800`,
          publicId: `sample_thumbnail_${i + 1}`,
        },
        audio: {
          name: `Sample Audio ${i + 1}`,
          artist: `Artist ${i + 1}`,
          originalAudio: i % 2 === 0,
        },
        tags: ['sample', 'reel', `tag${i + 1}`],
        emotions: i % 2 === 0 ? [{
          emotion: emotion._id,
          intensity: Math.floor(Math.random() * 10) + 1,
          position: { x: 50, y: 50 },
          timestamp: { start: 0, end: 5 },
        }] : [],
        arEffects: arEffect ? [{
          type: arEffect.type,
          effectId: arEffect._id.toString(),
          position: { x: 50, y: 50, z: 0 },
          scale: { x: 1, y: 1, z: 1 },
          rotation: { x: 0, y: 0, z: 0 },
          timestamp: { start: 0, end: null },
        }] : [],
      });
    }

    const createdReels = await Reel.insertMany(reels);
    console.log(`${createdReels.length} reels created`);

    // Create likes for posts and reels
    const likes = [];
    
    // Add likes to posts
    for (let i = 0; i < createdPosts.length; i++) {
      const post = createdPosts[i];
      const likeCount = Math.floor(Math.random() * 50) + 1;
      
      for (let j = 0; j < likeCount; j++) {
        const user = createdUsers[j % createdUsers.length];
        likes.push({
          user: user._id,
          post: post._id,
        });
      }
    }
    
    // Add likes to reels
    for (let i = 0; i < createdReels.length; i++) {
      const reel = createdReels[i];
      const likeCount = Math.floor(Math.random() * 100) + 1;
      
      for (let j = 0; j < likeCount; j++) {
        const user = createdUsers[j % createdUsers.length];
        likes.push({
          user: user._id,
          reel: reel._id,
        });
      }
    }

    const createdLikes = await Like.insertMany(likes);
    console.log(`${createdLikes.length} likes created`);

    // Create comments for posts and reels
    const comments = [];
    
    // Add comments to posts
    for (let i = 0; i < createdPosts.length; i++) {
      const post = createdPosts[i];
      const commentCount = Math.floor(Math.random() * 10) + 1;
      
      for (let j = 0; j < commentCount; j++) {
        const user = createdUsers[j % createdUsers.length];
        comments.push({
          user: user._id,
          post: post._id,
          text: `This is comment ${j + 1} on post ${i + 1} by ${user.username}.`,
        });
      }
    }
    
    // Add comments to reels
    for (let i = 0; i < createdReels.length; i++) {
      const reel = createdReels[i];
      const commentCount = Math.floor(Math.random() * 15) + 1;
      
      for (let j = 0; j < commentCount; j++) {
        const user = createdUsers[j % createdUsers.length];
        comments.push({
          user: user._id,
          reel: reel._id,
          text: `This is comment ${j + 1} on reel ${i + 1} by ${user.username}.`,
        });
      }
    }

    const createdComments = await Comment.insertMany(comments);
    console.log(`${createdComments.length} comments created`);

    console.log('Database seeded successfully');
  } catch (error) {
    console.error(`Error seeding database: ${error.message}`);
  }
};

// Main function
const main = async () => {
  await connectDB();
  await seedDatabase();
  console.log('Seed script completed');
  process.exit(0);
};

// Run the script
main();
