/**
 * Stream Recording Routes
 * Handles routes for stream recordings
 */

const express = require('express');
const router = express.Router();
const { protect } = require('../middleware/auth');
const multer = require('multer');
const upload = multer({ dest: 'uploads/' });
const streamRecordingController = require('../controllers/streamRecordingController');
const recordingAnalyticsController = require('../controllers/recordingAnalyticsController');

// Create a new stream recording
router.post(
  '/',
  protect,
  upload.single('recordingFile'),
  streamRecordingController.createStreamRecording
);

// Get all recordings for a stream
router.get(
  '/stream/:streamId',
  streamRecordingController.getStreamRecordings
);

// Get all recording categories
router.get(
  '/categories',
  streamRecordingController.getCategories
);

// Get a single stream recording
router.get(
  '/:id',
  streamRecordingController.getStreamRecording
);

// Update a stream recording
router.put(
  '/:id',
  protect,
  upload.single('thumbnail'),
  streamRecordingController.updateStreamRecording
);

// Edit a stream recording (with video processing)
router.put(
  '/:id/edit',
  protect,
  upload.single('thumbnail'),
  streamRecordingController.editRecording
);

// Delete a stream recording
router.delete(
  '/:id',
  protect,
  streamRecordingController.deleteStreamRecording
);

// Analytics routes
// Track view
router.post(
  '/:id/analytics/view',
  recordingAnalyticsController.trackView
);

// Track engagement (like, comment, share, etc.)
router.post(
  '/:id/analytics/engagement',
  recordingAnalyticsController.trackEngagement
);

// Get analytics (owner only)
router.get(
  '/:id/analytics',
  protect,
  recordingAnalyticsController.getAnalytics
);

// Like/unlike a recording
router.post(
  '/:id/like',
  protect,
  streamRecordingController.toggleLike
);

module.exports = router;
