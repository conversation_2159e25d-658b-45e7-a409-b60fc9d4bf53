const express = require('express');
const router = express.Router();
const { protect, authorize } = require('../middleware/auth');
const {
  getVendorBalance,
  getCommissionAnalytics,
  createPayoutRequest,
  getPayoutHistory,
  getPayoutRequestDetails,
  cancelPayoutRequest,
  setupAutomaticPayouts,
  getSupportedCurrencies,
  getExchangeRates,
  getAllPayoutRequests,
  processPayoutRequest,
  rejectPayoutRequest,
  getPlatformAnalytics
} = require('../controllers/paymentsPayoutsController');

// Public routes
router.get('/currencies', getSupportedCurrencies);
router.get('/exchange-rates/:fromCurrency', getExchangeRates);

// Test route to verify payments system
router.get('/test', async (req, res) => {
  try {
    const Commission = require('../models/Commission');
    const PayoutRequest = require('../models/PayoutRequest');
    const Currency = require('../models/Currency');

    const stats = {
      commissions: await Commission.countDocuments(),
      payoutRequests: await PayoutRequest.countDocuments(),
      currencies: await Currency.countDocuments(),
      timestamp: new Date().toISOString()
    };

    res.status(200).json({
      success: true,
      message: 'Payments & Payouts Engine is working correctly!',
      data: stats
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error testing payments system',
      error: error.message
    });
  }
});

// Protected routes (require authentication)
router.use(protect);

// Vendor routes
router.get('/balance', getVendorBalance);
router.get('/analytics', getCommissionAnalytics);
router.get('/payout-history', getPayoutHistory);
router.post('/payout-request', createPayoutRequest);
router.get('/payout-request/:id', getPayoutRequestDetails);
router.put('/payout-request/:id/cancel', cancelPayoutRequest);
router.post('/automatic-payouts', setupAutomaticPayouts);

// Admin routes
router.get('/admin/payout-requests', authorize('admin'), getAllPayoutRequests);
router.put('/admin/payout-requests/:id/process', authorize('admin'), processPayoutRequest);
router.put('/admin/payout-requests/:id/reject', authorize('admin'), rejectPayoutRequest);
router.get('/admin/analytics', authorize('admin'), getPlatformAnalytics);

module.exports = router;
