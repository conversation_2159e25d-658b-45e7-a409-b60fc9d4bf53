import React, { createContext, useContext, useReducer, useEffect } from 'react'
import axios from '../utils/fixedAxios'

const AuthContext = createContext()

const initialState = {
  user: null,
  token: localStorage.getItem('token'),
  isAuthenticated: false,
  loading: true,
  error: null
}

const authReducer = (state, action) => {
  switch (action.type) {
    case 'USER_LOADED':
      return {
        ...state,
        isAuthenticated: true,
        loading: false,
        user: action.payload,
        error: null
      }
    case 'LOGIN_SUCCESS':
    case 'REGISTER_SUCCESS':
      localStorage.setItem('token', action.payload.token)
      return {
        ...state,
        ...action.payload,
        isAuthenticated: true,
        loading: false,
        error: null
      }
    case 'AUTH_ERROR':
    case 'LOGIN_FAIL':
    case 'REGISTER_FAIL':
    case 'LOGOUT':
      localStorage.removeItem('token')
      return {
        ...state,
        token: null,
        isAuthenticated: false,
        loading: false,
        user: null,
        error: action.payload
      }
    case 'CLEAR_ERRORS':
      return {
        ...state,
        error: null
      }
    case 'SET_LOADING':
      return {
        ...state,
        loading: action.payload
      }
    default:
      return state
  }
}

export const AuthProvider = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, initialState)

  // Load user
  const loadUser = async () => {
    if (localStorage.token) {
      try {
        const res = await axios.get('/api/auth/me')
        dispatch({
          type: 'USER_LOADED',
          payload: res.data.data
        })
      } catch (err) {
        dispatch({
          type: 'AUTH_ERROR',
          payload: err.response?.data?.message || 'Authentication failed'
        })
      }
    } else {
      dispatch({ type: 'SET_LOADING', payload: false })
    }
  }

  // Register user
  const register = async (formData) => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true })
      const res = await axios.post('/api/auth/register', formData)
      dispatch({
        type: 'REGISTER_SUCCESS',
        payload: res.data
      })
      loadUser()
    } catch (err) {
      dispatch({
        type: 'REGISTER_FAIL',
        payload: err.response?.data?.message || 'Registration failed'
      })
    }
  }

  // Login user
  const login = async (formData) => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true })
      const res = await axios.post('/api/auth/login', formData)
      dispatch({
        type: 'LOGIN_SUCCESS',
        payload: res.data
      })
      loadUser()
    } catch (err) {
      dispatch({
        type: 'LOGIN_FAIL',
        payload: err.response?.data?.message || 'Login failed'
      })
    }
  }

  // Logout
  const logout = () => {
    dispatch({ type: 'LOGOUT' })
  }

  // Clear errors
  const clearErrors = () => {
    dispatch({ type: 'CLEAR_ERRORS' })
  }

  // Update user
  const updateUser = (userData) => {
    dispatch({
      type: 'USER_LOADED',
      payload: userData
    })
  }

  useEffect(() => {
    loadUser()
  }, [])

  return (
    <AuthContext.Provider
      value={{
        ...state,
        register,
        login,
        logout,
        clearErrors,
        updateUser,
        loadUser
      }}
    >
      {children}
    </AuthContext.Provider>
  )
}

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

export default AuthContext
