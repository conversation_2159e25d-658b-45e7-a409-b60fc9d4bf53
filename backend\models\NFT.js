const mongoose = require('mongoose');

const NFTSchema = new mongoose.Schema({
  title: {
    type: String,
    required: [true, 'Please add a title'],
    trim: true,
    maxlength: [100, 'Title cannot be more than 100 characters']
  },
  description: {
    type: String,
    required: [true, 'Please add a description'],
    maxlength: [1000, 'Description cannot be more than 1000 characters']
  },
  imageUrl: {
    type: String,
    required: [true, 'Please add an image URL']
  },
  contentUrl: {
    type: String,
    required: [true, 'Please add a content URL']
  },
  tokenId: {
    type: String,
    required: [true, 'Please add a token ID']
    // Removed unique: true to avoid duplicate index
  },
  contractAddress: {
    type: String,
    required: [true, 'Please add a contract address']
  },
  blockchain: {
    type: String,
    enum: ['ethereum', 'polygon', 'solana', 'binance'],
    default: 'polygon'
  },
  creator: {
    type: mongoose.Schema.ObjectId,
    ref: 'User',
    required: true
  },
  owner: {
    type: mongoose.Schema.ObjectId,
    ref: 'User',
    required: true
  },
  stream: {
    type: mongoose.Schema.ObjectId,
    ref: 'LiveStream'
  },
  clip: {
    type: mongoose.Schema.ObjectId,
    ref: 'Clip'
  },
  price: {
    type: Number
  },
  forSale: {
    type: Boolean,
    default: false
  },
  mintedAt: {
    type: Date,
    default: Date.now
  }
});

// Create index for faster queries
NFTSchema.index({ creator: 1 });
NFTSchema.index({ owner: 1 });
NFTSchema.index({ stream: 1 });
NFTSchema.index({ forSale: 1 });
NFTSchema.index({ tokenId: 1 }, { unique: true });

module.exports = mongoose.model('NFT', NFTSchema);
