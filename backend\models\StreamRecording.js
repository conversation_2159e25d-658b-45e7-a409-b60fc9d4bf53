/**
 * Stream Recording Model
 * Represents a recording of a live stream
 */

const mongoose = require('mongoose');

const StreamRecordingSchema = new mongoose.Schema(
  {
    stream: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'LiveStream',
      required: true
    },
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    title: {
      type: String,
      trim: true
    },
    description: {
      type: String,
      trim: true
    },
    url: {
      type: String,
      required: true
    },
    publicId: {
      type: String,
      required: true
    },
    thumbnail: {
      url: String,
      publicId: String
    },
    duration: {
      type: Number,
      default: 0
    },
    size: {
      type: Number,
      default: 0
    },
    format: {
      type: String
    },
    views: {
      type: Number,
      default: 0
    },
    likes: {
      type: Number,
      default: 0
    },
    visibility: {
      type: String,
      enum: ['public', 'private', 'unlisted'],
      default: 'public'
    },
    status: {
      type: String,
      enum: ['processing', 'published', 'failed'],
      default: 'processing'
    },
    analytics: {
      viewsHistory: [{
        date: {
          type: Date,
          default: Date.now
        },
        count: {
          type: Number,
          default: 1
        }
      }],
      totalWatchTime: {
        type: Number,
        default: 0
      },
      averageWatchTime: {
        type: Number,
        default: 0
      },
      completionRate: {
        type: Number,
        default: 0
      },
      engagement: {
        likes: {
          type: Number,
          default: 0
        },
        comments: {
          type: Number,
          default: 0
        },
        shares: {
          type: Number,
          default: 0
        }
      },
      demographics: {
        countries: [{
          country: String,
          count: Number
        }],
        devices: [{
          device: String,
          count: Number
        }],
        browsers: [{
          browser: String,
          count: Number
        }]
      },
      referrers: [{
        source: String,
        count: Number
      }]
    },
    category: {
      type: String,
      default: 'general'
    }
  },
  { timestamps: true }
);

// Add virtual field for formatted duration
StreamRecordingSchema.virtual('formattedDuration').get(function() {
  const minutes = Math.floor(this.duration / 60);
  const seconds = this.duration % 60;
  return `${minutes}:${seconds.toString().padStart(2, '0')}`;
});

// Add virtual field for formatted size
StreamRecordingSchema.virtual('formattedSize').get(function() {
  const kb = 1024;
  const mb = kb * 1024;
  const gb = mb * 1024;

  if (this.size < kb) {
    return `${this.size} B`;
  } else if (this.size < mb) {
    return `${(this.size / kb).toFixed(2)} KB`;
  } else if (this.size < gb) {
    return `${(this.size / mb).toFixed(2)} MB`;
  } else {
    return `${(this.size / gb).toFixed(2)} GB`;
  }
});

// Include virtuals when converting to JSON
StreamRecordingSchema.set('toJSON', { virtuals: true });
StreamRecordingSchema.set('toObject', { virtuals: true });

module.exports = mongoose.model('StreamRecording', StreamRecordingSchema);
