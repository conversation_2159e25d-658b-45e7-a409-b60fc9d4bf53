const jwt = require('jsonwebtoken');
const User = require('../models/User');

// Store active users
const activeUsers = new Map();

module.exports = (io) => {
  // Middleware for authentication
  io.use(async (socket, next) => {
    try {
      const token = socket.handshake.auth.token || socket.handshake.query.token;

      if (!token) {
        console.log('Socket connection attempt without token');
        // Allow connection without authentication for testing
        socket.user = {
          _id: 'guest',
          username: 'Guest User',
          avatar: null
        };
        return next();
      }

      try {
        // Verify token
        const decoded = jwt.verify(token, process.env.JWT_SECRET);

        // Get user from token
        const user = await User.findById(decoded.id);

        if (!user) {
          console.warn('Socket auth: User not found for token');
          // Allow connection without authentication for testing
          socket.user = {
            _id: 'guest',
            username: 'Guest User',
            avatar: null
          };
          return next();
        }

        // Attach user to socket
        socket.user = {
          _id: user._id,
          username: user.username,
          avatar: user.avatar
        };

        console.log(`Socket authenticated for user: ${user.username}`);
        next();
      } catch (tokenError) {
        console.error('Token verification error:', tokenError.message);
        // Allow connection without authentication for testing
        socket.user = {
          _id: 'guest',
          username: 'Guest User',
          avatar: null
        };
        next();
      }
    } catch (error) {
      console.error('Socket authentication error:', error.message);
      // Allow connection without authentication for testing
      socket.user = {
        _id: 'guest',
        username: 'Guest User',
        avatar: null
      };
      next();
    }
  });

  io.on('connection', (socket) => {
    console.log(`User connected: ${socket.user?.username || 'Unknown'} (${socket.user?._id || 'Unknown'})`);

    // Send a welcome message to confirm connection
    socket.emit('welcome', { message: 'Connected to socket server', timestamp: new Date() });

    // Only add authenticated users to active users
    if (socket.user && socket.user._id !== 'guest') {
      // Add user to active users
      activeUsers.set(socket.user._id.toString(), {
        socketId: socket.id,
        user: socket.user,
        lastActive: new Date()
      });

      // Emit active users list
      io.emit('activeUsers', Array.from(activeUsers.values()).map(u => u.user));

      // Join user's personal room for direct messages
      socket.join(socket.user._id.toString());
    }

    // Handle disconnect
    socket.on('disconnect', (reason) => {
      console.log(`User disconnected: ${socket.user?.username || 'Unknown'} (${socket.user?._id || 'Unknown'}), reason: ${reason}`);

      if (socket.user && socket.user._id !== 'guest') {
        activeUsers.delete(socket.user._id.toString());
        io.emit('activeUsers', Array.from(activeUsers.values()).map(u => u.user));
      }
    });

    // Handle typing events
    socket.on('typing', (data) => {
      // Skip for guest users
      if (socket.user._id === 'guest') return;

      const { conversationId, isTyping } = data;

      if (conversationId) {
        socket.to(conversationId).emit('userTyping', {
          user: socket.user,
          isTyping,
          conversationId
        });
      }
    });

    // Handle new message
    socket.on('newMessage', (data) => {
      // Skip for guest users
      if (socket.user._id === 'guest') return;

      const { conversationId, message } = data;

      if (conversationId && message) {
        socket.to(conversationId).emit('messageReceived', {
          message,
          sender: socket.user
        });
      }
    });

    // Handle new notification
    socket.on('newNotification', (data) => {
      // Skip for guest users
      if (socket.user._id === 'guest') return;

      const { recipientId, notification } = data;

      if (recipientId && notification) {
        socket.to(recipientId).emit('notificationReceived', notification);
      }
    });

    // Add a ping/pong mechanism to keep the connection alive
    socket.on('ping', () => {
      socket.emit('pong', { timestamp: new Date() });
    });
  });
};
