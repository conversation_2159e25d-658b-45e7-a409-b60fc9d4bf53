const mongoose = require('mongoose');

const promotionSchema = new mongoose.Schema({
  title: {
    type: String,
    required: true,
    maxlength: 100,
    trim: true
  },
  description: {
    type: String,
    required: true,
    maxlength: 500,
    trim: true
  },
  type: {
    type: String,
    enum: ['percentage', 'fixed_amount', 'buy_one_get_one', 'free_shipping', 'flash_sale'],
    required: true,
    index: true
  },
  discountPercentage: {
    type: Number,
    min: 0,
    max: 100,
    validate: {
      validator: function(value) {
        return this.type !== 'percentage' || (value >= 0 && value <= 100);
      },
      message: 'Discount percentage must be between 0 and 100'
    }
  },
  discountAmount: {
    type: Number,
    min: 0,
    validate: {
      validator: function(value) {
        return this.type !== 'fixed_amount' || value > 0;
      },
      message: 'Discount amount must be greater than 0'
    }
  },
  minimumOrderValue: {
    type: Number,
    min: 0,
    default: 0
  },
  maximumDiscountAmount: {
    type: Number,
    min: 0
  },
  usageLimit: {
    total: {
      type: Number,
      min: 1,
      default: null // null means unlimited
    },
    perUser: {
      type: Number,
      min: 1,
      default: 1
    }
  },
  usageCount: {
    type: Number,
    default: 0,
    min: 0
  },
  code: {
    type: String,
    unique: true,
    sparse: true,
    uppercase: true,
    trim: true,
    validate: {
      validator: function(value) {
        return !value || /^[A-Z0-9]{3,20}$/.test(value);
      },
      message: 'Promotion code must be 3-20 characters long and contain only uppercase letters and numbers'
    }
  },
  startDate: {
    type: Date,
    required: true,
    index: true
  },
  endDate: {
    type: Date,
    required: true,
    index: true,
    validate: {
      validator: function(value) {
        return value > this.startDate;
      },
      message: 'End date must be after start date'
    }
  },
  status: {
    type: String,
    enum: ['draft', 'active', 'paused', 'expired', 'cancelled'],
    default: 'draft',
    index: true
  },
  targetAudience: {
    userRoles: [{
      type: String,
      enum: ['user', 'vendor', 'admin']
    }],
    userTags: [{
      type: String
    }],
    newUsersOnly: {
      type: Boolean,
      default: false
    },
    returningUsersOnly: {
      type: Boolean,
      default: false
    }
  },
  applicableProducts: {
    type: {
      type: String,
      enum: ['all', 'specific', 'category', 'vendor'],
      default: 'all'
    },
    productIds: [{
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Product'
    }],
    categoryIds: [{
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Category'
    }],
    vendorIds: [{
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Vendor'
    }]
  },
  conditions: {
    firstTimeUser: {
      type: Boolean,
      default: false
    },
    minimumItems: {
      type: Number,
      min: 1
    },
    excludedProducts: [{
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Product'
    }],
    requiredProducts: [{
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Product'
    }]
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  analytics: {
    views: {
      type: Number,
      default: 0
    },
    clicks: {
      type: Number,
      default: 0
    },
    conversions: {
      type: Number,
      default: 0
    },
    revenue: {
      type: Number,
      default: 0
    }
  },
  metadata: {
    priority: {
      type: Number,
      default: 0,
      min: 0,
      max: 10
    },
    tags: [{
      type: String
    }],
    internalNotes: {
      type: String,
      maxlength: 1000
    }
  }
}, {
  timestamps: true
});

// Indexes for performance
promotionSchema.index({ status: 1, startDate: 1, endDate: 1 });
promotionSchema.index({ code: 1 }, { unique: true, sparse: true });
promotionSchema.index({ type: 1, status: 1 });
promotionSchema.index({ createdBy: 1, status: 1 });
promotionSchema.index({ 'targetAudience.userRoles': 1 });

// Virtual for active status
promotionSchema.virtual('isActive').get(function() {
  const now = new Date();
  return this.status === 'active' && 
         this.startDate <= now && 
         this.endDate >= now &&
         (this.usageLimit.total === null || this.usageCount < this.usageLimit.total);
});

// Virtual for expired status
promotionSchema.virtual('isExpired').get(function() {
  return new Date() > this.endDate;
});

// Virtual for usage percentage
promotionSchema.virtual('usagePercentage').get(function() {
  if (!this.usageLimit.total) return 0;
  return Math.round((this.usageCount / this.usageLimit.total) * 100);
});

// Instance methods
promotionSchema.methods.canBeUsedBy = function(user) {
  if (!this.isActive) return false;
  
  // Check user role
  if (this.targetAudience.userRoles.length > 0 && 
      !this.targetAudience.userRoles.includes(user.role)) {
    return false;
  }
  
  // Check new users only
  if (this.targetAudience.newUsersOnly) {
    const daysSinceRegistration = (new Date() - user.createdAt) / (1000 * 60 * 60 * 24);
    if (daysSinceRegistration > 30) return false; // Consider new if registered within 30 days
  }
  
  return true;
};

promotionSchema.methods.calculateDiscount = function(orderValue, items = []) {
  if (!this.isActive) return 0;
  
  // Check minimum order value
  if (orderValue < this.minimumOrderValue) return 0;
  
  let discount = 0;
  
  switch (this.type) {
    case 'percentage':
      discount = (orderValue * this.discountPercentage) / 100;
      if (this.maximumDiscountAmount) {
        discount = Math.min(discount, this.maximumDiscountAmount);
      }
      break;
      
    case 'fixed_amount':
      discount = Math.min(this.discountAmount, orderValue);
      break;
      
    case 'free_shipping':
      // This would be handled separately in shipping calculation
      discount = 0;
      break;
      
    default:
      discount = 0;
  }
  
  return Math.round(discount * 100) / 100; // Round to 2 decimal places
};

promotionSchema.methods.incrementUsage = function() {
  this.usageCount += 1;
  this.analytics.conversions += 1;
  return this.save();
};

promotionSchema.methods.addRevenue = function(amount) {
  this.analytics.revenue += amount;
  return this.save();
};

// Static methods
promotionSchema.statics.getActivePromotions = function(userRole = null) {
  const now = new Date();
  let query = {
    status: 'active',
    startDate: { $lte: now },
    endDate: { $gte: now }
  };
  
  if (userRole) {
    query['targetAudience.userRoles'] = userRole;
  }
  
  return this.find(query).sort({ 'metadata.priority': -1, createdAt: -1 });
};

promotionSchema.statics.findByCode = function(code) {
  return this.findOne({ 
    code: code.toUpperCase(),
    status: 'active',
    startDate: { $lte: new Date() },
    endDate: { $gte: new Date() }
  });
};

promotionSchema.statics.getPromotionStats = function(promotionId) {
  return this.findById(promotionId).select('analytics usageCount usageLimit');
};

// Pre-save middleware
promotionSchema.pre('save', function(next) {
  // Auto-generate code if not provided
  if (!this.code && this.type !== 'flash_sale') {
    this.code = this.generateCode();
  }
  
  // Auto-expire if past end date
  if (this.status === 'active' && new Date() > this.endDate) {
    this.status = 'expired';
  }
  
  // Auto-pause if usage limit reached
  if (this.status === 'active' && 
      this.usageLimit.total && 
      this.usageCount >= this.usageLimit.total) {
    this.status = 'paused';
  }
  
  next();
});

// Helper method to generate promotion code
promotionSchema.methods.generateCode = function() {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let code = '';
  for (let i = 0; i < 8; i++) {
    code += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return code;
};

module.exports = mongoose.model('Promotion', promotionSchema);
