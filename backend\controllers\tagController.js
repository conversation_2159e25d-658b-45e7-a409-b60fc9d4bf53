const Tag = require('../models/Tag');
const Post = require('../models/Post');
const Reel = require('../models/Reel');
const ErrorResponse = require('../utils/errorResponse');

/**
 * Get all tags
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.getTags = async (req, res, next) => {
  try {
    const { limit = 20, page = 1 } = req.query;
    const skip = (page - 1) * limit;

    const tags = await Tag.find()
      .sort({ count: -1 })
      .skip(skip)
      .limit(parseInt(limit));

    res.status(200).json({
      success: true,
      data: tags,
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Get trending tags
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.getTrendingTags = async (req, res, next) => {
  try {
    const { limit = 10 } = req.query;

    const tags = await Tag.find()
      .sort({ count: -1 })
      .limit(parseInt(limit));

    res.status(200).json({
      success: true,
      data: tags,
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Get a single tag
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.getTag = async (req, res, next) => {
  try {
    const tag = await Tag.findOne({ name: req.params.name.toLowerCase() });

    if (!tag) {
      return next(new ErrorResponse(`Tag not found with name ${req.params.name}`, 404));
    }

    res.status(200).json({
      success: true,
      data: tag,
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Search tags
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.searchTags = async (req, res, next) => {
  try {
    const { query } = req.query;

    if (!query) {
      return next(new ErrorResponse('Please provide a search query', 400));
    }

    const tags = await Tag.find({
      name: { $regex: query, $options: 'i' },
    })
      .sort({ count: -1 })
      .limit(20);

    res.status(200).json({
      success: true,
      data: tags,
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Get content by tag
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.getContentByTag = async (req, res, next) => {
  try {
    const { name } = req.params;
    const { limit = 20, page = 1 } = req.query;
    const skip = (page - 1) * limit;

    // Find posts and reels with this tag
    const [posts, reels] = await Promise.all([
      Post.find({ tags: name.toLowerCase() })
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(parseInt(limit))
        .populate('user', 'username name profilePicture isVerified')
        .populate('emotions.emotion', 'name color icon category'),

      Reel.find({ tags: name.toLowerCase() })
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(parseInt(limit))
        .populate('user', 'username name profilePicture isVerified')
        .populate('emotions.emotion', 'name color icon category'),
    ]);

    res.status(200).json({
      success: true,
      data: {
        posts,
        reels,
      },
    });
  } catch (err) {
    next(err);
  }
};
