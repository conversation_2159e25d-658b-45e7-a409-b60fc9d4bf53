const mongoose = require('mongoose');

const LiveStreamReactionSchema = new mongoose.Schema({
  stream: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'LiveStream',
    required: true,
  },
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  type: {
    type: String,
    enum: ['like', 'love', 'wow', 'haha', 'sad', 'angry', 'gift', 'custom'],
    required: true,
  },
  emoji: {
    type: String,
  },
  giftType: {
    type: String,
    enum: ['basic', 'premium', 'exclusive', 'custom'],
  },
  giftValue: {
    type: Number, // Value in platform currency
  },
  giftAnimation: {
    type: String,
  },
  message: {
    type: String,
    maxlength: [100, 'Message cannot be more than 100 characters'],
  },
  isHighlighted: {
    type: Boolean,
    default: false,
  },
  emotions: [
    {
      emotion: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Emotion',
      },
      intensity: {
        type: Number,
        min: 1,
        max: 10,
        default: 5,
      },
    },
  ],
}, { timestamps: true });

// Create indexes for efficient queries
LiveStreamReactionSchema.index({ stream: 1, createdAt: 1 });
LiveStreamReactionSchema.index({ user: 1, stream: 1 });
LiveStreamReactionSchema.index({ stream: 1, type: 1 });

module.exports = mongoose.model('LiveStreamReaction', LiveStreamReactionSchema);
