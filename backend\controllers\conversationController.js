const Conversation = require('../models/Conversation');
const Message = require('../models/Message');
const User = require('../models/User');
const { createError } = require('../utils/error');
const socketEmitter = require('../utils/socketEmitter');
const { cloudinary, uploadToCloudinary } = require('../config/cloudinary');

/**
 * Get all conversations for the current user
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.getConversations = async (req, res, next) => {
  try {
    // Get conversations where user is a participant
    const conversations = await Conversation.find({
      participants: req.user.id,
    })
      .sort({ updatedAt: -1 })
      .populate('participants', 'username name profilePicture')
      .populate('lastMessage')
      .populate('groupAdmin', 'username name profilePicture')
      .lean();

    // Process conversations to add additional info
    const processedConversations = conversations.map(conversation => {
      // For one-on-one conversations, get the other participant
      let otherParticipant = null;
      if (!conversation.isGroup) {
        otherParticipant = conversation.participants.find(
          p => p._id.toString() !== req.user.id
        );
      }

      // Get unread count for current user
      const unreadCount = conversation.unreadCount?.[req.user.id] || 0;

      // Check if conversation is muted by current user
      const isMuted = conversation.muted?.some(mute => {
        return mute.user.toString() === req.user.id &&
               (!mute.until || new Date(mute.until) > new Date());
      });

      // Check if conversation is pinned by current user
      const isPinned = conversation.pinnedBy?.some(id => id.toString() === req.user.id);

      return {
        ...conversation,
        otherParticipant,
        unreadCount,
        isMuted,
        isPinned,
      };
    });

    // Sort by pinned status first, then by last message date
    processedConversations.sort((a, b) => {
      if (a.isPinned && !b.isPinned) return -1;
      if (!a.isPinned && b.isPinned) return 1;
      return new Date(b.updatedAt) - new Date(a.updatedAt);
    });

    res.status(200).json({
      success: true,
      data: processedConversations,
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Get a single conversation by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.getConversation = async (req, res, next) => {
  try {
    const { conversationId } = req.params;

    // Get conversation
    const conversation = await Conversation.findById(conversationId)
      .populate('participants', 'username name profilePicture')
      .populate('lastMessage')
      .populate('groupAdmin', 'username name profilePicture')
      .populate({
        path: 'groupMembers.user',
        select: 'username name profilePicture',
      })
      .populate({
        path: 'groupMembers.addedBy',
        select: 'username name',
      });

    if (!conversation) {
      return next(createError(404, 'Conversation not found'));
    }

    // Check if user is a participant
    if (!conversation.isParticipant(req.user.id)) {
      return next(createError(403, 'You are not a participant in this conversation'));
    }

    // Get other participant for one-on-one conversations
    let otherParticipant = null;
    if (!conversation.isGroup) {
      otherParticipant = conversation.participants.find(
        p => p._id.toString() !== req.user.id
      );
    }

    // Get unread count for current user
    const unreadCount = conversation.unreadCount?.[req.user.id] || 0;

    // Check if conversation is muted by current user
    const isMuted = conversation.muted?.some(mute => {
      return mute.user.toString() === req.user.id &&
             (!mute.until || new Date(mute.until) > new Date());
    });

    // Check if conversation is pinned by current user
    const isPinned = conversation.pinnedBy?.some(id => id.toString() === req.user.id);

    // Get pinned messages
    const pinnedMessages = await Message.find({
      conversation: conversationId,
      isPinned: true,
      isDeleted: { $ne: true },
    })
      .populate('sender', 'username name profilePicture')
      .sort({ createdAt: -1 });

    res.status(200).json({
      success: true,
      data: {
        ...conversation.toObject(),
        otherParticipant,
        unreadCount,
        isMuted,
        isPinned,
        pinnedMessages,
      },
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Create a new one-on-one conversation
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.createOneOnOneConversation = async (req, res, next) => {
  try {
    const { userId } = req.body;

    if (!userId) {
      return next(createError(400, 'User ID is required'));
    }

    // Check if user exists
    const user = await User.findById(userId);
    if (!user) {
      return next(createError(404, 'User not found'));
    }

    // Check if conversation already exists
    const existingConversation = await Conversation.findOne({
      isGroup: false,
      participants: { $all: [req.user.id, userId], $size: 2 },
    })
      .populate('participants', 'username name profilePicture')
      .populate('lastMessage');

    if (existingConversation) {
      return res.status(200).json({
        success: true,
        data: existingConversation,
        message: 'Conversation already exists',
      });
    }

    // Create new conversation
    const newConversation = await Conversation.create({
      participants: [req.user.id, userId],
      isGroup: false,
    });

    // Populate participant details
    await newConversation.populate('participants', 'username name profilePicture');

    res.status(201).json({
      success: true,
      data: newConversation,
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Create a new group conversation
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.createGroupConversation = async (req, res, next) => {
  try {
    const { name, participants } = req.body;
    let groupImage = null;

    if (!name) {
      return next(createError(400, 'Group name is required'));
    }

    if (!participants || !Array.isArray(participants) || participants.length === 0) {
      return next(createError(400, 'At least one participant is required'));
    }

    // Check if all participants exist
    const users = await User.find({ _id: { $in: participants } });
    if (users.length !== participants.length) {
      return next(createError(404, 'One or more users not found'));
    }

    // Handle group image upload if any
    if (req.file) {
      const result = await cloudinary.uploader.upload(req.file.path, {
        folder: 'letstalk/groups',
      });
      groupImage = result.secure_url;
    }

    // Create group members array with current user as admin
    const groupMembers = [
      {
        user: req.user.id,
        role: 'admin',
        addedBy: req.user.id,
      },
      ...participants.map(userId => ({
        user: userId,
        role: 'member',
        addedBy: req.user.id,
      })),
    ];

    // Create new group conversation
    const newConversation = await Conversation.create({
      name,
      participants: [req.user.id, ...participants],
      isGroup: true,
      groupAdmin: req.user.id,
      groupImage,
      groupMembers,
    });

    // Populate participant details
    await newConversation.populate('participants', 'username name profilePicture');
    await newConversation.populate('groupAdmin', 'username name profilePicture');
    await newConversation.populate({
      path: 'groupMembers.user',
      select: 'username name profilePicture',
    });

    // Emit group creation to all participants
    socketEmitter.emitGroupCreated(newConversation);

    res.status(201).json({
      success: true,
      data: newConversation,
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Add participants to a group conversation
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.addParticipants = async (req, res, next) => {
  try {
    const { conversationId } = req.params;
    const { participants } = req.body;

    if (!participants || !Array.isArray(participants) || participants.length === 0) {
      return next(createError(400, 'At least one participant is required'));
    }

    // Get conversation
    const conversation = await Conversation.findById(conversationId);
    if (!conversation) {
      return next(createError(404, 'Conversation not found'));
    }

    // Check if it's a group conversation
    if (!conversation.isGroup) {
      return next(createError(400, 'Cannot add participants to a one-on-one conversation'));
    }

    // Check if user is an admin
    if (!conversation.isGroupAdmin(req.user.id)) {
      return next(createError(403, 'Only group admins can add participants'));
    }

    // Check if all participants exist
    const users = await User.find({ _id: { $in: participants } });
    if (users.length !== participants.length) {
      return next(createError(404, 'One or more users not found'));
    }

    // Filter out users who are already participants
    const newParticipants = participants.filter(
      userId => !conversation.participants.includes(userId)
    );

    if (newParticipants.length === 0) {
      return next(createError(400, 'All users are already participants'));
    }

    // Add new participants
    conversation.participants.push(...newParticipants);

    // Add new group members
    newParticipants.forEach(userId => {
      conversation.groupMembers.push({
        user: userId,
        role: 'member',
        addedBy: req.user.id,
      });
    });

    await conversation.save();

    // Populate participant details
    await conversation.populate('participants', 'username name profilePicture');
    await conversation.populate('groupAdmin', 'username name profilePicture');
    await conversation.populate({
      path: 'groupMembers.user',
      select: 'username name profilePicture',
    });

    // Emit participant addition to all participants
    socketEmitter.emitParticipantsAdded(conversation, newParticipants, req.user.id);

    res.status(200).json({
      success: true,
      data: conversation,
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Remove a participant from a group conversation
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.removeParticipant = async (req, res, next) => {
  try {
    const { conversationId, userId } = req.params;

    // Get conversation
    const conversation = await Conversation.findById(conversationId);
    if (!conversation) {
      return next(createError(404, 'Conversation not found'));
    }

    // Check if it's a group conversation
    if (!conversation.isGroup) {
      return next(createError(400, 'Cannot remove participants from a one-on-one conversation'));
    }

    // Check if user is an admin or removing themselves
    if (!conversation.isGroupAdmin(req.user.id) && req.user.id !== userId) {
      return next(createError(403, 'Only group admins can remove other participants'));
    }

    // Check if user is the only admin
    if (
      conversation.isGroupAdmin(userId) &&
      conversation.groupMembers.filter(member => member.role === 'admin').length === 1
    ) {
      return next(createError(400, 'Cannot remove the only admin from the group'));
    }

    // Remove participant
    conversation.participants = conversation.participants.filter(
      id => id.toString() !== userId
    );

    // Remove from group members
    conversation.groupMembers = conversation.groupMembers.filter(
      member => member.user.toString() !== userId
    );

    await conversation.save();

    // Populate participant details
    await conversation.populate('participants', 'username name profilePicture');
    await conversation.populate('groupAdmin', 'username name profilePicture');
    await conversation.populate({
      path: 'groupMembers.user',
      select: 'username name profilePicture',
    });

    // Emit participant removal to all participants
    socketEmitter.emitParticipantRemoved(conversation, userId, req.user.id);

    res.status(200).json({
      success: true,
      data: conversation,
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Leave a group conversation
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.leaveGroup = async (req, res, next) => {
  try {
    const { conversationId } = req.params;

    // Get conversation
    const conversation = await Conversation.findById(conversationId);
    if (!conversation) {
      return next(createError(404, 'Conversation not found'));
    }

    // Check if it's a group conversation
    if (!conversation.isGroup) {
      return next(createError(400, 'Cannot leave a one-on-one conversation'));
    }

    // Check if user is a participant
    if (!conversation.isParticipant(req.user.id)) {
      return next(createError(403, 'You are not a participant in this conversation'));
    }

    // Check if user is the only admin
    if (
      conversation.isGroupAdmin(req.user.id) &&
      conversation.groupMembers.filter(member => member.role === 'admin').length === 1 &&
      conversation.participants.length > 1
    ) {
      return next(createError(400, 'Please assign another admin before leaving the group'));
    }

    // Remove participant
    conversation.participants = conversation.participants.filter(
      id => id.toString() !== req.user.id
    );

    // Remove from group members
    conversation.groupMembers = conversation.groupMembers.filter(
      member => member.user.toString() !== req.user.id
    );

    // If user was the group admin, assign a new admin if there are other participants
    if (
      conversation.groupAdmin &&
      conversation.groupAdmin.toString() === req.user.id &&
      conversation.participants.length > 0
    ) {
      conversation.groupAdmin = conversation.participants[0];

      // Update the role of the new admin in group members
      const newAdminIndex = conversation.groupMembers.findIndex(
        member => member.user.toString() === conversation.participants[0].toString()
      );

      if (newAdminIndex !== -1) {
        conversation.groupMembers[newAdminIndex].role = 'admin';
      }
    }

    await conversation.save();

    // Emit participant left to all participants
    socketEmitter.emitParticipantLeft(conversation, req.user.id);

    res.status(200).json({
      success: true,
      message: 'You have left the group',
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Update group conversation details
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.updateGroupConversation = async (req, res, next) => {
  try {
    const { conversationId } = req.params;
    const { name } = req.body;

    // Get conversation
    const conversation = await Conversation.findById(conversationId);
    if (!conversation) {
      return next(createError(404, 'Conversation not found'));
    }

    // Check if it's a group conversation
    if (!conversation.isGroup) {
      return next(createError(400, 'Cannot update a one-on-one conversation'));
    }

    // Check if user is an admin
    if (!conversation.isGroupAdmin(req.user.id)) {
      return next(createError(403, 'Only group admins can update group details'));
    }

    // Update group name if provided
    if (name) {
      conversation.name = name;
    }

    // Handle group image upload if any
    if (req.file) {
      // Delete old image if exists
      if (conversation.groupImage) {
        const publicId = conversation.groupImage.split('/').pop().split('.')[0];
        await cloudinary.uploader.destroy(`letstalk/groups/${publicId}`);
      }

      // Upload new image
      const result = await cloudinary.uploader.upload(req.file.path, {
        folder: 'letstalk/groups',
      });
      conversation.groupImage = result.secure_url;
    }

    await conversation.save();

    // Populate participant details
    await conversation.populate('participants', 'username name profilePicture');
    await conversation.populate('groupAdmin', 'username name profilePicture');
    await conversation.populate({
      path: 'groupMembers.user',
      select: 'username name profilePicture',
    });

    // Emit group update to all participants
    socketEmitter.emitGroupUpdated(conversation);

    res.status(200).json({
      success: true,
      data: conversation,
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Change participant role in a group conversation
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.changeParticipantRole = async (req, res, next) => {
  try {
    const { conversationId, userId } = req.params;
    const { role } = req.body;

    if (!role || !['admin', 'member'].includes(role)) {
      return next(createError(400, 'Invalid role'));
    }

    // Get conversation
    const conversation = await Conversation.findById(conversationId);
    if (!conversation) {
      return next(createError(404, 'Conversation not found'));
    }

    // Check if it's a group conversation
    if (!conversation.isGroup) {
      return next(createError(400, 'Cannot change roles in a one-on-one conversation'));
    }

    // Check if user is an admin
    if (!conversation.isGroupAdmin(req.user.id)) {
      return next(createError(403, 'Only group admins can change participant roles'));
    }

    // Find the member
    const memberIndex = conversation.groupMembers.findIndex(
      member => member.user.toString() === userId
    );

    if (memberIndex === -1) {
      return next(createError(404, 'User is not a member of this group'));
    }

    // Update role
    conversation.groupMembers[memberIndex].role = role;

    // If promoting to admin, update group admin if not already set
    if (role === 'admin' && (!conversation.groupAdmin || conversation.groupAdmin.toString() !== userId)) {
      // Don't change the primary admin, just add this user as an additional admin
    }

    await conversation.save();

    // Populate participant details
    await conversation.populate('participants', 'username name profilePicture');
    await conversation.populate('groupAdmin', 'username name profilePicture');
    await conversation.populate({
      path: 'groupMembers.user',
      select: 'username name profilePicture',
    });

    // Emit role change to all participants
    socketEmitter.emitParticipantRoleChanged(conversation, userId, role, req.user.id);

    res.status(200).json({
      success: true,
      data: conversation,
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Mute a conversation
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.muteConversation = async (req, res, next) => {
  try {
    const { conversationId } = req.params;
    const { duration } = req.body; // Duration in hours, null for indefinite

    // Get conversation
    const conversation = await Conversation.findById(conversationId);
    if (!conversation) {
      return next(createError(404, 'Conversation not found'));
    }

    // Check if user is a participant
    if (!conversation.isParticipant(req.user.id)) {
      return next(createError(403, 'You are not a participant in this conversation'));
    }

    // Calculate until date if duration is provided
    let until = null;
    if (duration) {
      until = new Date();
      until.setHours(until.getHours() + parseInt(duration));
    }

    // Remove existing mute if any
    conversation.muted = conversation.muted.filter(
      mute => mute.user.toString() !== req.user.id
    );

    // Add new mute
    conversation.muted.push({
      user: req.user.id,
      until,
    });

    await conversation.save();

    res.status(200).json({
      success: true,
      message: duration ? `Conversation muted for ${duration} hours` : 'Conversation muted indefinitely',
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Unmute a conversation
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.unmuteConversation = async (req, res, next) => {
  try {
    const { conversationId } = req.params;

    // Get conversation
    const conversation = await Conversation.findById(conversationId);
    if (!conversation) {
      return next(createError(404, 'Conversation not found'));
    }

    // Check if user is a participant
    if (!conversation.isParticipant(req.user.id)) {
      return next(createError(403, 'You are not a participant in this conversation'));
    }

    // Remove mute
    conversation.muted = conversation.muted.filter(
      mute => mute.user.toString() !== req.user.id
    );

    await conversation.save();

    res.status(200).json({
      success: true,
      message: 'Conversation unmuted',
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Pin a conversation
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.pinConversation = async (req, res, next) => {
  try {
    const { conversationId } = req.params;

    // Get conversation
    const conversation = await Conversation.findById(conversationId);
    if (!conversation) {
      return next(createError(404, 'Conversation not found'));
    }

    // Check if user is a participant
    if (!conversation.isParticipant(req.user.id)) {
      return next(createError(403, 'You are not a participant in this conversation'));
    }

    // Check if already pinned
    if (conversation.isPinnedBy(req.user.id)) {
      return next(createError(400, 'Conversation is already pinned'));
    }

    // Add to pinned
    conversation.pinnedBy.push(req.user.id);

    await conversation.save();

    res.status(200).json({
      success: true,
      message: 'Conversation pinned',
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Unpin a conversation
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.unpinConversation = async (req, res, next) => {
  try {
    const { conversationId } = req.params;

    // Get conversation
    const conversation = await Conversation.findById(conversationId);
    if (!conversation) {
      return next(createError(404, 'Conversation not found'));
    }

    // Check if user is a participant
    if (!conversation.isParticipant(req.user.id)) {
      return next(createError(403, 'You are not a participant in this conversation'));
    }

    // Remove from pinned
    conversation.pinnedBy = conversation.pinnedBy.filter(
      id => id.toString() !== req.user.id
    );

    await conversation.save();

    res.status(200).json({
      success: true,
      message: 'Conversation unpinned',
    });
  } catch (err) {
    next(err);
  }
};
