const mongoose = require('mongoose');

const EmotionSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Please provide an emotion name'],
    unique: true,
    trim: true,
  },
  description: {
    type: String,
    required: [true, 'Please provide a description'],
  },
  category: {
    type: String,
    enum: ['positive', 'negative', 'neutral', 'complex'],
    required: true,
  },
  icon: {
    type: String,
    required: true,
  },
  color: {
    type: String,
    required: true,
  },
  defaultAudioCue: {
    type: String,
  },
}, {
  timestamps: true,
});

module.exports = mongoose.model('Emotion', EmotionSchema);
