const Mood = require('../models/Mood');
const User = require('../models/User');
const { createError } = require('../utils/error');
const mongoose = require('mongoose');

// Get all moods
exports.getMoods = async (req, res, next) => {
  try {
    // Get moods from the database
    const moods = await Mood.find();

    res.status(200).json({ success: true, data: moods });
  } catch (err) {
    next(err);
  }
};

exports.getMood = async (req, res, next) => {
  try {
    res.status(200).json({ success: true, data: {} });
  } catch (err) {
    next(err);
  }
};

exports.createMood = async (req, res, next) => {
  try {
    res.status(201).json({ success: true, data: {} });
  } catch (err) {
    next(err);
  }
};

exports.updateMood = async (req, res, next) => {
  try {
    res.status(200).json({ success: true, data: {} });
  } catch (err) {
    next(err);
  }
};

exports.deleteMood = async (req, res, next) => {
  try {
    res.status(200).json({ success: true, data: {} });
  } catch (err) {
    next(err);
  }
};

exports.getUserMoods = async (req, res, next) => {
  try {
    res.status(200).json({ success: true, data: [] });
  } catch (err) {
    next(err);
  }
};

exports.getCurrentMood = async (req, res, next) => {
  try {
    // Get the current user with their mood
    const user = await User.findById(req.user.id)
      .select('currentMood')
      .populate('currentMood.mood', 'name category color icon');

    if (!user) {
      return next(createError(404, 'User not found'));
    }

    res.status(200).json({
      success: true,
      data: user.currentMood || null
    });
  } catch (err) {
    next(err);
  }
};

exports.setCurrentMood = async (req, res, next) => {
  try {
    const { mood, intensity = 5 } = req.body;

    if (!mood && mood !== null) {
      return next(createError(400, 'Mood ID is required or null to clear mood'));
    }

    // If mood is null, clear the current mood
    if (mood === null) {
      const user = await User.findByIdAndUpdate(
        req.user.id,
        { $unset: { currentMood: 1 } },
        { new: true }
      ).select('currentMood');

      return res.status(200).json({
        success: true,
        data: { currentMood: null }
      });
    }

    // Validate that the mood exists
    const moodExists = await Mood.findById(mood);
    if (!moodExists) {
      return next(createError(404, 'Mood not found'));
    }

    // Update the user's current mood
    const user = await User.findByIdAndUpdate(
      req.user.id,
      {
        $set: {
          'currentMood.mood': mood,
          'currentMood.intensity': intensity,
          'currentMood.updatedAt': new Date()
        }
      },
      { new: true }
    ).select('currentMood');

    if (!user) {
      return next(createError(404, 'User not found'));
    }

    res.status(200).json({
      success: true,
      data: { currentMood: user.currentMood }
    });
  } catch (err) {
    next(err);
  }
};
