const NFTAccess = require('../models/NFTAccess');
const LiveStream = require('../models/LiveStream');
const User = require('../models/User');
const asyncHandler = require('../middleware/async');
const ErrorResponse = require('../utils/errorResponse');
const { cloudinary } = require('../config/cloudinary');
const socketEmitter = require('../utils/socketEmitter');

/**
 * Get all NFT access tokens for a stream
 * @route GET /api/live-streams/:streamId/nft-access
 * @access Public
 */
exports.getNFTAccessTokens = asyncHandler(async (req, res, next) => {
  const { page = 1, limit = 10 } = req.query;
  const skip = (page - 1) * limit;

  const stream = await LiveStream.findById(req.params.streamId);
  if (!stream) {
    return next(new ErrorResponse(`Stream not found with id of ${req.params.streamId}`, 404));
  }

  // Get NFT access tokens
  const nftAccessTokens = await NFTAccess.find({ stream: req.params.streamId })
    .sort({ createdAt: -1 })
    .skip(skip)
    .limit(parseInt(limit))
    .populate('creator', 'username name profilePicture isVerified');

  // Get total count
  const total = await NFTAccess.countDocuments({ stream: req.params.streamId });

  res.status(200).json({
    success: true,
    count: nftAccessTokens.length,
    total,
    pagination: {
      page: parseInt(page),
      limit: parseInt(limit),
      totalPages: Math.ceil(total / limit),
    },
    data: nftAccessTokens,
  });
});

/**
 * Get a single NFT access token
 * @route GET /api/nft-access/:id
 * @access Public
 */
exports.getNFTAccessToken = asyncHandler(async (req, res, next) => {
  const nftAccess = await NFTAccess.findById(req.params.id)
    .populate('creator', 'username name profilePicture isVerified')
    .populate('stream', 'title user');

  if (!nftAccess) {
    return next(new ErrorResponse(`NFT access token not found with id of ${req.params.id}`, 404));
  }

  res.status(200).json({
    success: true,
    data: nftAccess,
  });
});

/**
 * Create a new NFT access token
 * @route POST /api/live-streams/:streamId/nft-access
 * @access Private
 */
exports.createNFTAccessToken = asyncHandler(async (req, res, next) => {
  const stream = await LiveStream.findById(req.params.streamId);

  if (!stream) {
    return next(new ErrorResponse(`Stream not found with id of ${req.params.streamId}`, 404));
  }

  // Check if user is the stream owner
  if (stream.user.toString() !== req.user.id) {
    return next(new ErrorResponse('Not authorized to create NFT access tokens for this stream', 403));
  }

  // Add creator and stream to request body
  req.body.creator = req.user.id;
  req.body.stream = req.params.streamId;

  // Upload image if provided
  if (req.files && req.files.image) {
    const file = req.files.image;

    // Upload to Cloudinary
    const result = await cloudinary.uploader.upload(file.tempFilePath, {
      folder: 'letstalk/nft-access',
      resource_type: 'image',
    });

    // Add image URL to request body
    req.body.imageUrl = result.secure_url;
  }

  // Create NFT access token
  const nftAccess = await NFTAccess.create(req.body);

  // Populate creator
  await nftAccess.populate('creator', 'username name profilePicture isVerified');

  // Update stream settings to enable NFT access
  if (!stream.settings.monetization.nft.exclusiveAccess.enabled) {
    stream.settings.monetization.nft.exclusiveAccess.enabled = true;
    stream.settings.monetization.nft.exclusiveAccess.requiredCollection = nftAccess.contractAddress;
    await stream.save();
  }

  res.status(201).json({
    success: true,
    data: nftAccess,
  });
});

/**
 * Update an NFT access token
 * @route PUT /api/nft-access/:id
 * @access Private
 */
exports.updateNFTAccessToken = asyncHandler(async (req, res, next) => {
  let nftAccess = await NFTAccess.findById(req.params.id);

  if (!nftAccess) {
    return next(new ErrorResponse(`NFT access token not found with id of ${req.params.id}`, 404));
  }

  // Check if user is the creator
  if (nftAccess.creator.toString() !== req.user.id) {
    return next(new ErrorResponse('Not authorized to update this NFT access token', 403));
  }

  // Upload new image if provided
  if (req.files && req.files.image) {
    const file = req.files.image;

    // Upload to Cloudinary
    const result = await cloudinary.uploader.upload(file.tempFilePath, {
      folder: 'letstalk/nft-access',
      resource_type: 'image',
    });

    // Add image URL to request body
    req.body.imageUrl = result.secure_url;
  }

  // Update NFT access token
  nftAccess = await NFTAccess.findByIdAndUpdate(req.params.id, req.body, {
    new: true,
    runValidators: true,
  });

  // Populate creator
  await nftAccess.populate('creator', 'username name profilePicture isVerified');

  res.status(200).json({
    success: true,
    data: nftAccess,
  });
});

/**
 * Check if user has access to a stream
 * @route GET /api/live-streams/:streamId/nft-access/check
 * @access Private
 */
exports.checkAccess = asyncHandler(async (req, res, next) => {
  const stream = await LiveStream.findById(req.params.streamId);

  if (!stream) {
    return next(new ErrorResponse(`Stream not found with id of ${req.params.streamId}`, 404));
  }

  // If NFT access is not enabled, everyone has access
  if (!stream.settings.monetization.nft.exclusiveAccess.enabled) {
    return res.status(200).json({
      success: true,
      data: {
        hasAccess: true,
        accessType: 'full',
        reason: 'NFT access not required',
      },
    });
  }

  // Stream owner always has access
  if (stream.user.toString() === req.user.id) {
    return res.status(200).json({
      success: true,
      data: {
        hasAccess: true,
        accessType: 'full',
        reason: 'Stream owner',
      },
    });
  }

  // Get NFT access tokens for this stream
  const nftAccessTokens = await NFTAccess.find({
    stream: req.params.streamId,
    isActive: true
  });

  if (nftAccessTokens.length === 0) {
    return res.status(200).json({
      success: true,
      data: {
        hasAccess: true,
        accessType: 'full',
        reason: 'No active NFT access tokens',
      },
    });
  }

  // Check if user has any of the required NFTs
  let hasAccess = false;
  let accessType = stream.settings.monetization.nft.exclusiveAccess.fallbackAccess || 'none';
  let accessToken = null;

  for (const token of nftAccessTokens) {
    // Check if user is in holders list
    const holder = token.holders.find(h => h.user.toString() === req.user.id);

    if (holder) {
      hasAccess = true;
      accessType = token.accessType;
      accessToken = token;

      // Update access history
      token.accessHistory.push({
        user: req.user.id,
        tokenId: holder.tokenId,
        accessedAt: Date.now(),
        ipAddress: req.ip,
        deviceInfo: req.headers['user-agent'],
      });

      // Update holder's access status
      holder.hasAccessed = true;
      holder.lastAccessedAt = Date.now();

      await token.save();
      break;
    }
  }

  // If user doesn't have access, check if they have the NFT in their wallet
  if (!hasAccess) {
    const user = await User.findById(req.user.id);

    if (user.wallet && user.wallet.address) {
      // In a real implementation, we would check the blockchain here
      // For demo purposes, we'll just check if the user has any NFT collections
      const hasNFTCollection = user.nftCollections && user.nftCollections.some(
        collection => nftAccessTokens.some(token => token.contractAddress === collection.contractAddress)
      );

      if (hasNFTCollection) {
        hasAccess = true;
        accessType = 'full';
        accessToken = nftAccessTokens.find(token =>
          user.nftCollections.some(collection => token.contractAddress === collection.contractAddress)
        );

        // Add user to holders list
        if (accessToken) {
          accessToken.holders.push({
            user: req.user.id,
            tokenId: 'auto-verified',
            acquiredAt: Date.now(),
            hasAccessed: true,
            lastAccessedAt: Date.now(),
          });

          accessToken.accessHistory.push({
            user: req.user.id,
            tokenId: 'auto-verified',
            accessedAt: Date.now(),
            ipAddress: req.ip,
            deviceInfo: req.headers['user-agent'],
          });

          await accessToken.save();
        }
      }
    }
  }

  res.status(200).json({
    success: true,
    data: {
      hasAccess,
      accessType,
      accessToken: accessToken ? {
        _id: accessToken._id,
        name: accessToken.name,
        accessType: accessToken.accessType,
        accessBenefits: accessToken.accessBenefits,
      } : null,
      fallbackAccess: stream.settings.monetization.nft.exclusiveAccess.fallbackAccess,
    },
  });
});
