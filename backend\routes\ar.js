const express = require('express');
const router = express.Router();
const { protect, authorize } = require('../middleware/auth');
const {
  getEffects,
  getEffect,
  createEffect,
  updateEffect,
  deleteEffect,
  incrementUsage,
  getTrendingEffects,
} = require('../controllers/arController');

// Public routes
router.get('/effects', getEffects);
router.get('/effects/trending', getTrendingEffects);
router.get('/effects/:id', getEffect);

// Protected routes
router.use(protect);
router.post('/effects', createEffect);
router.put('/effects/:id', updateEffect);
router.delete('/effects/:id', deleteEffect);
router.post('/effects/:id/usage', incrementUsage);

module.exports = router;
