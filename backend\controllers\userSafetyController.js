const UserSafety = require('../models/UserSafety');
const User = require('../models/User');
const Post = require('../models/Post');
const Reel = require('../models/Reel');
const Comment = require('../models/Comment');
const LiveStreamChat = require('../models/LiveStreamChat');
const LiveStream = require('../models/LiveStream');
const Message = require('../models/Message');
const asyncHandler = require('../middleware/async');
const ErrorResponse = require('../utils/errorResponse');
const socketEmitter = require('../utils/socketEmitter');

/**
 * @desc    Block a user
 * @route   POST /api/safety/block
 * @access  Private
 */
exports.blockUser = asyncHandler(async (req, res, next) => {
  const { targetUserId, reason, details } = req.body;

  // Validate input
  if (!targetUserId) {
    return next(new ErrorResponse('Target user ID is required', 400));
  }

  // Check if target user exists
  const targetUser = await User.findById(targetUserId);
  if (!targetUser) {
    return next(new ErrorResponse('Target user not found', 404));
  }

  // Check if user is trying to block themselves
  if (targetUserId === req.user.id) {
    return next(new ErrorResponse('You cannot block yourself', 400));
  }

  // Check if block already exists
  const existingBlock = await UserSafety.findOne({
    user: req.user.id,
    targetUser: targetUserId,
    actionType: 'block',
    status: 'active'
  });

  if (existingBlock) {
    return next(new ErrorResponse('You have already blocked this user', 400));
  }

  // Create block
  const block = await UserSafety.create({
    user: req.user.id,
    targetUser: targetUserId,
    actionType: 'block',
    reason: reason || 'other',
    details: details || ''
  });

  // Emit socket event
  socketEmitter.emitUserBlocked(req.user.id, targetUserId);

  res.status(201).json({
    success: true,
    data: block
  });
});

/**
 * @desc    Unblock a user
 * @route   DELETE /api/safety/block/:targetUserId
 * @access  Private
 */
exports.unblockUser = asyncHandler(async (req, res, next) => {
  const { targetUserId } = req.params;

  // Find active block
  const block = await UserSafety.findOne({
    user: req.user.id,
    targetUser: targetUserId,
    actionType: 'block',
    status: 'active'
  });

  if (!block) {
    return next(new ErrorResponse('Block not found', 404));
  }

  // Update block status
  block.status = 'resolved';
  await block.save();

  // Emit socket event
  socketEmitter.emitUserUnblocked(req.user.id, targetUserId);

  res.status(200).json({
    success: true,
    data: {}
  });
});

/**
 * @desc    Get blocked users
 * @route   GET /api/safety/blocks
 * @access  Private
 */
exports.getBlockedUsers = asyncHandler(async (req, res, next) => {
  // Find active blocks
  const blocks = await UserSafety.find({
    user: req.user.id,
    actionType: 'block',
    status: 'active'
  }).populate('targetUser', 'username name profilePicture');

  res.status(200).json({
    success: true,
    count: blocks.length,
    data: blocks
  });
});

/**
 * @desc    Report a user
 * @route   POST /api/safety/report/user
 * @access  Private
 */
exports.reportUser = asyncHandler(async (req, res, next) => {
  const { targetUserId, reason, details } = req.body;

  // Validate input
  if (!targetUserId || !reason) {
    return next(new ErrorResponse('Target user ID and reason are required', 400));
  }

  // Check if target user exists
  const targetUser = await User.findById(targetUserId);
  if (!targetUser) {
    return next(new ErrorResponse('Target user not found', 404));
  }

  // Check if user is trying to report themselves
  if (targetUserId === req.user.id) {
    return next(new ErrorResponse('You cannot report yourself', 400));
  }

  // Check if report already exists
  const existingReport = await UserSafety.findOne({
    user: req.user.id,
    targetUser: targetUserId,
    actionType: 'report',
    status: { $in: ['active', 'pending'] }
  });

  if (existingReport) {
    return next(new ErrorResponse('You have already reported this user', 400));
  }

  // Create report
  const report = await UserSafety.create({
    user: req.user.id,
    targetUser: targetUserId,
    actionType: 'report',
    reason,
    details: details || ''
  });

  res.status(201).json({
    success: true,
    data: report
  });
});

/**
 * @desc    Report content (post, reel, comment, message, stream)
 * @route   POST /api/safety/report/content
 * @access  Private
 */
exports.reportContent = asyncHandler(async (req, res, next) => {
  const { contentType, contentId, reason, details } = req.body;

  // Validate input
  if (!contentType || !contentId || !reason) {
    return next(new ErrorResponse('Content type, content ID, and reason are required', 400));
  }

  // Check if content exists and get owner
  let content;
  let targetUserId;

  switch (contentType) {
    case 'post':
      content = await Post.findById(contentId);
      if (!content) return next(new ErrorResponse('Post not found', 404));
      targetUserId = content.user;
      break;
    case 'reel':
      content = await Reel.findById(contentId);
      if (!content) return next(new ErrorResponse('Reel not found', 404));
      targetUserId = content.user;
      break;
    case 'comment':
      content = await Comment.findById(contentId);
      if (!content) return next(new ErrorResponse('Comment not found', 404));
      targetUserId = content.user;
      break;
    case 'message':
      content = await Message.findById(contentId);
      if (!content) return next(new ErrorResponse('Message not found', 404));
      targetUserId = content.sender;
      break;
    case 'stream_chat':
      content = await LiveStreamChat.findById(contentId);
      if (!content) return next(new ErrorResponse('Chat message not found', 404));
      targetUserId = content.user;
      break;
    case 'live_stream':
      content = await LiveStream.findById(contentId);
      if (!content) return next(new ErrorResponse('Live stream not found', 404));
      targetUserId = content.user;
      break;
    default:
      return next(new ErrorResponse('Invalid content type', 400));
  }

  // Check if user is trying to report their own content
  if (targetUserId.toString() === req.user.id) {
    return next(new ErrorResponse('You cannot report your own content', 400));
  }

  // Check if report already exists
  const existingReport = await UserSafety.findOne({
    user: req.user.id,
    contentType,
    contentId,
    actionType: 'report',
    status: { $in: ['active', 'pending'] }
  });

  if (existingReport) {
    return next(new ErrorResponse('You have already reported this content', 400));
  }

  // Create report
  const report = await UserSafety.create({
    user: req.user.id,
    targetUser: targetUserId,
    actionType: 'report',
    reason,
    details: details || '',
    contentType,
    contentId
  });

  res.status(201).json({
    success: true,
    data: report
  });
});

/**
 * @desc    Get reports (admin only)
 * @route   GET /api/safety/reports
 * @access  Private (Admin)
 */
exports.getReports = asyncHandler(async (req, res, next) => {
  // Check if user is admin
  if (req.user.role !== 'admin' && req.user.role !== 'moderator') {
    return next(new ErrorResponse('Not authorized to view reports', 403));
  }

  // Get query parameters
  const { status, reason, contentType } = req.query;
  
  // Build filter
  const filter = { actionType: 'report' };
  if (status) filter.status = status;
  if (reason) filter.reason = reason;
  if (contentType) filter.contentType = contentType;
  
  // Find reports
  const reports = await UserSafety.find(filter)
    .populate('user', 'username name profilePicture')
    .populate('targetUser', 'username name profilePicture')
    .sort('-createdAt');

  res.status(200).json({
    success: true,
    count: reports.length,
    data: reports
  });
});

/**
 * @desc    Update report status (admin only)
 * @route   PUT /api/safety/reports/:id
 * @access  Private (Admin)
 */
exports.updateReport = asyncHandler(async (req, res, next) => {
  const { status, adminNotes, actionTaken } = req.body;

  // Check if user is admin
  if (req.user.role !== 'admin' && req.user.role !== 'moderator') {
    return next(new ErrorResponse('Not authorized to update reports', 403));
  }

  // Find report
  const report = await UserSafety.findById(req.params.id);

  if (!report) {
    return next(new ErrorResponse('Report not found', 404));
  }

  if (report.actionType !== 'report') {
    return next(new ErrorResponse('This record is not a report', 400));
  }

  // Update report
  if (status) report.status = status;
  if (adminNotes) report.adminNotes = adminNotes;
  if (actionTaken) report.actionTaken = actionTaken;
  
  report.reviewedBy = req.user.id;
  report.reviewedAt = Date.now();

  await report.save();

  res.status(200).json({
    success: true,
    data: report
  });
});
