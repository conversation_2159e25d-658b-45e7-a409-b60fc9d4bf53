const mongoose = require('mongoose');

const UserSafetySchema = new mongoose.Schema({
  // User who initiated the safety action
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  // Target user of the safety action
  targetUser: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  // Type of safety action
  actionType: {
    type: String,
    enum: ['block', 'report', 'hide', 'mute'],
    required: true
  },
  // Status of the action
  status: {
    type: String,
    enum: ['active', 'resolved', 'dismissed', 'expired'],
    default: 'active'
  },
  // Reason for the action
  reason: {
    type: String,
    enum: [
      'harassment', 
      'hate_speech', 
      'inappropriate_content', 
      'spam', 
      'impersonation', 
      'misinformation', 
      'violence', 
      'self_harm', 
      'privacy_violation',
      'intellectual_property',
      'minor_safety',
      'other'
    ],
    required: true
  },
  // Additional details provided by the user
  details: {
    type: String,
    maxlength: 1000,
    default: ''
  },
  // Related content (if applicable)
  contentType: {
    type: String,
    enum: ['post', 'reel', 'comment', 'message', 'stream_chat', 'live_stream', null],
    default: null
  },
  contentId: {
    type: mongoose.Schema.Types.ObjectId,
    refPath: 'contentType',
    default: null
  },
  // For reports: admin who reviewed the report
  reviewedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    default: null
  },
  // For reports: admin notes
  adminNotes: {
    type: String,
    default: ''
  },
  // For reports: action taken
  actionTaken: {
    type: String,
    enum: ['none', 'warning', 'content_removed', 'account_suspended', 'account_terminated', null],
    default: null
  },
  // Expiration date (for temporary actions)
  expiresAt: {
    type: Date,
    default: null
  },
  // Timestamps
  reviewedAt: {
    type: Date,
    default: null
  }
}, {
  timestamps: true
});

// Create indexes for efficient queries
UserSafetySchema.index({ user: 1, targetUser: 1, actionType: 1 }, { unique: true });
UserSafetySchema.index({ targetUser: 1, actionType: 1 });
UserSafetySchema.index({ status: 1 });
UserSafetySchema.index({ actionType: 1, reason: 1 });
UserSafetySchema.index({ contentType: 1, contentId: 1 });
UserSafetySchema.index({ expiresAt: 1 }, { sparse: true });

// Pre-save hook to validate that user and targetUser are not the same
UserSafetySchema.pre('save', function(next) {
  if (this.user.toString() === this.targetUser.toString()) {
    return next(new Error('User cannot perform safety actions on themselves'));
  }
  next();
});

module.exports = mongoose.model('UserSafety', UserSafetySchema);
