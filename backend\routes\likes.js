const express = require('express');
const router = express.Router();
const { protect } = require('../middleware/auth');
const {
  getLikes,
  createLike,
  deleteLike,
  getUserLikes,
  checkLike,
} = require('../controllers/likeController');

// Protected routes
router.use(protect);
router.get('/check/:type/:id', checkLike);
router.get('/:type/:id', getLikes);
router.post('/', createLike);
router.delete('/:type/:id', deleteLike);
router.get('/user/:userId', getUserLikes);

module.exports = router;
