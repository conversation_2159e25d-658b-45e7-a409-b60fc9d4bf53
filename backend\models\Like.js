const mongoose = require('mongoose');

const LikeSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  post: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Post',
  },
  reel: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Reel',
  },
  comment: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Comment',
  },
  mood: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'UserMood',
  },
  moodComment: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'MoodComment',
  },
  recording: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'StreamRecording',
  },
  emotion: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Emotion',
  },
}, {
  timestamps: true,
});

// A like must be associated with either a post, reel, comment, mood, moodComment, or recording
LikeSchema.pre('validate', function(next) {
  const hasContent = this.post || this.reel || this.comment || this.mood || this.moodComment || this.recording;
  const contentCount = [this.post, this.reel, this.comment, this.mood, this.moodComment, this.recording].filter(Boolean).length;

  if (!hasContent) {
    return next(new Error('Like must be associated with either a post, reel, comment, mood, mood comment, or recording'));
  }

  if (contentCount > 1) {
    return next(new Error('Like can only be associated with one content type'));
  }

  next();
});

// Compound index to ensure a user can only like a specific content once
LikeSchema.index({ user: 1, post: 1 }, { unique: true, sparse: true });
LikeSchema.index({ user: 1, reel: 1 }, { unique: true, sparse: true });
LikeSchema.index({ user: 1, comment: 1 }, { unique: true, sparse: true });
LikeSchema.index({ user: 1, mood: 1 }, { unique: true, sparse: true });
LikeSchema.index({ user: 1, moodComment: 1 }, { unique: true, sparse: true });
LikeSchema.index({ user: 1, recording: 1 }, { unique: true, sparse: true });

module.exports = mongoose.model('Like', LikeSchema);
