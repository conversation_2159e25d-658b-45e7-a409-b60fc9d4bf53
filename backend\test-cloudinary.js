const cloudinary = require('cloudinary').v2;
const dotenv = require('dotenv');
const path = require('path');

// Load environment variables
console.log('Loading environment variables from:', path.resolve(process.cwd(), '.env'));
dotenv.config();

// Log Cloudinary configuration (without showing the full API secret)
console.log('Cloudinary Configuration:');
console.log('- Cloud Name:', process.env.CLOUDINARY_CLOUD_NAME);
console.log('- API Key:', process.env.CLOUDINARY_API_KEY);
console.log('- API Secret:', process.env.CLOUDINARY_API_SECRET ? 'Set (not showing for security)' : 'Not set');

// Test Cloudinary connection
const testCloudinaryConnection = async () => {
  try {
    // Configure Cloudinary
    const config = {
      cloud_name: process.env.CLOUDINARY_CLOUD_NAME || 'dtx9ed1hh',
      api_key: process.env.CLOUDINARY_API_KEY || '6b14456b28cc663546ea37c45f1d40',
      api_secret: process.env.CLOUDINARY_API_SECRET
    };

    console.log('\nUsing Cloudinary config:');
    console.log('- Cloud Name:', config.cloud_name);
    console.log('- API Key:', config.api_key);
    console.log('- API Secret:', config.api_secret ? 'Set (not showing for security)' : 'Not set');

    cloudinary.config(config);

    // Try to get account info (this will fail if credentials are invalid)
    console.log('\nTesting Cloudinary connection...');
    try {
      const result = await cloudinary.api.ping();
      console.log('\n✅ Cloudinary connection successful!');
      console.log('Response:', result);
      return true;
    } catch (apiError) {
      console.error('\n❌ Cloudinary API error:', apiError);
      console.error('Error details:', JSON.stringify(apiError, null, 2));
      return false;
    }
  } catch (error) {
    console.error('\n❌ Cloudinary configuration error:', error);
    console.error('Error details:', JSON.stringify(error, null, 2));

    if (process.env.CLOUDINARY_API_SECRET === 'your_cloudinary_api_secret') {
      console.error('\nNOTE: It appears you are using a placeholder value for CLOUDINARY_API_SECRET.');
      console.error('You need to replace "your_cloudinary_api_secret" with your actual Cloudinary API secret.');
    }

    return false;
  }
};

// Run the test
testCloudinaryConnection()
  .then(() => {
    process.exit(0);
  })
  .catch(err => {
    console.error('Unexpected error:', err);
    process.exit(1);
  });
