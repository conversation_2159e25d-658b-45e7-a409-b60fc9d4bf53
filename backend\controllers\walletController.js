const User = require('../models/User');
const Wallet = require('../models/Wallet');
const Tip = require('../models/Tip');
const LiveStream = require('../models/LiveStream');
const asyncHandler = require('../middleware/async');
const ErrorResponse = require('../utils/errorResponse');
const socketEmitter = require('../utils/socketEmitter');

/**
 * Get user's wallet
 * @route GET /api/users/me/wallet
 * @access Private
 */
exports.getWallet = asyncHandler(async (req, res, next) => {
  // Find wallet or create if it doesn't exist
  let wallet = await Wallet.findOne({ user: req.user.id });

  if (!wallet) {
    wallet = await Wallet.create({
      user: req.user.id,
      address: null,
      blockchain: null,
      balance: 0,
      currency: 'usd'
    });
  }

  res.status(200).json({
    success: true,
    data: wallet
  });
});

/**
 * Connect wallet
 * @route POST /api/users/me/wallet
 * @access Private
 */
exports.connectWallet = asyncHandler(async (req, res, next) => {
  const { address, blockchain, signature } = req.body;

  if (!address || !blockchain) {
    return next(new ErrorResponse('Please provide wallet address and blockchain', 400));
  }

  // In a real implementation, you would verify the signature here

  // Find wallet or create if it doesn't exist
  let wallet = await Wallet.findOne({ user: req.user.id });

  if (wallet) {
    // Update existing wallet
    wallet.address = address;
    wallet.blockchain = blockchain;
    wallet.isConnected = true;
    wallet.lastConnected = Date.now();
    await wallet.save();
  } else {
    // Create new wallet
    wallet = await Wallet.create({
      user: req.user.id,
      address,
      blockchain,
      isConnected: true,
      lastConnected: Date.now()
    });
  }

  res.status(200).json({
    success: true,
    data: wallet
  });
});

/**
 * Disconnect wallet
 * @route DELETE /api/users/me/wallet
 * @access Private
 */
exports.disconnectWallet = asyncHandler(async (req, res, next) => {
  // Find wallet
  const wallet = await Wallet.findOne({ user: req.user.id });

  if (!wallet) {
    return next(new ErrorResponse('Wallet not found', 404));
  }

  // Update wallet
  wallet.isConnected = false;
  wallet.lastDisconnected = Date.now();
  await wallet.save();

  res.status(200).json({
    success: true,
    data: {}
  });
});

/**
 * Send tip to a stream
 * @route POST /api/live-streams/:streamId/tip
 * @access Private
 */
exports.sendTip = asyncHandler(async (req, res, next) => {
  const { streamId } = req.params;
  const { amount, currency = 'coins', message } = req.body;

  if (!amount || amount <= 0) {
    return next(new ErrorResponse('Please provide a valid amount', 400));
  }

  // Check if stream exists
  const stream = await LiveStream.findById(streamId);
  if (!stream) {
    return next(new ErrorResponse(`Live stream not found with id of ${streamId}`, 404));
  }

  // Check if user has enough coins if using coins
  if (currency === 'coins') {
    const user = await User.findById(req.user.id);
    if (user.coins < amount) {
      return next(new ErrorResponse('Insufficient coins', 400));
    }

    // Deduct coins from user
    await User.findByIdAndUpdate(req.user.id, { $inc: { coins: -amount } });

    // Add coins to stream owner
    await User.findByIdAndUpdate(stream.user, { $inc: { coins: amount } });
  }

  // Create tip
  const tip = await Tip.create({
    user: req.user.id,
    recipient: stream.user,
    stream: streamId,
    amount,
    currency,
    message
  });

  // Populate user
  await tip.populate('user', 'username name profilePicture isVerified');

  // Emit socket event for the tip
  socketEmitter.emitLiveStreamTip(streamId, {
    ...tip.toObject(),
    user: {
      _id: req.user._id,
      name: req.user.name,
      username: req.user.username,
      profilePicture: req.user.profilePicture,
      isVerified: req.user.isVerified
    }
  });

  res.status(200).json({
    success: true,
    data: tip
  });
});

/**
 * Get tip history
 * @route GET /api/users/me/tips
 * @access Private
 */
exports.getTipHistory = asyncHandler(async (req, res, next) => {
  const { page = 1, limit = 10, type = 'sent' } = req.query;
  const skip = (page - 1) * limit;

  // Build query
  let query = {};
  if (type === 'sent') {
    query.user = req.user.id;
  } else if (type === 'received') {
    query.recipient = req.user.id;
  }

  // Execute query
  const tips = await Tip.find(query)
    .sort({ createdAt: -1 })
    .skip(skip)
    .limit(parseInt(limit))
    .populate('user', 'username name profilePicture isVerified')
    .populate('recipient', 'username name profilePicture isVerified')
    .populate('stream', 'title thumbnail');

  // Get total count
  const total = await Tip.countDocuments(query);

  res.status(200).json({
    success: true,
    count: tips.length,
    total,
    pagination: {
      page: parseInt(page),
      limit: parseInt(limit),
      totalPages: Math.ceil(total / limit),
    },
    data: tips,
  });
});
