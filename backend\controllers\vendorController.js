const mongoose = require('mongoose');
const Vendor = require('../models/Vendor');
const VendorReview = require('../models/VendorReview');
const Product = require('../models/Product');
const Order = require('../models/Order');
const User = require('../models/User');
const cloudinary = require('../utils/cloudinary');

// @desc    Register as vendor / Become a seller
// @route   POST /api/vendors/register
// @access  Private
exports.registerVendor = async (req, res) => {
  try {
    const {
      businessName,
      businessType,
      description,
      contactInfo,
      address,
      storeName
    } = req.body;

    // Check if user is already a vendor
    const existingVendor = await Vendor.findOne({ user: req.user.id });
    if (existingVendor) {
      return res.status(400).json({
        success: false,
        message: 'User is already registered as a vendor'
      });
    }

    // Generate unique store slug
    let storeSlug = storeName.toLowerCase().replace(/[^a-z0-9]/g, '-').replace(/-+/g, '-').replace(/^-|-$/g, '');
    const existingSlug = await Vendor.findOne({ 'storeSettings.storeSlug': storeSlug });
    if (existingSlug) {
      storeSlug = `${storeSlug}-${Date.now()}`;
    }

    const vendor = new Vendor({
      user: req.user.id,
      businessName,
      businessType,
      description,
      contactInfo: {
        email: contactInfo.email || req.user.email,
        phone: contactInfo.phone,
        website: contactInfo.website,
        socialMedia: contactInfo.socialMedia || {}
      },
      address,
      storeSettings: {
        storeName,
        storeSlug,
        storeDescription: description
      }
    });

    // Mark business info step as completed
    vendor.onboardingSteps.businessInfo = true;

    await vendor.save();

    // Update user role to vendor
    await User.findByIdAndUpdate(req.user.id, { role: 'vendor' });

    res.status(201).json({
      success: true,
      data: vendor,
      message: 'Vendor registration successful'
    });
  } catch (error) {
    console.error('Error registering vendor:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Submit KYC documents
// @route   POST /api/vendors/kyc
// @access  Private (Vendor only)
exports.submitKYC = async (req, res) => {
  try {
    const vendor = await Vendor.findOne({ user: req.user.id });
    if (!vendor) {
      return res.status(404).json({
        success: false,
        message: 'Vendor not found'
      });
    }

    const { idDocument, addressProof, businessLicense, taxDocument } = req.files || {};

    // Upload documents to cloudinary
    const uploadPromises = [];
    const documents = {};

    if (idDocument) {
      uploadPromises.push(
        cloudinary.uploader.upload(idDocument[0].path, {
          folder: 'letstalk/kyc/id',
          resource_type: 'auto'
        }).then(result => {
          documents.idDocument = result.secure_url;
        })
      );
    }

    if (addressProof) {
      uploadPromises.push(
        cloudinary.uploader.upload(addressProof[0].path, {
          folder: 'letstalk/kyc/address',
          resource_type: 'auto'
        }).then(result => {
          documents.addressProof = result.secure_url;
        })
      );
    }

    if (businessLicense) {
      uploadPromises.push(
        cloudinary.uploader.upload(businessLicense[0].path, {
          folder: 'letstalk/kyc/business',
          resource_type: 'auto'
        }).then(result => {
          documents.businessLicense = result.secure_url;
        })
      );
    }

    if (taxDocument) {
      uploadPromises.push(
        cloudinary.uploader.upload(taxDocument[0].path, {
          folder: 'letstalk/kyc/tax',
          resource_type: 'auto'
        }).then(result => {
          documents.taxDocument = result.secure_url;
        })
      );
    }

    await Promise.all(uploadPromises);

    // Update vendor with KYC documents
    vendor.kycDocuments = { ...vendor.kycDocuments, ...documents };
    vendor.kycStatus = 'submitted';
    vendor.onboardingSteps.kycSubmission = true;

    await vendor.save();

    res.status(200).json({
      success: true,
      data: vendor,
      message: 'KYC documents submitted successfully'
    });
  } catch (error) {
    console.error('Error submitting KYC:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Setup payout method
// @route   POST /api/vendors/payout
// @access  Private (Vendor only)
exports.setupPayout = async (req, res) => {
  try {
    const vendor = await Vendor.findOne({ user: req.user.id });
    if (!vendor) {
      return res.status(404).json({
        success: false,
        message: 'Vendor not found'
      });
    }

    const { type, accountDetails } = req.body;

    vendor.payoutMethod = {
      type,
      accountDetails,
      isVerified: false // Will be verified by admin
    };
    vendor.onboardingSteps.payoutSetup = true;

    await vendor.save();

    res.status(200).json({
      success: true,
      data: vendor,
      message: 'Payout method setup successfully'
    });
  } catch (error) {
    console.error('Error setting up payout:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Complete store setup
// @route   PUT /api/vendors/store
// @access  Private (Vendor only)
exports.setupStore = async (req, res) => {
  try {
    const vendor = await Vendor.findOne({ user: req.user.id });
    if (!vendor) {
      return res.status(404).json({
        success: false,
        message: 'Vendor not found'
      });
    }

    const {
      storeName,
      storeDescription,
      storeCategories,
      operatingHours,
      shippingPolicy,
      returnPolicy,
      termsOfService
    } = req.body;

    // Handle logo and banner uploads
    if (req.files) {
      if (req.files.logo) {
        const logoResult = await cloudinary.uploader.upload(req.files.logo[0].path, {
          folder: 'letstalk/vendors/logos'
        });
        vendor.logo = logoResult.secure_url;
      }

      if (req.files.banner) {
        const bannerResult = await cloudinary.uploader.upload(req.files.banner[0].path, {
          folder: 'letstalk/vendors/banners'
        });
        vendor.banner = bannerResult.secure_url;
      }
    }

    // Update store settings
    vendor.storeSettings = {
      ...vendor.storeSettings,
      storeName: storeName || vendor.storeSettings.storeName,
      storeDescription,
      storeCategories,
      operatingHours,
      shippingPolicy,
      returnPolicy,
      termsOfService
    };

    vendor.onboardingSteps.storeSetup = true;

    await vendor.save();

    res.status(200).json({
      success: true,
      data: vendor,
      message: 'Store setup completed successfully'
    });
  } catch (error) {
    console.error('Error setting up store:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Get vendor dashboard data
// @route   GET /api/vendors/dashboard
// @access  Private (Vendor only)
exports.getDashboard = async (req, res) => {
  try {
    const vendor = await Vendor.findOne({ user: req.user.id }).populate('user', 'name email');
    if (!vendor) {
      return res.status(404).json({
        success: false,
        message: 'Vendor not found'
      });
    }

    // Update analytics
    await vendor.updateAnalytics();

    // Get recent orders
    const recentOrders = await Order.find({
      'items.product': { $in: await Product.find({ user: req.user.id }).distinct('_id') }
    })
    .populate('user', 'name email')
    .populate('items.product', 'name images price')
    .sort({ createdAt: -1 })
    .limit(10);

    // Get top products
    const topProducts = await Product.find({ user: req.user.id })
      .sort({ views: -1, purchases: -1 })
      .limit(5)
      .select('name images price views purchases averageRating');

    // Get recent reviews
    const recentReviews = await VendorReview.find({ vendor: vendor._id })
      .populate('user', 'name')
      .populate('order', 'orderNumber')
      .sort({ createdAt: -1 })
      .limit(5);

    // Calculate monthly revenue trend
    const monthlyRevenue = await Order.aggregate([
      {
        $match: {
          'items.product': { $in: await Product.find({ user: req.user.id }).distinct('_id') },
          createdAt: { $gte: new Date(Date.now() - 12 * 30 * 24 * 60 * 60 * 1000) }
        }
      },
      {
        $group: {
          _id: {
            year: { $year: '$createdAt' },
            month: { $month: '$createdAt' }
          },
          revenue: { $sum: '$total' },
          orders: { $sum: 1 }
        }
      },
      {
        $sort: { '_id.year': 1, '_id.month': 1 }
      }
    ]);

    const dashboardData = {
      vendor,
      analytics: vendor.analytics,
      recentOrders,
      topProducts,
      recentReviews,
      monthlyRevenue,
      onboardingProgress: vendor.getOnboardingProgress()
    };

    res.status(200).json({
      success: true,
      data: dashboardData
    });
  } catch (error) {
    console.error('Error getting dashboard:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Get vendor profile
// @route   GET /api/vendors/profile
// @access  Private (Vendor only)
exports.getProfile = async (req, res) => {
  try {
    const vendor = await Vendor.findOne({ user: req.user.id }).populate('user', 'name email');
    if (!vendor) {
      return res.status(404).json({
        success: false,
        message: 'Vendor not found'
      });
    }

    res.status(200).json({
      success: true,
      data: vendor
    });
  } catch (error) {
    console.error('Error getting vendor profile:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Update vendor profile
// @route   PUT /api/vendors/profile
// @access  Private (Vendor only)
exports.updateProfile = async (req, res) => {
  try {
    const vendor = await Vendor.findOne({ user: req.user.id });
    if (!vendor) {
      return res.status(404).json({
        success: false,
        message: 'Vendor not found'
      });
    }

    const allowedFields = [
      'businessName',
      'description',
      'contactInfo',
      'address',
      'storeSettings'
    ];

    allowedFields.forEach(field => {
      if (req.body[field]) {
        if (field === 'contactInfo' || field === 'address' || field === 'storeSettings') {
          vendor[field] = { ...vendor[field], ...req.body[field] };
        } else {
          vendor[field] = req.body[field];
        }
      }
    });

    await vendor.save();

    res.status(200).json({
      success: true,
      data: vendor,
      message: 'Profile updated successfully'
    });
  } catch (error) {
    console.error('Error updating vendor profile:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Get vendor storefront by slug or ID
// @route   GET /api/vendors/store/:slug
// @route   GET /api/vendors/storefront/:id
// @access  Public
exports.getStorefront = async (req, res) => {
  try {
    const identifier = req.params.slug || req.params.id;

    // Try to find by slug first, then by ID
    let vendor = await Vendor.findOne({
      'storeSettings.storeSlug': identifier,
      status: 'active'
    }).populate('user', 'name');

    // If not found by slug, try by ID
    if (!vendor && mongoose.Types.ObjectId.isValid(identifier)) {
      vendor = await Vendor.findOne({
        _id: identifier,
        status: 'active'
      }).populate('user', 'name');
    }

    // If still not found, try by user ID
    if (!vendor && mongoose.Types.ObjectId.isValid(identifier)) {
      vendor = await Vendor.findOne({
        user: identifier,
        status: 'active'
      }).populate('user', 'name');
    }

    if (!vendor) {
      return res.status(404).json({
        success: false,
        message: 'Store not found'
      });
    }

    // Get vendor products
    const products = await Product.find({
      user: vendor.user._id,
      isAvailable: true
    })
    .sort({ createdAt: -1 })
    .limit(20);

    // Get vendor reviews
    const reviews = await VendorReview.find({
      vendor: vendor._id,
      status: 'active'
    })
    .populate('user', 'name')
    .sort({ createdAt: -1 })
    .limit(10);

    // Increment store views
    vendor.analytics.totalViews += 1;
    await vendor.save();

    const storefrontData = {
      vendor: {
        _id: vendor._id,
        businessName: vendor.businessName,
        description: vendor.description,
        logo: vendor.logo,
        banner: vendor.banner,
        storeSettings: vendor.storeSettings,
        analytics: {
          averageRating: vendor.analytics.averageRating,
          totalReviews: vendor.analytics.totalReviews,
          totalProducts: vendor.analytics.totalProducts
        },
        verification: vendor.verification,
        createdAt: vendor.createdAt
      },
      products,
      reviews,
      totalProducts: await Product.countDocuments({ user: vendor.user._id, isAvailable: true })
    };

    res.status(200).json({
      success: true,
      data: storefrontData
    });
  } catch (error) {
    console.error('Error getting storefront:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Get vendor orders
// @route   GET /api/vendors/orders
// @access  Private (Vendor only)
exports.getOrders = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      status,
      startDate,
      endDate
    } = req.query;

    const vendor = await Vendor.findOne({ user: req.user.id });
    if (!vendor) {
      return res.status(404).json({
        success: false,
        message: 'Vendor not found'
      });
    }

    // Get vendor's product IDs
    const productIds = await Product.find({ user: req.user.id }).distinct('_id');

    let query = {
      'items.product': { $in: productIds }
    };

    if (status) query.status = status;
    if (startDate && endDate) {
      query.createdAt = {
        $gte: new Date(startDate),
        $lte: new Date(endDate)
      };
    }

    const orders = await Order.find(query)
      .populate('user', 'name email')
      .populate('items.product', 'name images price')
      .sort({ createdAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit);

    const total = await Order.countDocuments(query);

    res.status(200).json({
      success: true,
      data: orders,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error getting vendor orders:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Update order status
// @route   PUT /api/vendors/orders/:id/status
// @access  Private (Vendor only)
exports.updateOrderStatus = async (req, res) => {
  try {
    const { status, trackingNumber, notes } = req.body;

    const order = await Order.findById(req.params.id);
    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Order not found'
      });
    }

    // Verify vendor owns products in this order
    const productIds = await Product.find({ user: req.user.id }).distinct('_id');
    const hasVendorProducts = order.items.some(item =>
      productIds.some(id => id.toString() === item.product.toString())
    );

    if (!hasVendorProducts) {
      return res.status(403).json({
        success: false,
        message: 'Not authorized to update this order'
      });
    }

    order.status = status;
    if (trackingNumber) order.trackingNumber = trackingNumber;
    if (notes) order.vendorNotes = notes;

    await order.save();

    res.status(200).json({
      success: true,
      data: order,
      message: 'Order status updated successfully'
    });
  } catch (error) {
    console.error('Error updating order status:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Get sales reports
// @route   GET /api/vendors/reports
// @access  Private (Vendor only)
exports.getSalesReports = async (req, res) => {
  try {
    const {
      startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
      endDate = new Date(),
      groupBy = 'day'
    } = req.query;

    const vendor = await Vendor.findOne({ user: req.user.id });
    if (!vendor) {
      return res.status(404).json({
        success: false,
        message: 'Vendor not found'
      });
    }

    const productIds = await Product.find({ user: req.user.id }).distinct('_id');

    // Sales over time
    const salesOverTime = await Order.aggregate([
      {
        $match: {
          'items.product': { $in: productIds },
          createdAt: {
            $gte: new Date(startDate),
            $lte: new Date(endDate)
          }
        }
      },
      {
        $group: {
          _id: groupBy === 'month'
            ? { year: { $year: '$createdAt' }, month: { $month: '$createdAt' } }
            : { year: { $year: '$createdAt' }, month: { $month: '$createdAt' }, day: { $dayOfMonth: '$createdAt' } },
          revenue: { $sum: '$total' },
          orders: { $sum: 1 }
        }
      },
      {
        $sort: { '_id': 1 }
      }
    ]);

    // Top products
    const topProducts = await Order.aggregate([
      {
        $match: {
          'items.product': { $in: productIds },
          createdAt: {
            $gte: new Date(startDate),
            $lte: new Date(endDate)
          }
        }
      },
      { $unwind: '$items' },
      {
        $match: {
          'items.product': { $in: productIds }
        }
      },
      {
        $group: {
          _id: '$items.product',
          totalSold: { $sum: '$items.quantity' },
          revenue: { $sum: { $multiply: ['$items.price', '$items.quantity'] } }
        }
      },
      {
        $lookup: {
          from: 'products',
          localField: '_id',
          foreignField: '_id',
          as: 'product'
        }
      },
      { $unwind: '$product' },
      {
        $project: {
          product: {
            _id: '$product._id',
            name: '$product.name',
            images: '$product.images',
            price: '$product.price'
          },
          totalSold: 1,
          revenue: 1
        }
      },
      {
        $sort: { revenue: -1 }
      },
      {
        $limit: 10
      }
    ]);

    // Summary statistics
    const summary = await Order.aggregate([
      {
        $match: {
          'items.product': { $in: productIds },
          createdAt: {
            $gte: new Date(startDate),
            $lte: new Date(endDate)
          }
        }
      },
      {
        $group: {
          _id: null,
          totalRevenue: { $sum: '$total' },
          totalOrders: { $sum: 1 },
          averageOrderValue: { $avg: '$total' }
        }
      }
    ]);

    res.status(200).json({
      success: true,
      data: {
        summary: summary[0] || { totalRevenue: 0, totalOrders: 0, averageOrderValue: 0 },
        salesOverTime,
        topProducts
      }
    });
  } catch (error) {
    console.error('Error getting sales reports:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Get vendor reviews
// @route   GET /api/vendors/reviews
// @access  Private (Vendor only)
exports.getReviews = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      rating,
      status = 'active'
    } = req.query;

    const vendor = await Vendor.findOne({ user: req.user.id });
    if (!vendor) {
      return res.status(404).json({
        success: false,
        message: 'Vendor not found'
      });
    }

    let query = { vendor: vendor._id, status };
    if (rating) query.rating = parseInt(rating);

    const reviews = await VendorReview.find(query)
      .populate('user', 'name')
      .populate('order', 'orderNumber')
      .sort({ createdAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit);

    const total = await VendorReview.countDocuments(query);

    res.status(200).json({
      success: true,
      data: reviews,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error getting vendor reviews:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Respond to review
// @route   POST /api/vendors/reviews/:id/respond
// @access  Private (Vendor only)
exports.respondToReview = async (req, res) => {
  try {
    const { message } = req.body;

    const vendor = await Vendor.findOne({ user: req.user.id });
    if (!vendor) {
      return res.status(404).json({
        success: false,
        message: 'Vendor not found'
      });
    }

    const review = await VendorReview.findOne({
      _id: req.params.id,
      vendor: vendor._id
    });

    if (!review) {
      return res.status(404).json({
        success: false,
        message: 'Review not found'
      });
    }

    await review.addVendorResponse(message, req.user.id);

    res.status(200).json({
      success: true,
      data: review,
      message: 'Response added successfully'
    });
  } catch (error) {
    console.error('Error responding to review:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Complete onboarding
// @route   POST /api/vendors/complete-onboarding
// @access  Private (Vendor only)
exports.completeOnboarding = async (req, res) => {
  try {
    const vendor = await Vendor.findOne({ user: req.user.id });
    if (!vendor) {
      return res.status(404).json({
        success: false,
        message: 'Vendor not found'
      });
    }

    // Check if all required steps are completed
    const requiredSteps = ['businessInfo', 'kycSubmission', 'payoutSetup', 'storeSetup'];
    const allStepsCompleted = requiredSteps.every(step => vendor.onboardingSteps[step]);

    if (!allStepsCompleted) {
      return res.status(400).json({
        success: false,
        message: 'Please complete all onboarding steps first'
      });
    }

    vendor.onboardingCompleted = true;
    vendor.status = 'active'; // Activate vendor account
    await vendor.save();

    res.status(200).json({
      success: true,
      data: vendor,
      message: 'Onboarding completed successfully'
    });
  } catch (error) {
    console.error('Error completing onboarding:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Follow a vendor
// @route   POST /api/vendors/follow/:id
// @access  Private
exports.followVendor = async (req, res) => {
  try {
    const vendorId = req.params.id;
    const userId = req.user.id;

    // Find the vendor
    const vendor = await Vendor.findById(vendorId);
    if (!vendor) {
      return res.status(404).json({
        success: false,
        message: 'Vendor not found'
      });
    }

    // Check if already following
    const user = await User.findById(userId);
    if (user.followingVendors && user.followingVendors.includes(vendorId)) {
      return res.status(400).json({
        success: false,
        message: 'Already following this vendor'
      });
    }

    // Add to user's following list
    await User.findByIdAndUpdate(userId, {
      $addToSet: { followingVendors: vendorId }
    });

    // Increment vendor's follower count
    vendor.analytics.totalFollowers = (vendor.analytics.totalFollowers || 0) + 1;
    await vendor.save();

    res.status(200).json({
      success: true,
      message: 'Successfully followed vendor'
    });
  } catch (error) {
    console.error('Error following vendor:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Unfollow a vendor
// @route   DELETE /api/vendors/unfollow/:id
// @access  Private
exports.unfollowVendor = async (req, res) => {
  try {
    const vendorId = req.params.id;
    const userId = req.user.id;

    // Find the vendor
    const vendor = await Vendor.findById(vendorId);
    if (!vendor) {
      return res.status(404).json({
        success: false,
        message: 'Vendor not found'
      });
    }

    // Remove from user's following list
    await User.findByIdAndUpdate(userId, {
      $pull: { followingVendors: vendorId }
    });

    // Decrement vendor's follower count
    vendor.analytics.totalFollowers = Math.max((vendor.analytics.totalFollowers || 0) - 1, 0);
    await vendor.save();

    res.status(200).json({
      success: true,
      message: 'Successfully unfollowed vendor'
    });
  } catch (error) {
    console.error('Error unfollowing vendor:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

module.exports = {
  registerVendor: exports.registerVendor,
  submitKYC: exports.submitKYC,
  setupPayout: exports.setupPayout,
  setupStore: exports.setupStore,
  getDashboard: exports.getDashboard,
  getProfile: exports.getProfile,
  updateProfile: exports.updateProfile,
  getStorefront: exports.getStorefront,
  getOrders: exports.getOrders,
  updateOrderStatus: exports.updateOrderStatus,
  getSalesReports: exports.getSalesReports,
  getReviews: exports.getReviews,
  respondToReview: exports.respondToReview,
  completeOnboarding: exports.completeOnboarding,
  followVendor: exports.followVendor,
  unfollowVendor: exports.unfollowVendor
};
