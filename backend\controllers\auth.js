const User = require('../models/User');
const ErrorResponse = require('../utils/errorResponse');

// @desc    Register user
// @route   POST /api/auth/register
// @access  Public
exports.register = async (req, res, next) => {
  try {
    const { name, fullName, email, password, username } = req.body;

    // Use fullName if provided, otherwise use name
    const userFullName = fullName || name;

    // Validate required fields
    if (!userFullName || !email || !password || !username) {
      return res.status(400).json({
        success: false,
        message: 'Please provide all required fields (name, email, password, username)'
      });
    }

    // Check if user with email already exists
    const existingEmail = await User.findOne({ email });
    if (existingEmail) {
      return res.status(400).json({
        success: false,
        message: 'Email already in use'
      });
    }

    // Check if user with username already exists
    const existingUsername = await User.findOne({ username });
    if (existingUsername) {
      return res.status(400).json({
        success: false,
        message: 'Username already taken'
      });
    }

    // Create user
    const user = await User.create({
      name: userFullName,
      email,
      password,
      username,
    });

    console.log(`User registered successfully: ${email}`);
    sendTokenResponse(user, 201, res);
  } catch (err) {
    console.error('Registration error:', err);

    // Handle mongoose validation errors
    if (err.name === 'ValidationError') {
      const message = Object.values(err.errors).map(val => val.message).join(', ');
      return res.status(400).json({
        success: false,
        message
      });
    }

    // Handle duplicate key errors
    if (err.code === 11000) {
      const field = Object.keys(err.keyValue)[0];
      return res.status(400).json({
        success: false,
        message: `${field} already exists`
      });
    }

    return res.status(500).json({
      success: false,
      message: 'Server error during registration'
    });
  }
};

// @desc    Login user
// @route   POST /api/auth/login
// @access  Public
exports.login = async (req, res, next) => {
  try {
    const { email, password, rememberMe } = req.body;

    // Validate email & password
    if (!email || !password) {
      return res.status(400).json({
        success: false,
        message: 'Please provide an email and password'
      });
    }

    // Check for user by email first
    let user = await User.findOne({ email }).select('+password');

    // If not found by email, try username
    if (!user) {
      user = await User.findOne({ username: email }).select('+password');
    }

    if (!user) {
      console.log(`Login attempt failed: User with email/username ${email} not found`);
      return res.status(401).json({
        success: false,
        message: 'Invalid credentials'
      });
    }

    // Check if password matches
    try {
      const isMatch = await user.matchPassword(password);

      if (!isMatch) {
        console.log(`Login attempt failed: Password does not match for user ${email}`);
        return res.status(401).json({
          success: false,
          message: 'Invalid credentials'
        });
      }

      // Update user's online status
      await User.findByIdAndUpdate(user._id, {
        isOnline: true,
        lastActive: new Date()
      });

      console.log(`Login successful for user: ${user.username}`);

      // Password matches, send token response
      sendTokenResponse(user, 200, res, rememberMe);
    } catch (passwordError) {
      console.error(`Password comparison error for user ${email}:`, passwordError);
      return res.status(500).json({
        success: false,
        message: 'Authentication error'
      });
    }
  } catch (err) {
    console.error('Login error:', err);
    return res.status(500).json({
      success: false,
      message: 'Server error during login'
    });
  }
};

// @desc    Log user out / clear cookie
// @route   GET /api/auth/logout
// @access  Private
exports.logout = async (req, res, next) => {
  try {
    // Update user's online status if user is authenticated
    if (req.user) {
      await User.findByIdAndUpdate(req.user._id, {
        isOnline: false,
        lastActive: new Date()
      });
    }

    res.cookie('token', 'none', {
      expires: new Date(Date.now() + 10 * 1000),
      httpOnly: true,
    });

    res.status(200).json({
      success: true,
      message: 'Logged out successfully'
    });
  } catch (err) {
    console.error('Logout error:', err);
    res.status(500).json({
      success: false,
      message: 'Server error during logout'
    });
  }
};

// @desc    Get current logged in user
// @route   GET /api/auth/me
// @access  Private
exports.getMe = async (req, res, next) => {
  try {
    if (!req.user || !req.user._id) {
      return res.status(401).json({
        success: false,
        message: 'Not authenticated'
      });
    }

    const user = await User.findById(req.user._id)
      .populate('followersCount')
      .populate('followingCount');

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    res.status(200).json({
      success: true,
      data: user.getPublicProfile()
    });
  } catch (err) {
    console.error('Get me error:', err);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Refresh JWT token
// @route   POST /api/auth/refresh-token
// @access  Public (but requires valid token)
exports.refreshToken = async (req, res, next) => {
  try {
    console.log('Refresh token request received');

    // Get token from request
    let token;
    if (req.headers.authorization) {
      if (req.headers.authorization.startsWith('Bearer ')) {
        token = req.headers.authorization.split(' ')[1];
      } else {
        token = req.headers.authorization;
      }
    } else if (req.body && req.body.token) {
      token = req.body.token;
    }

    if (!token) {
      return res.status(401).json({
        success: false,
        message: 'No token provided'
      });
    }

    try {
      // Verify token (allow expired tokens for refresh)
      const jwt = require('jsonwebtoken');
      const decoded = jwt.verify(token, process.env.JWT_SECRET, {
        ignoreExpiration: true
      });

      // Get user from token
      const user = await User.findById(decoded.id);

      if (!user) {
        return res.status(401).json({
          success: false,
          message: 'User not found'
        });
      }

      // Generate new token
      const newToken = user.getSignedJwtToken();

      // Calculate token expiry date
      const expiryDate = new Date(
        Date.now() + (process.env.JWT_COOKIE_EXPIRE || 30) * 24 * 60 * 60 * 1000
      );

      console.log('Token refreshed successfully for user:', user.username);
      res.status(200).json({
        success: true,
        token: newToken,
        user: user.getPublicProfile(),
        expiresAt: expiryDate.toISOString()
      });
    } catch (verifyError) {
      console.error('Token verification error:', verifyError);
      return res.status(401).json({
        success: false,
        message: 'Invalid token'
      });
    }
  } catch (err) {
    console.error('Token refresh error:', err);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// Get token from model, create cookie and send response
const sendTokenResponse = (user, statusCode, res, rememberMe = false) => {
  // Create token
  const token = user.getSignedJwtToken();

  // Calculate token expiry date - longer if rememberMe is true
  const cookieExpireDays = rememberMe ? 30 : (process.env.JWT_COOKIE_EXPIRE || 30);
  const expiryDate = new Date(
    Date.now() + cookieExpireDays * 24 * 60 * 60 * 1000
  );

  const options = {
    expires: expiryDate,
    httpOnly: true,
  };

  if (process.env.NODE_ENV === 'production') {
    options.secure = true;
  }

  // Use the getPublicProfile method for consistent user data
  const userResponse = user.getPublicProfile();

  res.status(statusCode).cookie('token', token, options).json({
    success: true,
    token,
    user: userResponse,
    expiresAt: expiryDate.toISOString()
  });
};
