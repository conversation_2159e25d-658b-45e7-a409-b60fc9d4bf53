/**
 * Utility to find available ports
 */
const net = require('net');

/**
 * Check if a port is in use
 * @param {number} port - The port to check
 * @returns {Promise<boolean>} - True if port is available, false if in use
 */
const isPortAvailable = (port) => {
  return new Promise((resolve) => {
    const server = net.createServer();
    
    server.once('error', (err) => {
      if (err.code === 'EADDRINUSE') {
        resolve(false); // Port is in use
      } else {
        resolve(false); // Other error, consider port unavailable
      }
    });
    
    server.once('listening', () => {
      // Close the server and resolve with true (port is available)
      server.close(() => {
        resolve(true);
      });
    });
    
    server.listen(port);
  });
};

/**
 * Find an available port starting from the preferred port
 * @param {number} preferredPort - The preferred port to use
 * @param {number} maxAttempts - Maximum number of ports to try
 * @returns {Promise<number>} - An available port
 */
const findAvailablePort = async (preferredPort, maxAttempts = 10) => {
  // First check if the preferred port is available
  const isPreferredAvailable = await isPortAvailable(preferredPort);
  if (isPreferredAvailable) {
    return preferredPort;
  }
  
  // If preferred port is not available, try the next ports
  console.log(`Port ${preferredPort} is in use, searching for an available port...`);
  
  for (let i = 1; i <= maxAttempts; i++) {
    const port = preferredPort + i;
    const isAvailable = await isPortAvailable(port);
    
    if (isAvailable) {
      console.log(`Found available port: ${port}`);
      return port;
    }
  }
  
  // If no port is found, throw an error
  throw new Error(`Could not find an available port after ${maxAttempts} attempts`);
};

module.exports = { isPortAvailable, findAvailablePort };
