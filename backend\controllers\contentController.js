const Post = require('../models/Post');
const Reel = require('../models/Reel');
const Emotion = require('../models/Emotion');
const { createError } = require('../utils/error');

/**
 * Get content by emotion category
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.getContentByEmotion = async (req, res, next) => {
  try {
    const { category, limit = 10, page = 1 } = req.query;
    const skip = (page - 1) * limit;
    
    if (!category) {
      return next(createError(400, 'Emotion category is required'));
    }
    
    // Get emotions in this category
    const emotions = await Emotion.find({ category });
    const emotionIds = emotions.map(emotion => emotion._id);
    
    // Get posts and reels with matching emotions
    const [posts, reels] = await Promise.all([
      Post.find({ 'emotions.emotion': { $in: emotionIds } })
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit)
        .populate('user', 'username name profilePicture isVerified')
        .populate('emotions.emotion', 'name color icon category'),
      
      Reel.find({ 'emotions.emotion': { $in: emotionIds } })
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit)
        .populate('user', 'username name profilePicture isVerified')
        .populate('emotions.emotion', 'name color icon category'),
    ]);
    
    // Format posts and reels
    const formattedPosts = posts.map(post => ({
      ...post.toObject(),
      type: 'post',
    }));
    
    const formattedReels = reels.map(reel => ({
      ...reel.toObject(),
      type: 'reel',
    }));
    
    // Combine, sort by creation date, and limit
    const combinedContent = [...formattedPosts, ...formattedReels]
      .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
      .slice(0, limit);
    
    res.status(200).json({
      success: true,
      count: combinedContent.length,
      data: combinedContent,
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Get shared emotional experiences
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.getSharedEmotionalExperiences = async (req, res, next) => {
  try {
    const { emotion, limit = 10, page = 1 } = req.query;
    const skip = (page - 1) * limit;
    
    if (!emotion) {
      return next(createError(400, 'Emotion ID is required'));
    }
    
    // Get posts and reels with the specific emotion
    const [posts, reels] = await Promise.all([
      Post.find({ 'emotions.emotion': emotion })
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit)
        .populate('user', 'username name profilePicture isVerified')
        .populate('emotions.emotion', 'name color icon category'),
      
      Reel.find({ 'emotions.emotion': emotion })
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit)
        .populate('user', 'username name profilePicture isVerified')
        .populate('emotions.emotion', 'name color icon category'),
    ]);
    
    // Format posts and reels
    const formattedPosts = posts.map(post => ({
      ...post.toObject(),
      type: 'post',
    }));
    
    const formattedReels = reels.map(reel => ({
      ...reel.toObject(),
      type: 'reel',
    }));
    
    // Combine, sort by creation date, and limit
    const combinedContent = [...formattedPosts, ...formattedReels]
      .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
      .slice(0, limit);
    
    res.status(200).json({
      success: true,
      count: combinedContent.length,
      data: combinedContent,
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Get content with similar emotions to a specific post or reel
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.getSimilarEmotionalContent = async (req, res, next) => {
  try {
    const { type, id, limit = 10 } = req.query;
    
    if (!type || !id) {
      return next(createError(400, 'Content type and ID are required'));
    }
    
    // Get the source content
    let sourceContent;
    if (type === 'post') {
      sourceContent = await Post.findById(id).populate('emotions.emotion');
    } else if (type === 'reel') {
      sourceContent = await Reel.findById(id).populate('emotions.emotion');
    } else {
      return next(createError(400, 'Invalid content type. Must be post or reel'));
    }
    
    if (!sourceContent) {
      return next(createError(404, `${type.charAt(0).toUpperCase() + type.slice(1)} not found`));
    }
    
    // Get emotions from the source content
    const emotionIds = sourceContent.emotions.map(emotion => emotion.emotion._id);
    
    if (emotionIds.length === 0) {
      return res.status(200).json({
        success: true,
        count: 0,
        data: [],
      });
    }
    
    // Get posts and reels with similar emotions, excluding the source content
    const [posts, reels] = await Promise.all([
      Post.find({
        'emotions.emotion': { $in: emotionIds },
        _id: { $ne: type === 'post' ? id : null },
      })
        .sort({ createdAt: -1 })
        .limit(limit)
        .populate('user', 'username name profilePicture isVerified')
        .populate('emotions.emotion', 'name color icon category'),
      
      Reel.find({
        'emotions.emotion': { $in: emotionIds },
        _id: { $ne: type === 'reel' ? id : null },
      })
        .sort({ createdAt: -1 })
        .limit(limit)
        .populate('user', 'username name profilePicture isVerified')
        .populate('emotions.emotion', 'name color icon category'),
    ]);
    
    // Format posts and reels
    const formattedPosts = posts.map(post => ({
      ...post.toObject(),
      type: 'post',
    }));
    
    const formattedReels = reels.map(reel => ({
      ...reel.toObject(),
      type: 'reel',
    }));
    
    // Combine, sort by creation date, and limit
    const combinedContent = [...formattedPosts, ...formattedReels]
      .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
      .slice(0, limit);
    
    res.status(200).json({
      success: true,
      count: combinedContent.length,
      data: combinedContent,
    });
  } catch (err) {
    next(err);
  }
};
