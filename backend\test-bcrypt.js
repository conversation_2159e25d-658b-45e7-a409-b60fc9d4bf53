const bcrypt = require('bcryptjs');

// Test bcrypt functionality
async function testBcrypt() {
  try {
    console.log('Testing bcrypt functionality...');
    
    // Generate a salt
    console.log('Generating salt...');
    const salt = await bcrypt.genSalt(10);
    console.log('Salt generated:', salt);
    
    // Hash a password
    const password = 'testpassword123';
    console.log('Hashing password:', password);
    const hashedPassword = await bcrypt.hash(password, salt);
    console.log('Hashed password:', hashedPassword);
    
    // Compare passwords
    console.log('Comparing correct password...');
    const correctMatch = await bcrypt.compare(password, hashedPassword);
    console.log('Correct password match result:', correctMatch);
    
    console.log('Comparing incorrect password...');
    const incorrectMatch = await bcrypt.compare('wrongpassword', hashedPassword);
    console.log('Incorrect password match result:', incorrectMatch);
    
    console.log('Bcrypt test completed successfully!');
  } catch (error) {
    console.error('Bcrypt test failed:', error);
  }
}

// Run the test
testBcrypt();
