const express = require('express');
const router = express.Router();
const { protect } = require('../middleware/auth');
const {
  getThemePreference,
  updateThemePreference
} = require('../controllers/themeController');

// All routes require authentication
router.use(protect);

// Get and update theme preference
router.get('/', getThemePreference);
router.put('/', updateThemePreference);

module.exports = router;
