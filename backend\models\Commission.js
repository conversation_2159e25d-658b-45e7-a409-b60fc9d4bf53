const mongoose = require('mongoose');

const CommissionSchema = new mongoose.Schema({
  transactionId: {
    type: String,
    unique: true,
    required: true
  },
  order: {
    type: mongoose.Schema.ObjectId,
    ref: 'Order',
    required: true
  },
  vendor: {
    type: mongoose.Schema.ObjectId,
    ref: 'Vendor',
    required: true
  },
  buyer: {
    type: mongoose.Schema.ObjectId,
    ref: 'User',
    required: true
  },
  product: {
    type: mongoose.Schema.ObjectId,
    ref: 'Product',
    required: true
  },
  amounts: {
    grossAmount: {
      type: Number,
      required: true,
      min: 0
    },
    platformCommission: {
      type: Number,
      required: true,
      min: 0
    },
    vendorEarnings: {
      type: Number,
      required: true,
      min: 0
    },
    paymentProcessingFee: {
      type: Number,
      default: 0,
      min: 0
    },
    taxes: {
      type: Number,
      default: 0,
      min: 0
    },
    netAmount: {
      type: Number,
      required: true,
      min: 0
    }
  },
  currency: {
    type: String,
    required: true,
    default: 'USD',
    uppercase: true
  },
  commissionRate: {
    percentage: {
      type: Number,
      required: true,
      min: 0,
      max: 100
    },
    type: {
      type: String,
      enum: ['fixed', 'percentage', 'tiered'],
      default: 'percentage'
    },
    tierDetails: {
      tier: String,
      minAmount: Number,
      maxAmount: Number
    }
  },
  paymentMethod: {
    type: String,
    enum: ['stripe', 'paypal', 'mobile_money', 'bank_transfer', 'crypto'],
    required: true
  },
  status: {
    type: String,
    enum: ['pending', 'calculated', 'processed', 'paid_out', 'disputed', 'refunded'],
    default: 'pending'
  },
  payoutStatus: {
    type: String,
    enum: ['not_eligible', 'pending', 'processing', 'completed', 'failed', 'cancelled'],
    default: 'not_eligible'
  },
  taxes: {
    vatRate: {
      type: Number,
      default: 0,
      min: 0,
      max: 100
    },
    vatAmount: {
      type: Number,
      default: 0,
      min: 0
    },
    salesTaxRate: {
      type: Number,
      default: 0,
      min: 0,
      max: 100
    },
    salesTaxAmount: {
      type: Number,
      default: 0,
      min: 0
    },
    withholdingTaxRate: {
      type: Number,
      default: 0,
      min: 0,
      max: 100
    },
    withholdingTaxAmount: {
      type: Number,
      default: 0,
      min: 0
    },
    taxRegion: String,
    taxExempt: {
      type: Boolean,
      default: false
    }
  },
  exchangeRate: {
    rate: {
      type: Number,
      default: 1
    },
    fromCurrency: String,
    toCurrency: String,
    provider: String,
    timestamp: Date
  },
  breakdown: {
    itemPrice: Number,
    shipping: Number,
    discount: Number,
    couponDiscount: Number,
    refundAmount: Number
  },
  timeline: [{
    status: String,
    timestamp: {
      type: Date,
      default: Date.now
    },
    note: String,
    performedBy: {
      type: mongoose.Schema.ObjectId,
      ref: 'User'
    }
  }],
  payoutRequest: {
    type: mongoose.Schema.ObjectId,
    ref: 'PayoutRequest'
  },
  dispute: {
    isDisputed: {
      type: Boolean,
      default: false
    },
    disputeReason: String,
    disputeDate: Date,
    resolution: String,
    resolvedDate: Date
  },
  compliance: {
    kycVerified: {
      type: Boolean,
      default: false
    },
    taxFormSubmitted: {
      type: Boolean,
      default: false
    },
    sanctionsChecked: {
      type: Boolean,
      default: false
    },
    amlCleared: {
      type: Boolean,
      default: false
    }
  },
  metadata: {
    campaignId: String,
    affiliateId: String,
    promoCode: String,
    referralSource: String,
    customFields: mongoose.Schema.Types.Mixed
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Generate unique transaction ID
CommissionSchema.pre('save', function(next) {
  if (this.isNew && !this.transactionId) {
    this.transactionId = `TXN-${Date.now()}-${Math.random().toString(36).substr(2, 8).toUpperCase()}`;
  }
  this.updatedAt = Date.now();
  next();
});

// Calculate commission amounts
CommissionSchema.methods.calculateCommission = function(grossAmount, commissionRate, taxes = {}) {
  const vatAmount = (grossAmount * (taxes.vatRate || 0)) / 100;
  const salesTaxAmount = (grossAmount * (taxes.salesTaxRate || 0)) / 100;
  const totalTaxes = vatAmount + salesTaxAmount;
  
  const taxableAmount = grossAmount - totalTaxes;
  const platformCommission = (taxableAmount * commissionRate) / 100;
  const vendorEarnings = taxableAmount - platformCommission;
  
  const withholdingTaxAmount = (vendorEarnings * (taxes.withholdingTaxRate || 0)) / 100;
  const netAmount = vendorEarnings - withholdingTaxAmount;

  this.amounts = {
    grossAmount,
    platformCommission,
    vendorEarnings,
    paymentProcessingFee: this.amounts?.paymentProcessingFee || 0,
    taxes: totalTaxes,
    netAmount
  };

  this.taxes = {
    vatRate: taxes.vatRate || 0,
    vatAmount,
    salesTaxRate: taxes.salesTaxRate || 0,
    salesTaxAmount,
    withholdingTaxRate: taxes.withholdingTaxRate || 0,
    withholdingTaxAmount,
    taxRegion: taxes.taxRegion,
    taxExempt: taxes.taxExempt || false
  };

  return this.amounts;
};

// Add timeline entry
CommissionSchema.methods.addTimelineEntry = function(status, note, performedBy) {
  this.timeline.push({
    status,
    note,
    performedBy,
    timestamp: new Date()
  });
  return this.save();
};

// Update status
CommissionSchema.methods.updateStatus = function(status, note, performedBy) {
  this.status = status;
  return this.addTimelineEntry(status, note, performedBy);
};

// Mark as eligible for payout
CommissionSchema.methods.markEligibleForPayout = function() {
  if (this.status === 'processed' && this.compliance.kycVerified) {
    this.payoutStatus = 'pending';
    return this.addTimelineEntry('payout_eligible', 'Marked as eligible for payout');
  }
  return false;
};

// Apply exchange rate
CommissionSchema.methods.applyExchangeRate = function(rate, fromCurrency, toCurrency, provider) {
  this.exchangeRate = {
    rate,
    fromCurrency,
    toCurrency,
    provider,
    timestamp: new Date()
  };

  // Convert amounts
  Object.keys(this.amounts).forEach(key => {
    if (typeof this.amounts[key] === 'number') {
      this.amounts[key] = this.amounts[key] * rate;
    }
  });

  this.currency = toCurrency;
  return this.save();
};

// Create indexes for better performance
CommissionSchema.index({ transactionId: 1 });
CommissionSchema.index({ order: 1 });
CommissionSchema.index({ vendor: 1, status: 1 });
CommissionSchema.index({ buyer: 1 });
CommissionSchema.index({ status: 1, payoutStatus: 1 });
CommissionSchema.index({ createdAt: -1 });
CommissionSchema.index({ 'amounts.grossAmount': -1 });
CommissionSchema.index({ currency: 1 });
CommissionSchema.index({ paymentMethod: 1 });

module.exports = mongoose.model('Commission', CommissionSchema);
