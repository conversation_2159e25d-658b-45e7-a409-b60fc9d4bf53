const mongoose = require('mongoose');

const ContentModerationSchema = new mongoose.Schema({
  // Content reference (can be a post, reel, comment, message, or stream chat)
  contentType: {
    type: String,
    enum: ['post', 'reel', 'comment', 'message', 'stream_chat', 'user_profile'],
    required: true
  },
  contentId: {
    type: mongoose.Schema.Types.ObjectId,
    required: true,
    refPath: 'contentType'
  },
  // User who created the content
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  // AI moderation results
  aiModeration: {
    // Overall confidence score (0-1)
    score: {
      type: Number,
      min: 0,
      max: 1,
      default: 0
    },
    // Specific violation categories with confidence scores
    categories: {
      sexual: {
        type: Number,
        min: 0,
        max: 1,
        default: 0
      },
      violence: {
        type: Number,
        min: 0,
        max: 1,
        default: 0
      },
      hate: {
        type: Number,
        min: 0,
        max: 1,
        default: 0
      },
      harassment: {
        type: Number,
        min: 0,
        max: 1,
        default: 0
      },
      selfHarm: {
        type: Number,
        min: 0,
        max: 1,
        default: 0
      },
      sexualMinors: {
        type: Number,
        min: 0,
        max: 1,
        default: 0
      },
      hateThreatening: {
        type: Number,
        min: 0,
        max: 1,
        default: 0
      },
      violenceGraphic: {
        type: Number,
        min: 0,
        max: 1,
        default: 0
      },
      selfHarmIntent: {
        type: Number,
        min: 0,
        max: 1,
        default: 0
      },
      selfHarmInstructions: {
        type: Number,
        min: 0,
        max: 1,
        default: 0
      },
      harassmentThreatening: {
        type: Number,
        min: 0,
        max: 1,
        default: 0
      },
      spam: {
        type: Number,
        min: 0,
        max: 1,
        default: 0
      }
    },
    // Detected keywords that triggered moderation
    flaggedKeywords: [String],
    // AI-suggested moderation action
    suggestedAction: {
      type: String,
      enum: ['none', 'flag', 'hide', 'remove'],
      default: 'none'
    },
    // AI-generated explanation for moderation
    explanation: {
      type: String,
      default: ''
    }
  },
  // Moderation status
  status: {
    type: String,
    enum: ['pending', 'approved', 'rejected', 'auto_rejected', 'auto_approved'],
    default: 'pending'
  },
  // Moderation action taken
  action: {
    type: String,
    enum: ['none', 'hidden', 'removed', 'user_warned', 'user_suspended', 'user_banned'],
    default: 'none'
  },
  // Moderator who reviewed the content (if applicable)
  moderator: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    default: null
  },
  // Moderator notes
  notes: {
    type: String,
    default: ''
  },
  // Timestamps
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  },
  reviewedAt: {
    type: Date,
    default: null
  }
});

// Create indexes for efficient queries
ContentModerationSchema.index({ contentType: 1, contentId: 1 }, { unique: true });
ContentModerationSchema.index({ user: 1 });
ContentModerationSchema.index({ status: 1 });
ContentModerationSchema.index({ 'aiModeration.score': 1 });
ContentModerationSchema.index({ createdAt: 1 });

module.exports = mongoose.model('ContentModeration', ContentModerationSchema);
