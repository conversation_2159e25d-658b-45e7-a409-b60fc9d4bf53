const VoiceCommand = require('../models/VoiceCommand');
const User = require('../models/User');
const LiveStream = require('../models/LiveStream');
const asyncHandler = require('../middleware/async');
const ErrorResponse = require('../utils/errorResponse');
const socketEmitter = require('../utils/socketEmitter');

/**
 * Get all voice commands for a user
 * @route GET /api/voice-commands
 * @access Private
 */
exports.getVoiceCommands = asyncHandler(async (req, res, next) => {
  const commands = await VoiceCommand.find({ user: req.user.id });

  res.status(200).json({
    success: true,
    count: commands.length,
    data: commands,
  });
});

/**
 * Create a new voice command
 * @route POST /api/voice-commands
 * @access Private
 */
exports.createVoiceCommand = asyncHandler(async (req, res, next) => {
  const { command, action, customAction, parameters } = req.body;

  // Validate input
  if (!command) {
    return next(new ErrorResponse('Please provide a command phrase', 400));
  }

  if (!action) {
    return next(new ErrorResponse('Please provide an action', 400));
  }

  // Check if command already exists for this user
  const existingCommand = await VoiceCommand.findOne({
    user: req.user.id,
    command,
  });

  if (existingCommand) {
    return next(new ErrorResponse('Command phrase already exists for this user', 400));
  }

  // Create command
  const voiceCommand = await VoiceCommand.create({
    user: req.user.id,
    command,
    action,
    customAction: action === 'custom' ? customAction : undefined,
    parameters: parameters || {},
    isCustom: action === 'custom',
  });

  res.status(201).json({
    success: true,
    data: voiceCommand,
  });
});

/**
 * Update a voice command
 * @route PUT /api/voice-commands/:id
 * @access Private
 */
exports.updateVoiceCommand = asyncHandler(async (req, res, next) => {
  let command = await VoiceCommand.findById(req.params.id);

  if (!command) {
    return next(new ErrorResponse(`Voice command not found with id of ${req.params.id}`, 404));
  }

  // Check if user owns the command
  if (command.user.toString() !== req.user.id) {
    return next(new ErrorResponse('Not authorized to update this command', 403));
  }

  // Update command
  command = await VoiceCommand.findByIdAndUpdate(req.params.id, req.body, {
    new: true,
    runValidators: true,
  });

  res.status(200).json({
    success: true,
    data: command,
  });
});

/**
 * Delete a voice command
 * @route DELETE /api/voice-commands/:id
 * @access Private
 */
exports.deleteVoiceCommand = asyncHandler(async (req, res, next) => {
  const command = await VoiceCommand.findById(req.params.id);

  if (!command) {
    return next(new ErrorResponse(`Voice command not found with id of ${req.params.id}`, 404));
  }

  // Check if user owns the command
  if (command.user.toString() !== req.user.id) {
    return next(new ErrorResponse('Not authorized to delete this command', 403));
  }

  // Delete command
  await command.remove();

  res.status(200).json({
    success: true,
    data: {},
  });
});

/**
 * Execute a voice command
 * @route POST /api/live-streams/:streamId/voice-commands/execute
 * @access Private
 */
exports.executeVoiceCommand = asyncHandler(async (req, res, next) => {
  const { commandText, parameters } = req.body;
  const streamId = req.params.streamId;

  // Validate input
  if (!commandText) {
    return next(new ErrorResponse('Please provide a command text', 400));
  }

  // Get stream
  const stream = await LiveStream.findById(streamId);
  if (!stream) {
    return next(new ErrorResponse(`Stream not found with id of ${streamId}`, 404));
  }

  // Check if voice commands are enabled for this stream
  if (!stream.settings.voiceCommands.enabled) {
    return next(new ErrorResponse('Voice commands are not enabled for this stream', 400));
  }

  // Check if user is authorized to use voice commands
  const isOwner = stream.user.toString() === req.user.id;
  const isCoHost = stream.coHosts.some(id => id.toString() === req.user.id);
  const isViewer = !isOwner && !isCoHost;

  if (isViewer && !stream.settings.voiceCommands.allowViewerCommands) {
    return next(new ErrorResponse('Viewers are not allowed to use voice commands in this stream', 403));
  }

  // Find matching command
  let matchedCommand = null;

  // First check user's custom commands
  const userCommands = await VoiceCommand.find({ user: req.user.id, isActive: true });
  
  for (const cmd of userCommands) {
    if (commandText.toLowerCase().includes(cmd.command.toLowerCase())) {
      matchedCommand = cmd;
      break;
    }
  }

  // If no custom command found, check for built-in commands
  if (!matchedCommand) {
    // Built-in commands mapping
    const builtInCommands = {
      'mute chat': 'mute_chat',
      'unmute chat': 'unmute_chat',
      'highlight': 'highlight_clip',
      'clip this': 'highlight_clip',
      'screenshot': 'take_screenshot',
      'take a picture': 'take_screenshot',
      'start recording': 'start_recording',
      'stop recording': 'stop_recording',
      'camera off': 'toggle_camera',
      'camera on': 'toggle_camera',
      'mic off': 'toggle_microphone',
      'mic on': 'toggle_microphone',
      'change layout': 'change_layout',
      'add effect': 'add_effect',
      'remove effect': 'remove_effect',
      'send reaction': 'send_reaction',
      'pin message': 'pin_message',
      'block user': 'block_user',
      'promote viewer': 'promote_viewer',
      'change mode': 'change_mode',
    };

    for (const [phrase, action] of Object.entries(builtInCommands)) {
      if (commandText.toLowerCase().includes(phrase)) {
        // Check if viewer is allowed to use this command
        if (isViewer && !stream.settings.voiceCommands.viewerCommandPermissions.includes(action)) {
          return next(new ErrorResponse(`Viewers are not allowed to use the '${action}' command`, 403));
        }

        matchedCommand = {
          command: phrase,
          action,
          parameters: {},
          isBuiltIn: true,
        };
        break;
      }
    }
  }

  if (!matchedCommand) {
    return next(new ErrorResponse('No matching command found', 404));
  }

  // Update usage count if it's a custom command
  if (!matchedCommand.isBuiltIn) {
    matchedCommand.usageCount += 1;
    matchedCommand.lastUsed = Date.now();
    await matchedCommand.save();
  }

  // Execute command
  const commandResult = {
    command: matchedCommand.command,
    action: matchedCommand.action,
    parameters: parameters || matchedCommand.parameters,
    timestamp: Date.now(),
    user: {
      _id: req.user.id,
      name: req.user.name,
      username: req.user.username,
    },
    stream: {
      _id: stream._id,
      title: stream.title,
    },
  };

  // Emit socket event for command execution
  socketEmitter.emitVoiceCommandExecuted(streamId, commandResult);

  res.status(200).json({
    success: true,
    data: commandResult,
  });
});

/**
 * Get voice command settings
 * @route GET /api/users/me/voice-command-settings
 * @access Private
 */
exports.getVoiceCommandSettings = asyncHandler(async (req, res, next) => {
  const user = await User.findById(req.user.id);

  if (!user) {
    return next(new ErrorResponse('User not found', 404));
  }

  res.status(200).json({
    success: true,
    data: {
      enabled: user.voiceCommandsEnabled,
      settings: user.voiceCommandSettings,
    },
  });
});

/**
 * Update voice command settings
 * @route PUT /api/users/me/voice-command-settings
 * @access Private
 */
exports.updateVoiceCommandSettings = asyncHandler(async (req, res, next) => {
  const { enabled, settings } = req.body;

  const user = await User.findById(req.user.id);

  if (!user) {
    return next(new ErrorResponse('User not found', 404));
  }

  // Update settings
  if (enabled !== undefined) {
    user.voiceCommandsEnabled = enabled;
  }

  if (settings) {
    user.voiceCommandSettings = {
      ...user.voiceCommandSettings,
      ...settings,
    };
  }

  await user.save();

  res.status(200).json({
    success: true,
    data: {
      enabled: user.voiceCommandsEnabled,
      settings: user.voiceCommandSettings,
    },
  });
});
