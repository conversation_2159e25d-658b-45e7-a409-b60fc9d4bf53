const express = require('express');
const router = express.Router();
const { protect } = require('../middleware/auth');
const {
  followUser,
  unfollowUser,
  getFollowers,
  getFollowing,
  checkFollow,
} = require('../controllers/followController');

// Protected routes
router.use(protect);
router.post('/:userId', followUser);
router.delete('/:userId', unfollowUser);
router.get('/followers/:userId', getFollowers);
router.get('/following/:userId', getFollowing);
router.get('/check/:userId', checkFollow);

module.exports = router;
