const express = require('express');
const router = express.Router();
const { protect } = require('../middleware/auth');
const { upload, processUploadedFiles } = require('../middleware/fileUpload');
const {
  getConversations,
  getConversation,
  createOneOnOneConversation,
  createGroupConversation,
  addParticipants,
  removeParticipant,
  leaveGroup,
  updateGroupConversation,
  changeParticipantRole,
  muteConversation,
  unmuteConversation,
  pinConversation,
  unpinConversation,
} = require('../controllers/conversationController');

// All routes require authentication
router.use(protect);

// Get all conversations for the current user
router.get('/', getConversations);

// Get a single conversation by ID
router.get('/:conversationId', getConversation);

// Create a new one-on-one conversation
router.post('/one-on-one', createOneOnOneConversation);

// Create a new group conversation
router.post('/group', upload.single('groupImage'), processUploadedFiles, createGroupConversation);

// Add participants to a group conversation
router.post('/:conversationId/participants', addParticipants);

// Remove a participant from a group conversation
router.delete('/:conversationId/participants/:userId', removeParticipant);

// Leave a group conversation
router.delete('/:conversationId/leave', leaveGroup);

// Update group conversation details
router.put('/:conversationId', upload.single('groupImage'), processUploadedFiles, updateGroupConversation);

// Change participant role in a group conversation
router.put('/:conversationId/participants/:userId/role', changeParticipantRole);

// Mute a conversation
router.post('/:conversationId/mute', muteConversation);

// Unmute a conversation
router.delete('/:conversationId/mute', unmuteConversation);

// Pin a conversation
router.post('/:conversationId/pin', pinConversation);

// Unpin a conversation
router.delete('/:conversationId/pin', unpinConversation);

module.exports = router;
