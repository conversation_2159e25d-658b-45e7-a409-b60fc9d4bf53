import React, { useState, useEffect } from 'react'
import {
  Container,
  Typography,
  Box,
  Grid,
  Card,
  CardMedia,
  CardContent,
  CardActions,
  IconButton,
  Tabs,
  Tab,
  CircularProgress,
  Chip,
  Avatar,
  Dialog,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  InputAdornment,
  Fab,
  Paper
} from '@mui/material'
import {
  Favorite,
  FavoriteBorder,
  Comment,
  Share,
  Search,
  FilterList,
  Add,
  PlayArrow,
  Close,
  PhotoLibrary,
  VideoLibrary,
  AutoStories
} from '@mui/icons-material'
import { useSnackbar } from 'notistack'
import { useNavigate } from 'react-router-dom'
import { useAuth } from '../context/AuthContext'
import axios from '../utils/fixedAxios'

const GalleryPage = () => {
  const [activeTab, setActiveTab] = useState(0)
  const [posts, setPosts] = useState([])
  const [reels, setReels] = useState([])
  const [stories, setStories] = useState([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedMedia, setSelectedMedia] = useState(null)
  const [likedPosts, setLikedPosts] = useState(new Set())

  const { user } = useAuth()
  const { enqueueSnackbar } = useSnackbar()
  const navigate = useNavigate()

  useEffect(() => {
    fetchGalleryData()
  }, [activeTab])

  const fetchGalleryData = async () => {
    try {
      setLoading(true)

      const endpoints = [
        '/api/posts?type=image',
        '/api/reels',
        '/api/stories'
      ]

      const [postsRes, reelsRes, storiesRes] = await Promise.allSettled(
        endpoints.map(endpoint => axios.get(endpoint))
      )

      if (postsRes.status === 'fulfilled') {
        setPosts(postsRes.value.data.data || postsRes.value.data.posts || [])
      }

      if (reelsRes.status === 'fulfilled') {
        setReels(reelsRes.value.data.data || reelsRes.value.data.reels || [])
      }

      if (storiesRes.status === 'fulfilled') {
        setStories(storiesRes.value.data.data || storiesRes.value.data.stories || [])
      }

    } catch (error) {
      console.error('Error fetching gallery data:', error)
      enqueueSnackbar('Failed to load gallery', { variant: 'error' })
    } finally {
      setLoading(false)
    }
  }

  const handleLike = async (postId, type = 'post') => {
    try {
      const isLiked = likedPosts.has(postId)

      if (isLiked) {
        await axios.delete(`/api/likes/${type}/${postId}`)
        setLikedPosts(prev => {
          const newSet = new Set(prev)
          newSet.delete(postId)
          return newSet
        })
      } else {
        await axios.post('/api/likes', {
          type: type,
          targetId: postId
        })
        setLikedPosts(prev => new Set(prev).add(postId))
      }

      // Update likes count
      if (type === 'reel') {
        setReels(prev => prev.map(reel =>
          reel._id === postId
            ? { ...reel, likesCount: (reel.likesCount || 0) + (isLiked ? -1 : 1) }
            : reel
        ))
      } else {
        setPosts(prev => prev.map(post =>
          post._id === postId
            ? { ...post, likesCount: (post.likesCount || 0) + (isLiked ? -1 : 1) }
            : post
        ))
      }

    } catch (error) {
      console.error('Error liking content:', error)
      enqueueSnackbar('Failed to like content', { variant: 'error' })
    }
  }

  const getCurrentData = () => {
    switch (activeTab) {
      case 0: return posts
      case 1: return reels
      case 2: return stories
      default: return []
    }
  }

  const filteredData = getCurrentData().filter(item =>
    item.caption?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    item.user?.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    item.user?.username?.toLowerCase().includes(searchQuery.toLowerCase())
  )

  const tabs = [
    { label: 'Photos', icon: <PhotoLibrary /> },
    { label: 'Reels', icon: <VideoLibrary /> },
    { label: 'Stories', icon: <AutoStories /> }
  ]

  const MediaCard = ({ item, type }) => (
    <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      <Box sx={{ position: 'relative' }}>
        <CardMedia
          component={type === 'reel' ? 'video' : 'img'}
          height="200"
          image={item.media?.[0]?.url || item.image}
          alt={item.caption || 'Media'}
          sx={{ cursor: 'pointer' }}
          onClick={() => setSelectedMedia({ ...item, type })}
        />
        {type === 'reel' && (
          <Box
            sx={{
              position: 'absolute',
              top: '50%',
              left: '50%',
              transform: 'translate(-50%, -50%)',
              bgcolor: 'rgba(0,0,0,0.6)',
              borderRadius: '50%',
              p: 1
            }}
          >
            <PlayArrow sx={{ color: 'white', fontSize: 30 }} />
          </Box>
        )}
        {type === 'story' && (
          <Chip
            label="Story"
            size="small"
            sx={{
              position: 'absolute',
              top: 8,
              right: 8,
              bgcolor: 'primary.main',
              color: 'white'
            }}
          />
        )}
      </Box>

      <CardContent sx={{ flexGrow: 1, pb: 1 }}>
        <Box display="flex" alignItems="center" mb={1}>
          <Avatar src={item.user?.avatar} sx={{ width: 32, height: 32, mr: 1 }}>
            {item.user?.name?.charAt(0)?.toUpperCase()}
          </Avatar>
          <Box>
            <Typography variant="subtitle2" fontWeight="medium">
              {item.user?.name}
            </Typography>
            <Typography variant="caption" color="text.secondary">
              @{item.user?.username}
            </Typography>
          </Box>
        </Box>

        {item.caption && (
          <Typography variant="body2" color="text.secondary" noWrap>
            {item.caption}
          </Typography>
        )}
      </CardContent>

      <CardActions sx={{ pt: 0 }}>
        <IconButton
          size="small"
          onClick={() => handleLike(item._id, type)}
          color={likedPosts.has(item._id) ? 'error' : 'default'}
        >
          {likedPosts.has(item._id) ? <Favorite /> : <FavoriteBorder />}
        </IconButton>
        <Typography variant="caption">
          {item.likesCount || 0}
        </Typography>

        <IconButton size="small">
          <Comment />
        </IconButton>
        <Typography variant="caption">
          {item.commentsCount || 0}
        </Typography>

        <IconButton size="small">
          <Share />
        </IconButton>
      </CardActions>
    </Card>
  )

  return (
    <Container maxWidth="lg" sx={{ py: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 3 }}>
        <Typography variant="h4" component="h1" fontWeight="bold">
          Gallery
        </Typography>
        <Fab
          color="primary"
          size="medium"
          onClick={() => navigate('/create')}
        >
          <Add />
        </Fab>
      </Box>

      {/* Search and Filter */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
          <TextField
            fullWidth
            size="small"
            placeholder="Search gallery..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Search />
                </InputAdornment>
              )
            }}
          />
          <IconButton>
            <FilterList />
          </IconButton>
        </Box>
      </Paper>

      {/* Tabs */}
      <Tabs
        value={activeTab}
        onChange={(e, newValue) => setActiveTab(newValue)}
        sx={{ mb: 3 }}
        variant="fullWidth"
      >
        {tabs.map((tab, index) => (
          <Tab
            key={index}
            label={tab.label}
            icon={tab.icon}
            iconPosition="start"
          />
        ))}
      </Tabs>

      {/* Content */}
      {loading ? (
        <Box display="flex" justifyContent="center" py={6}>
          <CircularProgress />
        </Box>
      ) : filteredData.length === 0 ? (
        <Box textAlign="center" py={6}>
          <Typography variant="h6" gutterBottom>
            No {tabs[activeTab].label.toLowerCase()} found
          </Typography>
          <Typography variant="body2" color="text.secondary" gutterBottom>
            {searchQuery ? 'Try adjusting your search' : `No ${tabs[activeTab].label.toLowerCase()} available yet`}
          </Typography>
          <Button
            variant="contained"
            startIcon={<Add />}
            onClick={() => navigate('/create')}
            sx={{ mt: 2 }}
          >
            Create {tabs[activeTab].label.slice(0, -1)}
          </Button>
        </Box>
      ) : (
        <Grid container spacing={3}>
          {filteredData.map((item) => (
            <Grid item xs={12} sm={6} md={4} lg={3} key={item._id}>
              <MediaCard
                item={item}
                type={activeTab === 0 ? 'post' : activeTab === 1 ? 'reel' : 'story'}
              />
            </Grid>
          ))}
        </Grid>
      )}

      {/* Media Viewer Dialog */}
      <Dialog
        open={!!selectedMedia}
        onClose={() => setSelectedMedia(null)}
        maxWidth="md"
        fullWidth
      >
        {selectedMedia && (
          <>
            <DialogContent sx={{ p: 0 }}>
              <Box sx={{ position: 'relative' }}>
                <IconButton
                  sx={{ position: 'absolute', top: 8, right: 8, bgcolor: 'rgba(0,0,0,0.5)', color: 'white' }}
                  onClick={() => setSelectedMedia(null)}
                >
                  <Close />
                </IconButton>

                {selectedMedia.type === 'reel' ? (
                  <video
                    controls
                    style={{ width: '100%', maxHeight: '70vh' }}
                    src={selectedMedia.media?.[0]?.url}
                  />
                ) : (
                  <img
                    src={selectedMedia.media?.[0]?.url || selectedMedia.image}
                    alt={selectedMedia.caption}
                    style={{ width: '100%', maxHeight: '70vh', objectFit: 'contain' }}
                  />
                )}
              </Box>

              <Box sx={{ p: 2 }}>
                <Box display="flex" alignItems="center" mb={2}>
                  <Avatar src={selectedMedia.user?.avatar} sx={{ mr: 2 }}>
                    {selectedMedia.user?.name?.charAt(0)?.toUpperCase()}
                  </Avatar>
                  <Box>
                    <Typography variant="subtitle1" fontWeight="medium">
                      {selectedMedia.user?.name}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      @{selectedMedia.user?.username}
                    </Typography>
                  </Box>
                </Box>

                {selectedMedia.caption && (
                  <Typography variant="body1" mb={2}>
                    {selectedMedia.caption}
                  </Typography>
                )}

                <Box display="flex" alignItems="center" gap={2}>
                  <IconButton
                    onClick={() => handleLike(selectedMedia._id, selectedMedia.type)}
                    color={likedPosts.has(selectedMedia._id) ? 'error' : 'default'}
                  >
                    {likedPosts.has(selectedMedia._id) ? <Favorite /> : <FavoriteBorder />}
                  </IconButton>
                  <Typography variant="body2">
                    {selectedMedia.likesCount || 0} likes
                  </Typography>

                  <IconButton>
                    <Comment />
                  </IconButton>
                  <Typography variant="body2">
                    {selectedMedia.commentsCount || 0} comments
                  </Typography>

                  <IconButton>
                    <Share />
                  </IconButton>
                </Box>
              </Box>
            </DialogContent>
          </>
        )}
      </Dialog>
    </Container>
  )
}

export default GalleryPage
