const mongoose = require('mongoose');

const NFTAccessSchema = new mongoose.Schema({
  stream: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'LiveStream',
    required: true,
  },
  creator: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  name: {
    type: String,
    required: [true, 'Please provide a name for the NFT access token'],
    trim: true,
    maxlength: [100, 'Name cannot be more than 100 characters'],
  },
  description: {
    type: String,
    trim: true,
    maxlength: [500, 'Description cannot be more than 500 characters'],
  },
  imageUrl: {
    type: String,
    required: true,
  },
  contractAddress: {
    type: String,
    required: true,
  },
  tokenIds: [{
    type: String,
  }],
  blockchain: {
    type: String,
    enum: ['ethereum', 'polygon', 'solana', 'binance'],
    default: 'polygon',
  },
  accessType: {
    type: String,
    enum: ['full', 'vip', 'early', 'exclusive', 'limited'],
    default: 'full',
  },
  accessBenefits: [{
    type: String,
  }],
  totalSupply: {
    type: Number,
    required: true,
  },
  remainingSupply: {
    type: Number,
  },
  price: {
    amount: {
      type: Number,
    },
    currency: {
      type: String,
      enum: ['ETH', 'MATIC', 'SOL', 'BNB', 'USD'],
      default: 'MATIC',
    },
  },
  saleStartDate: {
    type: Date,
  },
  saleEndDate: {
    type: Date,
  },
  isActive: {
    type: Boolean,
    default: true,
  },
  holders: [{
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
    },
    tokenId: {
      type: String,
    },
    acquiredAt: {
      type: Date,
      default: Date.now,
    },
    hasAccessed: {
      type: Boolean,
      default: false,
    },
    lastAccessedAt: {
      type: Date,
    },
  }],
  accessHistory: [{
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
    },
    tokenId: {
      type: String,
    },
    accessedAt: {
      type: Date,
      default: Date.now,
    },
    ipAddress: {
      type: String,
    },
    deviceInfo: {
      type: String,
    },
  }],
}, { timestamps: true });

// Create indexes for efficient queries
NFTAccessSchema.index({ stream: 1 });
NFTAccessSchema.index({ creator: 1 });
NFTAccessSchema.index({ contractAddress: 1 });
NFTAccessSchema.index({ 'holders.user': 1 });

module.exports = mongoose.model('NFTAccess', NFTAccessSchema);
