const mongoose = require('mongoose');

const ProductSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Please add a name'],
    trim: true,
    maxlength: [100, 'Name cannot be more than 100 characters']
  },
  description: {
    type: String,
    required: [true, 'Please add a description'],
    maxlength: [1000, 'Description cannot be more than 1000 characters']
  },
  price: {
    type: Number,
    required: [true, 'Please add a price'],
    min: [0.01, 'Price must be at least 0.01']
  },
  originalPrice: {
    type: Number,
    min: [0.01, 'Original price must be at least 0.01']
  },
  discount: {
    type: Number,
    min: [0, 'Discount cannot be negative'],
    max: [100, 'Discount cannot exceed 100%'],
    default: 0
  },
  images: {
    type: [String],
    required: [true, 'Please add at least one image'],
    validate: {
      validator: function(v) {
        return v.length > 0;
      },
      message: 'Please add at least one image'
    }
  },
  category: {
    type: String,
    required: [true, 'Please add a category']
  },
  tags: {
    type: [String]
  },
  inventory: {
    type: Number,
    default: 0
  },
  isAvailable: {
    type: Boolean,
    default: true
  },
  user: {
    type: mongoose.Schema.ObjectId,
    ref: 'User',
    required: true
  },
  vendor: {
    type: mongoose.Schema.ObjectId,
    ref: 'Vendor'
  },
  views: {
    type: Number,
    default: 0
  },
  purchases: {
    type: Number,
    default: 0
  },
  averageRating: {
    type: Number,
    min: [0, 'Rating cannot be negative'],
    max: [5, 'Rating cannot exceed 5'],
    default: 0
  },
  reviewCount: {
    type: Number,
    default: 0
  },
  brand: {
    type: String,
    trim: true
  },
  model: {
    type: String,
    trim: true
  },
  weight: {
    type: Number,
    min: [0, 'Weight cannot be negative']
  },
  dimensions: {
    length: Number,
    width: Number,
    height: Number,
    unit: {
      type: String,
      enum: ['cm', 'in'],
      default: 'cm'
    }
  },
  features: [String],
  specifications: [{
    name: String,
    value: String
  }],
  warranty: {
    duration: Number,
    unit: {
      type: String,
      enum: ['days', 'months', 'years'],
      default: 'months'
    },
    description: String
  },
  shippingInfo: {
    weight: Number,
    freeShipping: {
      type: Boolean,
      default: false
    },
    shippingCost: {
      type: Number,
      default: 0
    },
    processingTime: {
      type: Number,
      default: 1
    }
  },
  seoTitle: String,
  seoDescription: String,
  seoKeywords: [String],
  isBoosted: {
    type: Boolean,
    default: false
  },
  boostScore: {
    type: Number,
    default: 0
  },
  boostExpiry: Date,
  createdAt: {
    type: Date,
    default: Date.now
  }
});

// Auto-populate vendor field when product is created
ProductSchema.pre('save', async function(next) {
  if (this.isNew && this.user && !this.vendor) {
    try {
      const Vendor = mongoose.model('Vendor');
      const vendor = await Vendor.findOne({ user: this.user });
      if (vendor) {
        this.vendor = vendor._id;
      }
    } catch (error) {
      console.error('Error setting vendor field:', error);
    }
  }
  next();
});

// Create index for faster queries
ProductSchema.index({ name: 'text', description: 'text', category: 'text', tags: 'text' });
ProductSchema.index({ user: 1, createdAt: -1 });
ProductSchema.index({ vendor: 1, createdAt: -1 });
ProductSchema.index({ category: 1 });
ProductSchema.index({ isAvailable: 1 });
ProductSchema.index({ isBoosted: 1, boostScore: -1 });
ProductSchema.index({ boostExpiry: 1 });

module.exports = mongoose.model('Product', ProductSchema);
