const mongoose = require('mongoose');
const Story = require('../models/Story');
const User = require('../models/User');
require('dotenv').config();

// Connect to MongoDB
mongoose.connect(process.env.MONGO_URI || 'mongodb://localhost:27017/letstalk')
  .then(() => console.log('MongoDB Connected'))
  .catch(err => console.error('MongoDB Connection Error:', err));

const createTestStory = async () => {
  try {
    // Find a user to create a story for
    const user = await User.findOne();
    
    if (!user) {
      console.error('No users found in the database');
      process.exit(1);
    }
    
    console.log(`Creating test story for user: ${user.username} (${user._id})`);
    
    // Create a test story
    const story = await Story.create({
      user: user._id,
      type: 'image',
      media: 'https://res.cloudinary.com/dtx9ed1hh/image/upload/v1620000000/letstalk/stories/images/test-story.jpg',
      caption: 'This is a test story',
      expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours from now
    });
    
    console.log('Test story created successfully:', story);
    
    // Disconnect from MongoDB
    await mongoose.disconnect();
    console.log('MongoDB Disconnected');
    
    process.exit(0);
  } catch (error) {
    console.error('Error creating test story:', error);
    process.exit(1);
  }
};

createTestStory();
