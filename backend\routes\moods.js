const express = require('express');
const router = express.Router();
const { protect } = require('../middleware/auth');
const {
  getMoods,
  getMood,
  createMood,
  updateMood,
  deleteMood,
  getUserMoods,
  getCurrentMood,
  setCurrentMood,
} = require('../controllers/moodController');

// All routes require authentication
router.use(protect);

// Protected routes
router.get('/', getMoods);
router.get('/current', getCurrentMood);
router.post('/current', setCurrentMood);
router.get('/user/:userId', getUserMoods);
router.get('/:id', getMood);
router.post('/', createMood);
router.put('/:id', updateMood);
router.delete('/:id', deleteMood);

module.exports = router;
