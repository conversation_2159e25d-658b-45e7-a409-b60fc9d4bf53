import React, { useState, useEffect } from 'react'
import {
  Box,
  Container,
  Typography,
  Tabs,
  Tab,
  Card,
  CardContent,
  Avatar,
  CircularProgress,
  IconButton,
  Chip,
  CardMedia,
  CardActions,
  Button,
  Divider,
  Grid,
  Paper,
  Badge,
  Fab
} from '@mui/material'
import {
  Favorite,
  FavoriteBorder,
  Comment,
  Share,
  Add,
  TrendingUp,
  People,
  Home as HomeIcon,
  Mood,
  PlayArrow,
  ShoppingCart,
  Store
} from '@mui/icons-material'
import { useSnackbar } from 'notistack'
import { useNavigate } from 'react-router-dom'
import { useAuth } from '../context/AuthContext'
import axios from '../utils/fixedAxios'

const HomePage = () => {
  const [posts, setPosts] = useState([])
  const [stories, setStories] = useState([])
  const [reels, setReels] = useState([])
  const [products, setProducts] = useState([])
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState(0)
  const [likedPosts, setLikedPosts] = useState(new Set())

  const { enqueueSnackbar } = useSnackbar()
  const { user } = useAuth()
  const navigate = useNavigate()

  useEffect(() => {
    fetchData()
  }, [activeTab])

  const fetchData = async () => {
    try {
      setLoading(true)

      // Fetch posts based on active tab
      let postsEndpoint = '/api/posts'
      if (activeTab === 1) {
        postsEndpoint = '/api/feed/following'
      } else if (activeTab === 2) {
        postsEndpoint = '/api/feed/trending'
      }

      const [postsRes, storiesRes, reelsRes, productsRes] = await Promise.allSettled([
        axios.get(postsEndpoint),
        axios.get('/api/stories'),
        axios.get('/api/reels'),
        axios.get('/api/shop/products?featured=true&limit=6')
      ])

      if (postsRes.status === 'fulfilled') {
        setPosts(postsRes.value.data.data || postsRes.value.data.posts || [])
      }

      if (storiesRes.status === 'fulfilled') {
        setStories(storiesRes.value.data.data || storiesRes.value.data.stories || [])
      }

      if (reelsRes.status === 'fulfilled') {
        setReels(reelsRes.value.data.data || reelsRes.value.data.reels || [])
      }

      if (productsRes.status === 'fulfilled') {
        setProducts(productsRes.value.data.data || productsRes.value.data.products || [])
      }

    } catch (error) {
      console.error('Error fetching data:', error)
      enqueueSnackbar('Failed to load content', { variant: 'error' })
    } finally {
      setLoading(false)
    }
  }

  const handleLike = async (postId) => {
    try {
      const isLiked = likedPosts.has(postId)

      if (isLiked) {
        await axios.delete(`/api/likes/post/${postId}`)
        setLikedPosts(prev => {
          const newSet = new Set(prev)
          newSet.delete(postId)
          return newSet
        })
        enqueueSnackbar('Post unliked', { variant: 'info' })
      } else {
        await axios.post(`/api/likes/post/${postId}`)
        setLikedPosts(prev => new Set(prev).add(postId))
        enqueueSnackbar('Post liked!', { variant: 'success' })
      }

      // Update post likes count
      setPosts(prev => prev.map(post =>
        post._id === postId
          ? { ...post, likesCount: (post.likesCount || 0) + (isLiked ? -1 : 1) }
          : post
      ))

    } catch (error) {
      console.error('Error liking post:', error)
      enqueueSnackbar('Failed to like post', { variant: 'error' })
    }
  }

  const tabs = ['For You', 'Following', 'Trending']

  return (
    <Box sx={{ display: 'flex', gap: 3 }}>
      {/* Main Content */}
      <Box sx={{ flex: 1, maxWidth: '600px' }}>
        <Container maxWidth="md" sx={{ py: 3, px: 0 }}>
          {/* Header */}
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 3 }}>
            <Typography variant="h4" component="h1" fontWeight="bold">
              Home
            </Typography>
            <Fab
              color="primary"
              size="medium"
              onClick={() => navigate('/create')}
              sx={{ boxShadow: 3 }}
            >
              <Add />
            </Fab>
          </Box>

          {/* Stories Section */}
          {stories.length > 0 && (
            <Paper sx={{ p: 2, mb: 3, borderRadius: 2 }}>
              <Typography variant="h6" gutterBottom>
                Stories
              </Typography>
              <Box sx={{ display: 'flex', gap: 2, overflowX: 'auto', pb: 1 }}>
                {/* Add Story Button */}
                <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', minWidth: 80 }}>
                  <Avatar
                    sx={{
                      width: 60,
                      height: 60,
                      bgcolor: 'primary.main',
                      cursor: 'pointer',
                      border: '2px dashed',
                      borderColor: 'primary.main'
                    }}
                    onClick={() => navigate('/create?type=story')}
                  >
                    <Add />
                  </Avatar>
                  <Typography variant="caption" sx={{ mt: 1, textAlign: 'center' }}>
                    Your Story
                  </Typography>
                </Box>

                {/* Stories */}
                {stories.slice(0, 8).map((story) => (
                  <Box key={story._id} sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', minWidth: 80 }}>
                    <Avatar
                      src={story.media?.[0]?.url}
                      sx={{
                        width: 60,
                        height: 60,
                        cursor: 'pointer',
                        border: '3px solid',
                        borderColor: 'primary.main'
                      }}
                    >
                      {story.user?.name?.charAt(0)?.toUpperCase()}
                    </Avatar>
                    <Typography variant="caption" sx={{ mt: 1, textAlign: 'center' }}>
                      {story.user?.name?.split(' ')[0]}
                    </Typography>
                  </Box>
                ))}
              </Box>
            </Paper>
          )}

          {/* Tabs */}
          <Tabs
            value={activeTab}
            onChange={(e, newValue) => setActiveTab(newValue)}
            sx={{ mb: 3 }}
            variant="fullWidth"
          >
            {tabs.map((tab, index) => (
              <Tab
                key={index}
                label={tab}
                icon={index === 0 ? <HomeIcon /> : index === 1 ? <People /> : <TrendingUp />}
                iconPosition="start"
              />
            ))}
          </Tabs>

          {loading ? (
            <Box display="flex" justifyContent="center" py={6}>
              <CircularProgress />
            </Box>
          ) : (
            <Box>
              {posts.length === 0 ? (
                <Box textAlign="center" py={6}>
                  <Typography variant="h6" gutterBottom>
                    No posts yet
                  </Typography>
                  <Typography variant="body1" color="text.secondary" gutterBottom>
                    Start following people or create your first post!
                  </Typography>
                  <Button
                    variant="contained"
                    startIcon={<Add />}
                    onClick={() => navigate('/create')}
                    sx={{ mt: 2 }}
                  >
                    Create Post
                  </Button>
                </Box>
              ) : (
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
                  {posts.map((post) => (
                    <Card key={post._id} sx={{ borderRadius: 2, boxShadow: 2 }}>
                      {/* Post Header */}
                      <CardContent sx={{ pb: 1 }}>
                        <Box display="flex" alignItems="center" justifyContent="space-between">
                          <Box display="flex" alignItems="center">
                            <Avatar
                              src={post.user?.avatar}
                              sx={{ bgcolor: 'primary.main', mr: 2 }}
                            >
                              {post.user?.name?.charAt(0)?.toUpperCase()}
                            </Avatar>
                            <Box>
                              <Typography variant="subtitle1" fontWeight="medium">
                                {post.user?.name}
                              </Typography>
                              <Typography variant="caption" color="text.secondary">
                                @{post.user?.username} • {new Date(post.createdAt).toLocaleDateString()}
                              </Typography>
                            </Box>
                          </Box>
                          {post.mood && (
                            <Chip
                              icon={<Mood />}
                              label={post.mood.name}
                              size="small"
                              color="primary"
                              variant="outlined"
                            />
                          )}
                        </Box>
                      </CardContent>

                      {/* Post Content */}
                      {post.caption && (
                        <CardContent sx={{ pt: 0, pb: 1 }}>
                          <Typography variant="body1">
                            {post.caption}
                          </Typography>
                        </CardContent>
                      )}

                      {/* Post Media */}
                      {post.media && post.media.length > 0 && (
                        <CardMedia
                          component="img"
                          image={post.media[0].url}
                          alt="Post media"
                          sx={{ maxHeight: 400, objectFit: 'cover' }}
                        />
                      )}

                      {/* Post Actions */}
                      <CardActions sx={{ justifyContent: 'space-between', px: 2 }}>
                        <Box>
                          <IconButton
                            onClick={() => handleLike(post._id)}
                            color={likedPosts.has(post._id) ? 'error' : 'default'}
                          >
                            {likedPosts.has(post._id) ? <Favorite /> : <FavoriteBorder />}
                          </IconButton>
                          <Typography variant="caption" sx={{ mr: 2 }}>
                            {post.likesCount || 0}
                          </Typography>

                          <IconButton>
                            <Comment />
                          </IconButton>
                          <Typography variant="caption" sx={{ mr: 2 }}>
                            {post.commentsCount || 0}
                          </Typography>

                          <IconButton>
                            <Share />
                          </IconButton>
                        </Box>
                      </CardActions>
                    </Card>
                  ))}
                </Box>
              )}
            </Box>
          )}
        </Container>
      </Box>

      {/* Right Sidebar - Shopping & Marketplace */}
      <Box sx={{ width: '350px', display: { xs: 'none', lg: 'block' } }}>
        <Paper sx={{ p: 3, borderRadius: 2, position: 'sticky', top: 20 }}>
          <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Store color="primary" />
            Marketplace
          </Typography>

          {products.length > 0 ? (
            <Grid container spacing={2}>
              {products.slice(0, 6).map((product) => (
                <Grid item xs={6} key={product._id}>
                  <Card sx={{ cursor: 'pointer', '&:hover': { transform: 'scale(1.02)' } }}>
                    <CardMedia
                      component="img"
                      height="100"
                      image={product.images?.[0]?.url || '/default-product.jpg'}
                      alt={product.name}
                    />
                    <CardContent sx={{ p: 1 }}>
                      <Typography variant="caption" noWrap>
                        {product.name}
                      </Typography>
                      <Typography variant="subtitle2" color="primary">
                        ${product.price}
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
              ))}
            </Grid>
          ) : (
            <Typography variant="body2" color="text.secondary">
              No products available
            </Typography>
          )}

          <Button
            fullWidth
            variant="outlined"
            startIcon={<ShoppingCart />}
            sx={{ mt: 2 }}
            onClick={() => navigate('/shop')}
          >
            View All Products
          </Button>
        </Paper>
      </Box>
    </Box>
  )
}

export default HomePage
