










































import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import fs from 'fs'
import path from 'path'

// Function to check if node_modules/.vite exists and is older than package.json
const shouldForceOptimizeDeps = () => {
  const viteDir = path.resolve('node_modules/.vite')
  const packageJsonPath = path.resolve('package.json')

  // If .vite directory doesn't exist, force optimization
  if (!fs.existsSync(viteDir)) {
    console.log('No .vite directory found, forcing dependency optimization')
    return true
  }

  // If package.json is newer than .vite directory, force optimization
  const viteStats = fs.statSync(viteDir)
  const packageStats = fs.statSync(packageJsonPath)

  if (packageStats.mtime > viteStats.mtime) {
    console.log('package.json is newer than .vite cache, forcing dependency optimization')
    return true
  }

  return false
}

// https://vite.dev/config/
export default defineConfig({
  plugins: [react()],
  optimizeDeps: {
    force: shouldForceOptimizeDeps(), // Dynamically determine if we need to force optimization
    include: [
      'react',
      'react-dom',
      'react-router-dom',
      '@mui/material',
      '@emotion/react',
      '@emotion/styled',
      'axios',
      'socket.io-client',
      'recharts'
    ]
    // esbuildOptions timeout removed as it's no longer supported in esbuild 0.25+
  },
  // Build configuration
  build: {
    // Increase chunk size warning limit
    chunkSizeWarningLimit: 1000,
    rollupOptions: {
      output: {
        // Reduce chunk size by splitting large dependencies
        manualChunks: {
          vendor: ['react', 'react-dom', 'react-router-dom'],
          mui: ['@mui/material', '@mui/icons-material', '@emotion/react', '@emotion/styled'],
          charts: ['recharts'],
          utils: ['axios', 'socket.io-client']
        }
      }
    }
  },

  // Server configuration
  server: {
    port: 50001, // Changed to avoid port conflicts
    strictPort: false, // Allow fallback to other ports
    host: true,
    proxy: {
      '/api': {
        target: 'http://localhost:10002', // Updated to match backend port
        changeOrigin: true,
        secure: false,
        timeout: 60000,
      },
      '/socket.io': {
        target: 'http://localhost:10002', // Updated to match backend port
        changeOrigin: true,
        ws: true,
        secure: false,
        timeout: 60000,
        rewrite: (path) => path.replace(/^\/socket\.io/, '/socket.io'),
        configure: (proxy, _options) => {
          proxy.on('error', (err, _req, _res) => {
            console.log('proxy error', err);
          });
          proxy.on('proxyReq', (_proxyReq, req, _res) => {
            console.log('Sending Request to the Target:', req.method, req.url);
          });
          proxy.on('proxyRes', (proxyRes, req, _res) => {
            console.log('Received Response from the Target:', proxyRes.statusCode, req.url);
          });
          proxy.on('proxyReqWs', (_proxyReq, req, _socket, _options, _head) => {
            console.log('WebSocket connection:', req.url);
          });
          proxy.on('open', (_proxySocket) => {
            console.log('WebSocket connection opened');
          });
          proxy.on('close', (_proxyRes, _proxySocket, _proxyHead) => {
            console.log('WebSocket connection closed');
          });
        },
      },
    },
    hmr: {
      clientPort: 50001, // Updated to match server port
      timeout: 60000,
    },
  },
})
