const LiveStream = require('../models/LiveStream');
const LiveStreamReaction = require('../models/LiveStreamReaction');
const LiveStreamViewer = require('../models/LiveStreamViewer');
const User = require('../models/User');
const asyncHandler = require('../middleware/async');
const ErrorResponse = require('../utils/errorResponse');
const socketEmitter = require('../utils/socketEmitter');

/**
 * Get reactions for a live stream
 * @route GET /api/live-streams/:streamId/reactions
 * @access Public
 */
exports.getReactions = asyncHandler(async (req, res, next) => {
  const { page = 1, limit = 20, type } = req.query;
  const skip = (page - 1) * limit;

  // Check if stream exists
  const stream = await LiveStream.findById(req.params.streamId);

  if (!stream) {
    return next(new ErrorResponse(`Live stream not found with id of ${req.params.streamId}`, 404));
  }

  // Check if stream is private and user is allowed to view
  if (stream.isPrivate) {
    if (!req.user) {
      return next(new ErrorResponse('Not authorized to view this stream', 401));
    }

    const isAllowed = stream.user.toString() === req.user._id.toString() || 
                      stream.allowedViewers.includes(req.user._id);

    if (!isAllowed) {
      return next(new ErrorResponse('Not authorized to view this stream', 403));
    }
  }

  // Build query
  const query = {
    stream: req.params.streamId,
  };

  // Add type filter if provided
  if (type) {
    query.type = type;
  }

  // Get reactions
  const reactions = await LiveStreamReaction.find(query)
    .sort({ createdAt: -1 })
    .skip(skip)
    .limit(parseInt(limit))
    .populate('user', 'username name profilePicture isVerified')
    .populate('emotions.emotion', 'name color icon category');

  // Get total count
  const total = await LiveStreamReaction.countDocuments(query);

  res.status(200).json({
    success: true,
    count: reactions.length,
    total,
    pagination: {
      page: parseInt(page),
      limit: parseInt(limit),
      totalPages: Math.ceil(total / limit),
    },
    data: reactions,
  });
});

/**
 * Add a reaction to a live stream
 * @route POST /api/live-streams/:streamId/reactions
 * @access Private
 */
exports.addReaction = asyncHandler(async (req, res, next) => {
  const stream = await LiveStream.findById(req.params.streamId);

  if (!stream) {
    return next(new ErrorResponse(`Live stream not found with id of ${req.params.streamId}`, 404));
  }

  // Check if stream is live
  if (stream.status !== 'live') {
    return next(new ErrorResponse('Cannot react to a stream that is not live', 400));
  }

  // Check if reactions are enabled
  if (!stream.settings.reactions.enabled) {
    return next(new ErrorResponse('Reactions are disabled for this stream', 400));
  }

  // Check if stream is private and user is allowed to view
  if (stream.isPrivate) {
    const isAllowed = stream.user.toString() === req.user.id || 
                      stream.allowedViewers.includes(req.user._id);

    if (!isAllowed) {
      return next(new ErrorResponse('Not authorized to view this stream', 403));
    }
  }

  // Create reaction
  const reaction = await LiveStreamReaction.create({
    stream: req.params.streamId,
    user: req.user.id,
    type: req.body.type,
    emoji: req.body.emoji,
    giftType: req.body.giftType,
    giftValue: req.body.giftValue,
    giftAnimation: req.body.giftAnimation,
    message: req.body.message,
    isHighlighted: req.body.isHighlighted || false,
    emotions: req.body.emotions,
  });

  // Populate user and emotions
  await reaction.populate('user', 'username name profilePicture isVerified');
  await reaction.populate('emotions.emotion', 'name color icon category');

  // Update viewer interaction count
  await LiveStreamViewer.findOneAndUpdate(
    {
      stream: req.params.streamId,
      user: req.user.id,
      isActive: true,
    },
    {
      $inc: { 'interactions.reactions': 1 },
    }
  );

  // Update stream likes count if reaction is a like
  if (req.body.type === 'like') {
    await LiveStream.findByIdAndUpdate(req.params.streamId, {
      $inc: { likesCount: 1 },
    });
  }

  // Emit socket event for new reaction
  socketEmitter.emitLiveStreamReaction(reaction);

  res.status(201).json({
    success: true,
    data: reaction,
  });
});

/**
 * Delete a reaction
 * @route DELETE /api/live-streams/:streamId/reactions/:id
 * @access Private
 */
exports.deleteReaction = asyncHandler(async (req, res, next) => {
  const reaction = await LiveStreamReaction.findById(req.params.id);

  if (!reaction) {
    return next(new ErrorResponse(`Reaction not found with id of ${req.params.id}`, 404));
  }

  // Check if user is the reaction owner or stream owner
  const stream = await LiveStream.findById(req.params.streamId);

  if (!stream) {
    return next(new ErrorResponse(`Live stream not found with id of ${req.params.streamId}`, 404));
  }

  if (reaction.user.toString() !== req.user.id && stream.user.toString() !== req.user.id) {
    return next(new ErrorResponse('Not authorized to delete this reaction', 403));
  }

  // Delete the reaction
  await reaction.remove();

  // Update stream likes count if reaction was a like
  if (reaction.type === 'like') {
    await LiveStream.findByIdAndUpdate(req.params.streamId, {
      $inc: { likesCount: -1 },
    });
  }

  // Emit socket event for deleted reaction
  socketEmitter.emitLiveStreamReactionDeleted(reaction._id, req.params.streamId);

  res.status(200).json({
    success: true,
    data: {},
  });
});

/**
 * Get reaction statistics for a stream
 * @route GET /api/live-streams/:streamId/reactions/stats
 * @access Private
 */
exports.getReactionStats = asyncHandler(async (req, res, next) => {
  const stream = await LiveStream.findById(req.params.streamId);

  if (!stream) {
    return next(new ErrorResponse(`Live stream not found with id of ${req.params.streamId}`, 404));
  }

  // Check if user is the stream owner
  if (stream.user.toString() !== req.user.id) {
    return next(new ErrorResponse('Not authorized to view these statistics', 403));
  }

  // Get reaction counts by type
  const reactionStats = await LiveStreamReaction.aggregate([
    {
      $match: {
        stream: mongoose.Types.ObjectId(req.params.streamId),
      },
    },
    {
      $group: {
        _id: '$type',
        count: { $sum: 1 },
      },
    },
  ]);

  // Format stats
  const stats = {
    total: reactionStats.reduce((acc, curr) => acc + curr.count, 0),
    byType: reactionStats.reduce((acc, curr) => {
      acc[curr._id] = curr.count;
      return acc;
    }, {}),
  };

  res.status(200).json({
    success: true,
    data: stats,
  });
});
