const LiveStream = require('../models/LiveStream');
const LiveStreamChat = require('../models/LiveStreamChat');
const LiveStreamViewer = require('../models/LiveStreamViewer');
const User = require('../models/User');
const Follow = require('../models/Follow');
const asyncHandler = require('../middleware/async');
const ErrorResponse = require('../utils/errorResponse');
const socketEmitter = require('../utils/socketEmitter');

/**
 * Get chat messages for a live stream
 * @route GET /api/live-streams/:streamId/chat
 * @access Public
 */
exports.getChatMessages = asyncHandler(async (req, res, next) => {
  const { page = 1, limit = 50 } = req.query;
  const skip = (page - 1) * limit;

  // Check if stream exists
  const stream = await LiveStream.findById(req.params.streamId);

  if (!stream) {
    return next(new ErrorResponse(`Live stream not found with id of ${req.params.streamId}`, 404));
  }

  // Check if stream is private and user is allowed to view
  if (stream.isPrivate) {
    if (!req.user) {
      return next(new ErrorResponse('Not authorized to view this stream', 401));
    }

    const isAllowed = stream.user.toString() === req.user._id.toString() || 
                      stream.allowedViewers.includes(req.user._id);

    if (!isAllowed) {
      return next(new ErrorResponse('Not authorized to view this stream', 403));
    }
  }

  // Get chat messages
  const messages = await LiveStreamChat.find({
    stream: req.params.streamId,
    isDeleted: false,
  })
    .sort({ createdAt: -1 })
    .skip(skip)
    .limit(parseInt(limit))
    .populate('user', 'username name profilePicture isVerified')
    .populate('emotions.emotion', 'name color icon category')
    .populate({
      path: 'replyTo',
      select: 'message user',
      populate: {
        path: 'user',
        select: 'username name profilePicture',
      },
    });

  // Get total count
  const total = await LiveStreamChat.countDocuments({
    stream: req.params.streamId,
    isDeleted: false,
  });

  res.status(200).json({
    success: true,
    count: messages.length,
    total,
    pagination: {
      page: parseInt(page),
      limit: parseInt(limit),
      totalPages: Math.ceil(total / limit),
    },
    data: messages.reverse(), // Return in chronological order
  });
});

/**
 * Add a chat message to a live stream
 * @route POST /api/live-streams/:streamId/chat
 * @access Private
 */
exports.addChatMessage = asyncHandler(async (req, res, next) => {
  const stream = await LiveStream.findById(req.params.streamId);

  if (!stream) {
    return next(new ErrorResponse(`Live stream not found with id of ${req.params.streamId}`, 404));
  }

  // Check if stream is live
  if (stream.status !== 'live') {
    return next(new ErrorResponse('Cannot chat in a stream that is not live', 400));
  }

  // Check if chat is enabled
  if (!stream.settings.chat.enabled) {
    return next(new ErrorResponse('Chat is disabled for this stream', 400));
  }

  // Check if user is allowed to chat (followers only mode)
  if (stream.settings.chat.followersOnly && stream.user.toString() !== req.user.id) {
    const isFollowing = await Follow.findOne({
      follower: req.user.id,
      following: stream.user,
    });

    if (!isFollowing) {
      return next(new ErrorResponse('Only followers can chat in this stream', 403));
    }
  }

  // Check for slow mode
  if (stream.settings.chat.slowMode && stream.user.toString() !== req.user.id) {
    const lastMessage = await LiveStreamChat.findOne({
      stream: req.params.streamId,
      user: req.user.id,
    }).sort({ createdAt: -1 });

    if (lastMessage) {
      const timeSinceLastMessage = Date.now() - lastMessage.createdAt;
      const slowModeInterval = stream.settings.chat.slowModeInterval * 1000; // Convert to milliseconds

      if (timeSinceLastMessage < slowModeInterval) {
        return next(
          new ErrorResponse(
            `Slow mode is enabled. Please wait ${Math.ceil(
              (slowModeInterval - timeSinceLastMessage) / 1000
            )} seconds before sending another message`,
            429
          )
        );
      }
    }
  }

  // Create chat message
  const chatMessage = await LiveStreamChat.create({
    stream: req.params.streamId,
    user: req.user.id,
    message: req.body.message,
    replyTo: req.body.replyTo,
    emotions: req.body.emotions,
  });

  // Populate user and emotions
  await chatMessage.populate('user', 'username name profilePicture isVerified');
  await chatMessage.populate('emotions.emotion', 'name color icon category');

  if (chatMessage.replyTo) {
    await chatMessage.populate({
      path: 'replyTo',
      select: 'message user',
      populate: {
        path: 'user',
        select: 'username name profilePicture',
      },
    });
  }

  // Update viewer interaction count
  await LiveStreamViewer.findOneAndUpdate(
    {
      stream: req.params.streamId,
      user: req.user.id,
      isActive: true,
    },
    {
      $inc: { 'interactions.chatMessages': 1 },
    }
  );

  // Emit socket event for new chat message
  socketEmitter.emitLiveStreamChatMessage(chatMessage);

  res.status(201).json({
    success: true,
    data: chatMessage,
  });
});

/**
 * Delete a chat message
 * @route DELETE /api/live-streams/:streamId/chat/:id
 * @access Private
 */
exports.deleteChatMessage = asyncHandler(async (req, res, next) => {
  const message = await LiveStreamChat.findById(req.params.id);

  if (!message) {
    return next(new ErrorResponse(`Chat message not found with id of ${req.params.id}`, 404));
  }

  // Check if user is the message owner or stream owner
  const stream = await LiveStream.findById(req.params.streamId);

  if (!stream) {
    return next(new ErrorResponse(`Live stream not found with id of ${req.params.streamId}`, 404));
  }

  if (message.user.toString() !== req.user.id && stream.user.toString() !== req.user.id) {
    return next(new ErrorResponse('Not authorized to delete this message', 403));
  }

  // Soft delete the message
  message.isDeleted = true;
  message.deletedBy = req.user.id;
  message.deletedAt = Date.now();
  await message.save();

  // Emit socket event for deleted chat message
  socketEmitter.emitLiveStreamChatMessageDeleted(message._id, req.params.streamId);

  res.status(200).json({
    success: true,
    data: {},
  });
});

/**
 * Pin a chat message
 * @route PUT /api/live-streams/:streamId/chat/:id/pin
 * @access Private
 */
exports.pinChatMessage = asyncHandler(async (req, res, next) => {
  const message = await LiveStreamChat.findById(req.params.id);

  if (!message) {
    return next(new ErrorResponse(`Chat message not found with id of ${req.params.id}`, 404));
  }

  // Check if user is the stream owner
  const stream = await LiveStream.findById(req.params.streamId);

  if (!stream) {
    return next(new ErrorResponse(`Live stream not found with id of ${req.params.streamId}`, 404));
  }

  if (stream.user.toString() !== req.user.id) {
    return next(new ErrorResponse('Not authorized to pin messages in this stream', 403));
  }

  // Unpin all other messages
  await LiveStreamChat.updateMany(
    { stream: req.params.streamId, isPinned: true },
    { isPinned: false }
  );

  // Pin the message
  message.isPinned = true;
  await message.save();

  // Populate user and emotions
  await message.populate('user', 'username name profilePicture isVerified');
  await message.populate('emotions.emotion', 'name color icon category');

  // Emit socket event for pinned chat message
  socketEmitter.emitLiveStreamChatMessagePinned(message, req.params.streamId);

  res.status(200).json({
    success: true,
    data: message,
  });
});
