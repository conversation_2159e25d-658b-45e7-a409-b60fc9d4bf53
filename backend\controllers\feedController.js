const Post = require('../models/Post');
const Reel = require('../models/Reel');
const User = require('../models/User');
const Follow = require('../models/Follow');
const Emotion = require('../models/Emotion');
const Mood = require('../models/Mood');
const { createError } = require('../utils/error');

/**
 * Get general feed
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.getFeed = async (req, res, next) => {
  try {
    const { page = 1, limit = 10 } = req.query;
    const skip = (page - 1) * limit;

    // Get posts and reels
    const [posts, reels] = await Promise.all([
      Post.find()
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit)
        .populate('user', 'username name profilePicture isVerified')
        .populate('emotions.emotion', 'name color icon category'),

      Reel.find()
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit)
        .populate('user', 'username name profilePicture isVerified')
        .populate('emotions.emotion', 'name color icon category'),
    ]);

    // Combine and format posts and reels
    const formattedPosts = posts.map(post => ({
      ...post.toObject(),
      type: 'post',
    }));

    const formattedReels = reels.map(reel => ({
      ...reel.toObject(),
      type: 'reel',
    }));

    // Combine and sort by creation date
    const combinedFeed = [...formattedPosts, ...formattedReels]
      .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
      .slice(0, limit);

    res.status(200).json({
      success: true,
      count: combinedFeed.length,
      data: combinedFeed,
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Get "For You" feed - content from users the current user doesn't follow and popular content
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.getForYouFeed = async (req, res, next) => {
  try {
    const { page = 1, limit = 10 } = req.query;
    const skip = (page - 1) * limit;

    // If user is not authenticated, return general feed
    if (!req.user) {
      return exports.getFeed(req, res, next);
    }

    // Get users that the current user follows
    const following = await Follow.find({ follower: req.user.id });
    const followingIds = following.map(follow => follow.following);

    // Get user's wellness settings for content filtering
    const user = await User.findById(req.user.id)
      .populate('wellnessSettings.contentFilters.preferredEmotions')
      .populate('wellnessSettings.contentFilters.avoidedEmotions');

    // Prepare content filter conditions
    let emotionFilter = {};

    if (user.wellnessSettings && user.wellnessSettings.contentFilters) {
      if (user.wellnessSettings.contentFilters.hideNegativeContent) {
        // Get all negative emotions
        const negativeEmotions = await Emotion.find({ category: 'negative' });
        const negativeEmotionIds = negativeEmotions.map(emotion => emotion._id);

        // Filter out content with negative emotions
        emotionFilter = { 'emotions.emotion': { $nin: negativeEmotionIds } };
      } else if (user.wellnessSettings.contentFilters.avoidedEmotions &&
                user.wellnessSettings.contentFilters.avoidedEmotions.length > 0) {
        // Filter out content with avoided emotions
        const avoidedEmotionIds = user.wellnessSettings.contentFilters.avoidedEmotions.map(emotion => emotion._id);
        emotionFilter = { 'emotions.emotion': { $nin: avoidedEmotionIds } };
      }
    }

    // Get posts from users the current user doesn't follow
    const [unfollowedPosts, unfollowedReels, popularPosts, popularReels] = await Promise.all([
      // Posts from users the current user doesn't follow
      Post.find({
        user: { $nin: followingIds },
        ...emotionFilter
      })
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(Math.floor(limit * 0.7)) // 70% of the limit for unfollowed content
        .populate('user', 'username name profilePicture isVerified')
        .populate('emotions.emotion', 'name color icon category'),

      // Reels from users the current user doesn't follow
      Reel.find({
        user: { $nin: followingIds },
        ...emotionFilter
      })
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(Math.floor(limit * 0.7)) // 70% of the limit for unfollowed content
        .populate('user', 'username name profilePicture isVerified')
        .populate('emotions.emotion', 'name color icon category'),

      // Popular posts regardless of who created them
      Post.find({
        ...emotionFilter
      })
        .sort({ likesCount: -1, commentsCount: -1 })
        .skip(skip)
        .limit(Math.ceil(limit * 0.3)) // 30% of the limit for popular content
        .populate('user', 'username name profilePicture isVerified')
        .populate('emotions.emotion', 'name color icon category'),

      // Popular reels regardless of who created them
      Reel.find({
        ...emotionFilter
      })
        .sort({ likesCount: -1, commentsCount: -1 })
        .skip(skip)
        .limit(Math.ceil(limit * 0.3)) // 30% of the limit for popular content
        .populate('user', 'username name profilePicture isVerified')
        .populate('emotions.emotion', 'name color icon category'),
    ]);

    // Format posts and reels
    const formattedUnfollowedPosts = unfollowedPosts.map(post => ({
      ...post.toObject(),
      type: 'post',
      source: 'unfollowed'
    }));

    const formattedUnfollowedReels = unfollowedReels.map(reel => ({
      ...reel.toObject(),
      type: 'reel',
      source: 'unfollowed'
    }));

    const formattedPopularPosts = popularPosts.map(post => ({
      ...post.toObject(),
      type: 'post',
      source: 'popular'
    }));

    const formattedPopularReels = popularReels.map(reel => ({
      ...reel.toObject(),
      type: 'reel',
      source: 'popular'
    }));

    // Combine all content
    let allContent = [
      ...formattedUnfollowedPosts,
      ...formattedUnfollowedReels,
      ...formattedPopularPosts,
      ...formattedPopularReels
    ];

    // Remove duplicates (same post might appear in both unfollowed and popular)
    const uniqueIds = new Set();
    const uniqueContent = [];

    for (const item of allContent) {
      const itemId = item._id.toString();
      if (!uniqueIds.has(itemId)) {
        uniqueIds.add(itemId);
        uniqueContent.push(item);
      }
    }

    // Sort by creation date and limit
    const combinedFeed = uniqueContent
      .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
      .slice(0, limit);

    // If user has preferred emotions, boost content with those emotions
    if (user.wellnessSettings &&
        user.wellnessSettings.contentFilters &&
        user.wellnessSettings.contentFilters.preferredEmotions &&
        user.wellnessSettings.contentFilters.preferredEmotions.length > 0) {

      const preferredEmotionIds = user.wellnessSettings.contentFilters.preferredEmotions.map(emotion =>
        emotion._id.toString()
      );

      // Calculate preference score for each item
      combinedFeed.forEach(item => {
        let preferenceScore = 0;

        if (item.emotions && item.emotions.length > 0) {
          item.emotions.forEach(emotion => {
            const emotionId = emotion.emotion._id.toString();
            if (preferredEmotionIds.includes(emotionId)) {
              preferenceScore += 1;
            }
          });
        }

        item.preferenceScore = preferenceScore;
      });

      // Sort by preference score (higher first) and then by date
      combinedFeed.sort((a, b) => {
        if (b.preferenceScore !== a.preferenceScore) {
          return b.preferenceScore - a.preferenceScore;
        }
        return new Date(b.createdAt) - new Date(a.createdAt);
      });
    }

    res.status(200).json({
      success: true,
      count: combinedFeed.length,
      data: combinedFeed,
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Get mood-based feed
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.getMoodBasedFeed = async (req, res, next) => {
  try {
    const { mood, intensity = 5, page = 1, limit = 10 } = req.query;
    const skip = (page - 1) * limit;

    if (!mood) {
      return next(createError(400, 'Mood ID is required'));
    }

    // Get the mood object
    const moodObj = await Mood.findById(mood);
    if (!moodObj) {
      return next(createError(404, 'Mood not found'));
    }

    // Get emotions related to this mood
    const relatedEmotions = await Emotion.find({ category: moodObj.category });
    const emotionIds = relatedEmotions.map(emotion => emotion._id);

    // Get posts and reels with matching emotions
    const [posts, reels] = await Promise.all([
      Post.find({ 'emotions.emotion': { $in: emotionIds } })
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit)
        .populate('user', 'username name profilePicture isVerified')
        .populate('emotions.emotion', 'name color icon category'),

      Reel.find({ 'emotions.emotion': { $in: emotionIds } })
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit)
        .populate('user', 'username name profilePicture isVerified')
        .populate('emotions.emotion', 'name color icon category'),
    ]);

    // Calculate emotional relevance score
    const calculateRelevance = (item) => {
      let score = 0;

      // Check if item has emotions
      if (item.emotions && item.emotions.length > 0) {
        // For each emotion in the item
        item.emotions.forEach(emotion => {
          // Check if emotion is in the same category as the mood
          const emotionObj = relatedEmotions.find(e =>
            e._id.toString() === emotion.emotion._id.toString()
          );

          if (emotionObj) {
            // Base score for matching category
            let emotionScore = 1;

            // Bonus for intensity match
            const intensityDiff = Math.abs(emotion.intensity - intensity);
            const intensityMatch = 1 - (intensityDiff / 10); // 0 to 1 score
            emotionScore += intensityMatch;

            // Add to total score
            score += emotionScore;
          }
        });
      }

      return score;
    };

    // Format and add relevance score
    const formattedPosts = posts.map(post => ({
      ...post.toObject(),
      type: 'post',
      relevanceScore: calculateRelevance(post),
    }));

    const formattedReels = reels.map(reel => ({
      ...reel.toObject(),
      type: 'reel',
      relevanceScore: calculateRelevance(reel),
    }));

    // Combine, sort by relevance score, and limit
    const combinedFeed = [...formattedPosts, ...formattedReels]
      .sort((a, b) => b.relevanceScore - a.relevanceScore)
      .slice(0, limit);

    res.status(200).json({
      success: true,
      count: combinedFeed.length,
      data: combinedFeed,
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Get trending feed with focus on popular reels
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.getTrendingFeed = async (req, res, next) => {
  try {
    const { page = 1, limit = 10, reelsOnly = false } = req.query;
    const skip = (page - 1) * limit;

    // Calculate the number of reels and posts to fetch
    // If reelsOnly is true, fetch only reels
    const reelsLimit = reelsOnly ? limit : Math.ceil(limit * 0.7); // 70% reels by default
    const postsLimit = reelsOnly ? 0 : Math.floor(limit * 0.3); // 30% posts by default

    // Get reels sorted by views, likes, and comments
    const reels = await Reel.find()
      .sort({ viewsCount: -1, likesCount: -1, commentsCount: -1, createdAt: -1 })
      .skip(skip)
      .limit(reelsLimit)
      .populate('user', 'username name profilePicture isVerified')
      .populate('emotions.emotion', 'name color icon category');

    // Format reels with engagement score that prioritizes views
    const formattedReels = reels.map(reel => {
      // Calculate engagement score with higher weight for views
      const viewsWeight = 1;
      const likesWeight = 0.5;
      const commentsWeight = 0.5;

      const engagementScore =
        (reel.viewsCount || 0) * viewsWeight +
        (reel.likesCount || 0) * likesWeight +
        (reel.commentsCount || 0) * commentsWeight;

      return {
        ...reel.toObject(),
        type: 'reel',
        engagementScore,
        // Add a flag to highlight top reels
        isTopReel: (reel.viewsCount || 0) > 100 || (reel.likesCount || 0) > 50
      };
    });

    let combinedFeed = [...formattedReels];

    // Only fetch posts if not in reelsOnly mode
    if (!reelsOnly && postsLimit > 0) {
      // Get posts sorted by engagement (likes + comments)
      const posts = await Post.find()
        .sort({ likesCount: -1, commentsCount: -1, createdAt: -1 })
        .skip(skip)
        .limit(postsLimit)
        .populate('user', 'username name profilePicture isVerified')
        .populate('emotions.emotion', 'name color icon category');

      // Format posts with engagement score
      const formattedPosts = posts.map(post => ({
        ...post.toObject(),
        type: 'post',
        engagementScore: (post.likesCount || 0) + (post.commentsCount || 0)
      }));

      // Add posts to the feed
      combinedFeed = [...formattedReels, ...formattedPosts];
    }

    // Sort by engagement score and limit
    combinedFeed.sort((a, b) => b.engagementScore - a.engagementScore)
      .slice(0, limit);

    // Add trending rank to each item
    combinedFeed = combinedFeed.map((item, index) => ({
      ...item,
      trendingRank: index + 1
    }));

    res.status(200).json({
      success: true,
      count: combinedFeed.length,
      data: combinedFeed,
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Get following feed
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.getFollowingFeed = async (req, res, next) => {
  try {
    const { page = 1, limit = 10 } = req.query;
    const skip = (page - 1) * limit;

    // Get users that the current user follows
    const following = await Follow.find({ follower: req.user.id });
    const followingIds = following.map(follow => follow.following);

    // If not following anyone, return empty feed
    if (followingIds.length === 0) {
      return res.status(200).json({
        success: true,
        count: 0,
        data: [],
      });
    }

    // Get user's wellness settings for content filtering
    const user = await User.findById(req.user.id)
      .populate('wellnessSettings.contentFilters.preferredEmotions')
      .populate('wellnessSettings.contentFilters.avoidedEmotions');

    // Prepare content filter conditions
    let emotionFilter = {};

    if (user.wellnessSettings.contentFilters.hideNegativeContent) {
      // Get all negative emotions
      const negativeEmotions = await Emotion.find({ category: 'negative' });
      const negativeEmotionIds = negativeEmotions.map(emotion => emotion._id);

      // Filter out content with negative emotions
      emotionFilter = { 'emotions.emotion': { $nin: negativeEmotionIds } };
    } else if (user.wellnessSettings.contentFilters.avoidedEmotions.length > 0) {
      // Filter out content with avoided emotions
      const avoidedEmotionIds = user.wellnessSettings.contentFilters.avoidedEmotions.map(emotion => emotion._id);
      emotionFilter = { 'emotions.emotion': { $nin: avoidedEmotionIds } };
    }

    // Get posts and reels from followed users with content filters
    const [posts, reels] = await Promise.all([
      Post.find({
        user: { $in: followingIds },
        ...emotionFilter
      })
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit)
        .populate('user', 'username name profilePicture isVerified')
        .populate('emotions.emotion', 'name color icon category'),

      Reel.find({
        user: { $in: followingIds },
        ...emotionFilter
      })
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit)
        .populate('user', 'username name profilePicture isVerified')
        .populate('emotions.emotion', 'name color icon category'),
    ]);

    // Format posts and reels
    const formattedPosts = posts.map(post => ({
      ...post.toObject(),
      type: 'post',
    }));

    const formattedReels = reels.map(reel => ({
      ...reel.toObject(),
      type: 'reel',
    }));

    // Combine, sort by creation date, and limit
    const combinedFeed = [...formattedPosts, ...formattedReels]
      .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
      .slice(0, limit);

    // If user has preferred emotions, boost content with those emotions
    if (user.wellnessSettings.contentFilters.preferredEmotions.length > 0) {
      const preferredEmotionIds = user.wellnessSettings.contentFilters.preferredEmotions.map(emotion =>
        emotion._id.toString()
      );

      // Calculate preference score for each item
      combinedFeed.forEach(item => {
        let preferenceScore = 0;

        if (item.emotions && item.emotions.length > 0) {
          item.emotions.forEach(emotion => {
            const emotionId = emotion.emotion._id.toString();
            if (preferredEmotionIds.includes(emotionId)) {
              preferenceScore += 1;
            }
          });
        }

        item.preferenceScore = preferenceScore;
      });

      // Sort by preference score (higher first) and then by date
      combinedFeed.sort((a, b) => {
        if (b.preferenceScore !== a.preferenceScore) {
          return b.preferenceScore - a.preferenceScore;
        }
        return new Date(b.createdAt) - new Date(a.createdAt);
      });
    }

    res.status(200).json({
      success: true,
      count: combinedFeed.length,
      data: combinedFeed,
    });
  } catch (err) {
    next(err);
  }
};
