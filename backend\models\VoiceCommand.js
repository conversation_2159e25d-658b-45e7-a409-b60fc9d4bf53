const mongoose = require('mongoose');

const VoiceCommandSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  command: {
    type: String,
    required: true,
    trim: true,
  },
  action: {
    type: String,
    required: true,
    enum: [
      'mute_chat', 
      'unmute_chat', 
      'highlight_clip', 
      'take_screenshot', 
      'start_recording', 
      'stop_recording',
      'toggle_camera',
      'toggle_microphone',
      'change_layout',
      'add_effect',
      'remove_effect',
      'send_reaction',
      'pin_message',
      'block_user',
      'promote_viewer',
      'change_mode',
      'custom'
    ],
  },
  customAction: {
    type: String,
    trim: true,
  },
  parameters: {
    type: Object,
    default: {},
  },
  isActive: {
    type: Boolean,
    default: true,
  },
  isCustom: {
    type: Boolean,
    default: false,
  },
  usageCount: {
    type: Number,
    default: 0,
  },
  lastUsed: {
    type: Date,
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
  updatedAt: {
    type: Date,
    default: Date.now,
  },
});

// Create indexes for efficient queries
VoiceCommandSchema.index({ user: 1, command: 1 }, { unique: true });
VoiceCommandSchema.index({ user: 1, action: 1 });

module.exports = mongoose.model('VoiceCommand', VoiceCommandSchema);
