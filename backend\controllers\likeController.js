const Like = require('../models/Like');
const Post = require('../models/Post');
const Reel = require('../models/Reel');
const Comment = require('../models/Comment');
const User = require('../models/User');
const Notification = require('../models/Notification');
const { createError } = require('../utils/error');
const socketEmitter = require('../utils/socketEmitter');

/**
 * Create a new like
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.createLike = async (req, res, next) => {
  try {
    const { post, reel, comment } = req.body;

    // Check if at least one item is provided
    if (!post && !reel && !comment) {
      return next(createError(400, 'Please provide a post, reel, or comment to like'));
    }

    // Check if like already exists
    const existingLike = await Like.findOne({
      user: req.user.id,
      ...(post && { post }),
      ...(reel && { reel }),
      ...(comment && { comment }),
    });

    if (existingLike) {
      return next(createError(400, 'You have already liked this item'));
    }

    // Create like
    const like = await Like.create({
      user: req.user.id,
      ...(post && { post }),
      ...(reel && { reel }),
      ...(comment && { comment }),
    });

    // Update like count
    let itemType, itemId, ownerId, mediaUrl, mediaType, mediaThumbnail;

    if (post) {
      const postDoc = await Post.findById(post);
      if (!postDoc) {
        return next(createError(404, 'Post not found'));
      }

      postDoc.likesCount = (postDoc.likesCount || 0) + 1;
      await postDoc.save();

      itemType = 'post';
      itemId = post;
      ownerId = postDoc.user.toString();

      // Get media info for notification
      if (postDoc.media && postDoc.media.length > 0) {
        mediaUrl = postDoc.media[0].url;
        mediaType = postDoc.media[0].type;
        if (mediaType === 'video' && postDoc.media[0].thumbnail) {
          mediaThumbnail = postDoc.media[0].thumbnail;
        }
      }
    } else if (reel) {
      const reelDoc = await Reel.findById(reel);
      if (!reelDoc) {
        return next(createError(404, 'Reel not found'));
      }

      reelDoc.likesCount = (reelDoc.likesCount || 0) + 1;
      await reelDoc.save();

      itemType = 'reel';
      itemId = reel;
      ownerId = reelDoc.user.toString();

      // Get media info for notification
      mediaUrl = reelDoc.video.url;
      mediaType = 'video';
      if (reelDoc.thumbnail) {
        mediaThumbnail = reelDoc.thumbnail.url;
      }
    } else if (comment) {
      const commentDoc = await Comment.findById(comment);
      if (!commentDoc) {
        return next(createError(404, 'Comment not found'));
      }

      commentDoc.likesCount = (commentDoc.likesCount || 0) + 1;
      await commentDoc.save();

      itemType = 'comment';
      itemId = comment;
      ownerId = commentDoc.user.toString();

      // Get parent post or reel for notification routing
      if (commentDoc.post) {
        const parentPost = await Post.findById(commentDoc.post);
        if (parentPost && parentPost.media && parentPost.media.length > 0) {
          mediaUrl = parentPost.media[0].url;
          mediaType = parentPost.media[0].type;
          if (mediaType === 'video' && parentPost.media[0].thumbnail) {
            mediaThumbnail = parentPost.media[0].thumbnail;
          }
        }
      } else if (commentDoc.reel) {
        const parentReel = await Reel.findById(commentDoc.reel);
        if (parentReel) {
          mediaUrl = parentReel.video.url;
          mediaType = 'video';
          if (parentReel.thumbnail) {
            mediaThumbnail = parentReel.thumbnail.url;
          }
        }
      }
    }

    // Create notification if the owner is not the same as the liker
    if (ownerId && ownerId !== req.user.id) {
      const notification = await Notification.create({
        recipient: ownerId,
        type: 'like',
        sender: req.user.id,
        ...(post && { post }),
        ...(reel && { reel }),
        ...(comment && { comment }),
        mediaUrl,
        mediaType,
        mediaThumbnail,
      });

      // Populate sender details for real-time notification
      await notification.populate('sender', 'username name profilePicture');

      // Emit notification via Socket.IO
      socketEmitter.emitNotification(ownerId, notification);
    }

    // Emit like event via Socket.IO
    socketEmitter.emitLike(req.user.id, itemType, itemId, ownerId);

    res.status(201).json({
      success: true,
      data: like,
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Delete a like
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.deleteLike = async (req, res, next) => {
  try {
    const { type, id } = req.params;

    // Validate type
    if (!['post', 'reel', 'comment'].includes(type)) {
      return next(createError(400, 'Invalid type. Must be post, reel, or comment'));
    }

    // Find like
    const like = await Like.findOne({
      user: req.user.id,
      [type]: id,
    });

    if (!like) {
      return next(createError(404, `You have not liked this ${type}`));
    }

    // Delete like
    await like.remove();

    // Update like count
    let itemType, itemId, ownerId;

    if (type === 'post') {
      const post = await Post.findById(id);
      if (post) {
        post.likesCount = Math.max(0, (post.likesCount || 0) - 1);
        await post.save();
        ownerId = post.user.toString();
      }
      itemType = 'post';
      itemId = id;
    } else if (type === 'reel') {
      const reel = await Reel.findById(id);
      if (reel) {
        reel.likesCount = Math.max(0, (reel.likesCount || 0) - 1);
        await reel.save();
        ownerId = reel.user.toString();
      }
      itemType = 'reel';
      itemId = id;
    } else if (type === 'comment') {
      const comment = await Comment.findById(id);
      if (comment) {
        comment.likesCount = Math.max(0, (comment.likesCount || 0) - 1);
        await comment.save();
        ownerId = comment.user.toString();
      }
      itemType = 'comment';
      itemId = id;
    }

    // Emit unlike event via Socket.IO
    if (ownerId) {
      socketEmitter.emit('unlike', {
        userId: req.user.id,
        itemType,
        itemId,
        ownerId,
      });
    }

    res.status(200).json({
      success: true,
      message: `${type.charAt(0).toUpperCase() + type.slice(1)} unliked successfully`,
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Check if user has liked an item
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.checkLike = async (req, res, next) => {
  try {
    const { type, id } = req.params;

    // Validate type
    if (!['post', 'reel', 'comment'].includes(type)) {
      return next(createError(400, 'Invalid type. Must be post, reel, or comment'));
    }

    // Find like
    const like = await Like.findOne({
      user: req.user.id,
      [type]: id,
    });

    res.status(200).json({
      success: true,
      isLiked: !!like,
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Get likes for an item
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.getLikes = async (req, res, next) => {
  try {
    const { type, id } = req.params;

    // Validate type
    if (!['post', 'reel', 'comment'].includes(type)) {
      return next(createError(400, 'Invalid type. Must be post, reel, or comment'));
    }

    // Find likes
    const likes = await Like.find({
      [type]: id,
    }).populate('user', 'username name profilePicture');

    res.status(200).json({
      success: true,
      count: likes.length,
      data: likes,
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Get likes by a user
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.getUserLikes = async (req, res, next) => {
  try {
    const { userId } = req.params;
    const { limit = 20, page = 1 } = req.query;

    // Calculate pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);

    // Find likes
    const likes = await Like.find({ user: userId })
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit))
      .populate('post', 'media caption')
      .populate('reel', 'video thumbnail caption')
      .populate('comment', 'text')
      .populate({
        path: 'post',
        populate: {
          path: 'user',
          select: 'username name profilePicture',
        },
      })
      .populate({
        path: 'reel',
        populate: {
          path: 'user',
          select: 'username name profilePicture',
        },
      })
      .populate({
        path: 'comment',
        populate: {
          path: 'user',
          select: 'username name profilePicture',
        },
      });

    // Get total count
    const total = await Like.countDocuments({ user: userId });

    res.status(200).json({
      success: true,
      count: likes.length,
      total,
      totalPages: Math.ceil(total / parseInt(limit)),
      currentPage: parseInt(page),
      data: likes,
    });
  } catch (err) {
    next(err);
  }
};
