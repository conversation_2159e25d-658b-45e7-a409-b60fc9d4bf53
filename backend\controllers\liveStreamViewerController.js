const LiveStream = require('../models/LiveStream');
const LiveStreamViewer = require('../models/LiveStreamViewer');
const User = require('../models/User');
const asyncHandler = require('../middleware/async');
const ErrorResponse = require('../utils/errorResponse');
const socketEmitter = require('../utils/socketEmitter');

/**
 * Get viewers for a live stream
 * @route GET /api/live-streams/:streamId/viewers
 * @access Public
 */
exports.getViewers = asyncHandler(async (req, res, next) => {
  const { page = 1, limit = 20, active = 'true' } = req.query;
  const skip = (page - 1) * limit;

  // Check if stream exists
  const stream = await LiveStream.findById(req.params.streamId);

  if (!stream) {
    return next(new ErrorResponse(`Live stream not found with id of ${req.params.streamId}`, 404));
  }

  // Check if stream is private and user is allowed to view
  if (stream.isPrivate) {
    if (!req.user) {
      return next(new ErrorResponse('Not authorized to view this stream', 401));
    }

    const isAllowed = stream.user.toString() === req.user._id.toString() || 
                      stream.allowedViewers.includes(req.user._id);

    if (!isAllowed) {
      return next(new ErrorResponse('Not authorized to view this stream', 403));
    }
  }

  // Build query
  const query = {
    stream: req.params.streamId,
  };

  // Add active filter if provided
  if (active === 'true') {
    query.isActive = true;
  } else if (active === 'false') {
    query.isActive = false;
  }

  // Get viewers
  const viewers = await LiveStreamViewer.find(query)
    .sort({ joinedAt: -1 })
    .skip(skip)
    .limit(parseInt(limit))
    .populate('user', 'username name profilePicture isVerified');

  // Get total count
  const total = await LiveStreamViewer.countDocuments(query);

  res.status(200).json({
    success: true,
    count: viewers.length,
    total,
    pagination: {
      page: parseInt(page),
      limit: parseInt(limit),
      totalPages: Math.ceil(total / limit),
    },
    data: viewers,
  });
});

/**
 * Join a live stream as a viewer
 * @route POST /api/live-streams/:streamId/viewers
 * @access Private
 */
exports.joinStream = asyncHandler(async (req, res, next) => {
  const stream = await LiveStream.findById(req.params.streamId);

  if (!stream) {
    return next(new ErrorResponse(`Live stream not found with id of ${req.params.streamId}`, 404));
  }

  // Check if stream is live
  if (stream.status !== 'live') {
    return next(new ErrorResponse('Cannot join a stream that is not live', 400));
  }

  // Check if stream is private and user is allowed to view
  if (stream.isPrivate) {
    const isAllowed = stream.user.toString() === req.user.id || 
                      stream.allowedViewers.includes(req.user._id);

    if (!isAllowed) {
      return next(new ErrorResponse('Not authorized to view this stream', 403));
    }
  }

  // Check if user is already an active viewer
  let viewer = await LiveStreamViewer.findOne({
    stream: req.params.streamId,
    user: req.user.id,
    isActive: true,
  });

  if (viewer) {
    return res.status(200).json({
      success: true,
      data: viewer,
    });
  }

  // Check if user was a previous viewer
  viewer = await LiveStreamViewer.findOne({
    stream: req.params.streamId,
    user: req.user.id,
  });

  if (viewer) {
    // Update existing viewer record
    viewer.isActive = true;
    viewer.joinedAt = Date.now();
    viewer.leftAt = null;
    viewer.duration = null;
    viewer.device = req.body.device || 'other';
    await viewer.save();
  } else {
    // Create new viewer record
    viewer = await LiveStreamViewer.create({
      stream: req.params.streamId,
      user: req.user.id,
      device: req.body.device || 'other',
    });
  }

  // Populate user
  await viewer.populate('user', 'username name profilePicture isVerified');

  // Update stream view count
  await LiveStream.findByIdAndUpdate(req.params.streamId, {
    $inc: { viewCount: 1, totalViews: 1 },
  });

  // Get current viewer count
  const activeViewers = await LiveStreamViewer.countDocuments({
    stream: req.params.streamId,
    isActive: true,
  });

  // Update peak viewers if needed
  if (activeViewers > stream.peakViewers) {
    await LiveStream.findByIdAndUpdate(req.params.streamId, {
      peakViewers: activeViewers,
    });
  }

  // Emit socket event for viewer joined
  socketEmitter.emitLiveStreamViewerJoined(viewer, req.params.streamId, activeViewers);

  res.status(200).json({
    success: true,
    data: viewer,
  });
});

/**
 * Leave a live stream as a viewer
 * @route DELETE /api/live-streams/:streamId/viewers
 * @access Private
 */
exports.leaveStream = asyncHandler(async (req, res, next) => {
  const stream = await LiveStream.findById(req.params.streamId);

  if (!stream) {
    return next(new ErrorResponse(`Live stream not found with id of ${req.params.streamId}`, 404));
  }

  // Find viewer record
  const viewer = await LiveStreamViewer.findOne({
    stream: req.params.streamId,
    user: req.user.id,
    isActive: true,
  });

  if (!viewer) {
    return next(new ErrorResponse('You are not currently viewing this stream', 404));
  }

  // Update viewer record
  viewer.isActive = false;
  viewer.leftAt = Date.now();
  viewer.duration = (Date.now() - viewer.joinedAt) / 1000; // Duration in seconds
  await viewer.save();

  // Update stream view count
  await LiveStream.findByIdAndUpdate(req.params.streamId, {
    $inc: { viewCount: -1 },
  });

  // Get current viewer count
  const activeViewers = await LiveStreamViewer.countDocuments({
    stream: req.params.streamId,
    isActive: true,
  });

  // Emit socket event for viewer left
  socketEmitter.emitLiveStreamViewerLeft(viewer._id, req.user.id, req.params.streamId, activeViewers);

  res.status(200).json({
    success: true,
    data: {},
  });
});

/**
 * Get viewer statistics for a stream
 * @route GET /api/live-streams/:streamId/viewers/stats
 * @access Private
 */
exports.getViewerStats = asyncHandler(async (req, res, next) => {
  const stream = await LiveStream.findById(req.params.streamId);

  if (!stream) {
    return next(new ErrorResponse(`Live stream not found with id of ${req.params.streamId}`, 404));
  }

  // Check if user is the stream owner
  if (stream.user.toString() !== req.user.id) {
    return next(new ErrorResponse('Not authorized to view these statistics', 403));
  }

  // Get active viewers count
  const activeViewers = await LiveStreamViewer.countDocuments({
    stream: req.params.streamId,
    isActive: true,
  });

  // Get total unique viewers
  const totalUniqueViewers = await LiveStreamViewer.countDocuments({
    stream: req.params.streamId,
  });

  // Get average watch duration
  const viewerStats = await LiveStreamViewer.aggregate([
    {
      $match: {
        stream: mongoose.Types.ObjectId(req.params.streamId),
        duration: { $exists: true, $ne: null },
      },
    },
    {
      $group: {
        _id: null,
        averageDuration: { $avg: '$duration' },
        maxDuration: { $max: '$duration' },
      },
    },
  ]);

  const stats = {
    activeViewers,
    totalUniqueViewers,
    peakViewers: stream.peakViewers,
    totalViews: stream.totalViews,
    averageWatchDuration: viewerStats.length > 0 ? Math.round(viewerStats[0].averageDuration) : 0,
    maxWatchDuration: viewerStats.length > 0 ? Math.round(viewerStats[0].maxDuration) : 0,
  };

  res.status(200).json({
    success: true,
    data: stats,
  });
});
