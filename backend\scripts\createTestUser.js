const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const dotenv = require('dotenv');
const path = require('path');

// Load environment variables
dotenv.config({ path: path.resolve(__dirname, '../.env') });

// Connect to MongoDB
mongoose.connect(process.env.MONGO_URI, {
  useNewUrlParser: true,
  useUnifiedTopology: true,
});

const db = mongoose.connection;

db.on('error', console.error.bind(console, 'connection error:'));
db.once('open', async function() {
  console.log('Connected to MongoDB');

  try {
    // Check if User model exists
    const User = mongoose.model('User');
    console.log('User model already exists');
  } catch (error) {
    // If not, create the User model
    console.log('Creating User model');

    const UserSchema = new mongoose.Schema({
      username: {
        type: String,
        required: [true, 'Please provide a username'],
        unique: true,
        trim: true,
        minlength: [3, 'Username must be at least 3 characters'],
        maxlength: [20, 'Username cannot be more than 20 characters'],
      },
      email: {
        type: String,
        required: [true, 'Please provide an email'],
        unique: true,
        match: [
          /^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/,
          'Please provide a valid email',
        ],
      },
      password: {
        type: String,
        required: [true, 'Please provide a password'],
        minlength: [6, 'Password must be at least 6 characters'],
        select: false,
      },
      name: {
        type: String,
        required: [true, 'Please provide your name'],
      },
      bio: {
        type: String,
        maxlength: [150, 'Bio cannot be more than 150 characters'],
        default: '',
      },
      website: {
        type: String,
        default: '',
      },
      profilePicture: {
        type: String,
        default: '/default-profile_jktu3d.png',
      },
      currentMood: {
        mood: {
          type: mongoose.Schema.Types.ObjectId,
          ref: 'Mood',
        },
        intensity: {
          type: Number,
          min: 1,
          max: 10,
          default: 5,
        },
        updatedAt: {
          type: Date,
          default: Date.now,
        },
      },
      resetPasswordToken: String,
      resetPasswordExpire: Date,
    }, {
      timestamps: true,
      toJSON: { virtuals: true },
      toObject: { virtuals: true },
    });

    // Encrypt password using bcrypt
    UserSchema.pre('save', async function (next) {
      if (!this.isModified('password')) {
        next();
      }

      const salt = await bcrypt.genSalt(10);
      this.password = await bcrypt.hash(this.password, salt);
    });

    // Match user entered password to hashed password in database
    UserSchema.methods.matchPassword = async function (enteredPassword) {
      return await bcrypt.compare(enteredPassword, this.password);
    };

    mongoose.model('User', UserSchema);
  }

  // Get the User model
  const User = mongoose.model('User');

  // No test users are created - users should register through the application
  console.log('No test users are created. Please register a new account through the application.');

  try {
    // Close the connection
    mongoose.connection.close();
  } catch (error) {
    console.error('Error closing MongoDB connection:', error);
    mongoose.connection.close();
  }
});
