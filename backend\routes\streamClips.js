const express = require('express');
const router = express.Router({ mergeParams: true });
const { protect, optionalAuth } = require('../middleware/auth');
const {
  getStreamClips,
  getClip,
  createClip,
  updateClip,
  deleteClip,
  generateAIClips,
  remixClip,
} = require('../controllers/streamClipController');

// Public routes
router.get('/', optionalAuth, getStreamClips);
router.get('/:id', optionalAuth, getClip);

// Protected routes
router.use(protect);

router.post('/', createClip);
router.put('/:id', updateClip);
router.delete('/:id', deleteClip);
router.post('/ai-generate', generateAIClips);
router.post('/:id/remix', remixClip);

module.exports = router;
