const express = require('express');
const mongoose = require('mongoose');
const dotenv = require('dotenv');
const path = require('path');

// Load environment variables
console.log('Loading environment variables from:', path.resolve(process.cwd(), '.env'));
dotenv.config();

// Create a simple Express app
const app = express();

// Health check route
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'success',
    message: 'Test server is running',
    environment: process.env.NODE_ENV,
    timestamp: new Date().toISOString()
  });
});

// Connect to MongoDB
console.log('Attempting to connect to MongoDB at:', process.env.MONGO_URI || 'mongodb://localhost:27017/letstalk');

mongoose.connect(process.env.MONGO_URI || 'mongodb://localhost:27017/letstalk')
  .then(() => {
    console.log('✅ Connected to MongoDB');
    
    // Start server
    const PORT = 60001;
    const server = app.listen(PORT, () => {
      console.log(`✅ Test server running on port ${PORT}`);
      console.log('Server will automatically shut down after 5 seconds');
      
      // Shut down after 5 seconds
      setTimeout(() => {
        console.log('Shutting down test server...');
        server.close(() => {
          console.log('Server closed');
          mongoose.connection.close()
            .then(() => {
              console.log('MongoDB connection closed');
              console.log('Test completed successfully');
              process.exit(0);
            })
            .catch(err => {
              console.error('Error closing MongoDB connection:', err);
              process.exit(1);
            });
        });
      }, 5000);
    });
  })
  .catch(err => {
    console.error('❌ MongoDB connection error:', err.message);
    process.exit(1);
  });
