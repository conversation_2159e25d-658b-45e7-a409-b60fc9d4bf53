const mongoose = require('mongoose');

const FollowSchema = new mongoose.Schema({
  follower: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  following: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  status: {
    type: String,
    enum: ['pending', 'accepted'],
    default: 'accepted',
  },
}, {
  timestamps: true,
});

// Compound index to ensure a user can only follow another user once
FollowSchema.index({ follower: 1, following: 1 }, { unique: true });

// Prevent self-following
FollowSchema.pre('validate', function(next) {
  if (this.follower.toString() === this.following.toString()) {
    return next(new Error('Users cannot follow themselves'));
  }
  next();
});

module.exports = mongoose.model('Follow', FollowSchema);
