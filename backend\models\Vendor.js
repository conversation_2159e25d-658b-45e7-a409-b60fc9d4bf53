const mongoose = require('mongoose');

const VendorSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.ObjectId,
    ref: 'User',
    required: true,
    unique: true
  },
  businessName: {
    type: String,
    required: [true, 'Please add a business name'],
    trim: true,
    maxlength: [100, 'Business name cannot be more than 100 characters']
  },
  businessType: {
    type: String,
    enum: ['individual', 'company', 'partnership', 'corporation'],
    required: true
  },
  description: {
    type: String,
    maxlength: [1000, 'Description cannot be more than 1000 characters']
  },
  logo: {
    type: String,
    default: ''
  },
  banner: {
    type: String,
    default: ''
  },
  contactInfo: {
    email: {
      type: String,
      required: true
    },
    phone: {
      type: String,
      required: true
    },
    website: String,
    socialMedia: {
      facebook: String,
      instagram: String,
      twitter: String,
      linkedin: String
    }
  },
  address: {
    street: {
      type: String,
      required: true
    },
    city: {
      type: String,
      required: true
    },
    state: {
      type: String,
      required: true
    },
    postalCode: {
      type: String,
      required: true
    },
    country: {
      type: String,
      required: true
    }
  },
  kycStatus: {
    type: String,
    enum: ['pending', 'submitted', 'verified', 'rejected'],
    default: 'pending'
  },
  kycDocuments: {
    idDocument: {
      type: String,
      required: function() { return this.kycStatus !== 'pending'; }
    },
    addressProof: {
      type: String,
      required: function() { return this.kycStatus !== 'pending'; }
    },
    businessLicense: String,
    taxDocument: String
  },
  payoutMethod: {
    type: {
      type: String,
      enum: ['bank_account', 'paypal', 'stripe'],
      required: true
    },
    accountDetails: {
      accountNumber: String,
      routingNumber: String,
      accountHolderName: String,
      bankName: String,
      paypalEmail: String,
      stripeAccountId: String
    },
    isVerified: {
      type: Boolean,
      default: false
    }
  },
  storeSettings: {
    storeName: {
      type: String,
      required: true
    },
    storeSlug: {
      type: String,
      required: true,
      unique: true,
      lowercase: true
    },
    storeDescription: String,
    storeCategories: [String],
    operatingHours: {
      monday: { open: String, close: String, closed: Boolean },
      tuesday: { open: String, close: String, closed: Boolean },
      wednesday: { open: String, close: String, closed: Boolean },
      thursday: { open: String, close: String, closed: Boolean },
      friday: { open: String, close: String, closed: Boolean },
      saturday: { open: String, close: String, closed: Boolean },
      sunday: { open: String, close: String, closed: Boolean }
    },
    shippingPolicy: String,
    returnPolicy: String,
    termsOfService: String
  },
  analytics: {
    totalOrders: {
      type: Number,
      default: 0
    },
    totalRevenue: {
      type: Number,
      default: 0
    },
    totalProducts: {
      type: Number,
      default: 0
    },
    averageRating: {
      type: Number,
      default: 0,
      min: 0,
      max: 5
    },
    totalReviews: {
      type: Number,
      default: 0
    },
    totalViews: {
      type: Number,
      default: 0
    },
    conversionRate: {
      type: Number,
      default: 0
    }
  },
  subscription: {
    plan: {
      type: String,
      enum: ['basic', 'premium', 'enterprise'],
      default: 'basic'
    },
    startDate: {
      type: Date,
      default: Date.now
    },
    endDate: Date,
    isActive: {
      type: Boolean,
      default: true
    },
    features: {
      maxProducts: {
        type: Number,
        default: 10
      },
      maxImages: {
        type: Number,
        default: 5
      },
      analyticsAccess: {
        type: Boolean,
        default: true
      },
      prioritySupport: {
        type: Boolean,
        default: false
      },
      customDomain: {
        type: Boolean,
        default: false
      }
    }
  },
  verification: {
    isVerified: {
      type: Boolean,
      default: false
    },
    verifiedAt: Date,
    verificationBadge: {
      type: String,
      enum: ['bronze', 'silver', 'gold', 'platinum'],
      default: 'bronze'
    },
    verificationCriteria: {
      kycCompleted: {
        type: Boolean,
        default: false
      },
      minimumOrders: {
        type: Boolean,
        default: false
      },
      goodRating: {
        type: Boolean,
        default: false
      },
      activeForMonths: {
        type: Boolean,
        default: false
      }
    }
  },
  status: {
    type: String,
    enum: ['active', 'suspended', 'pending_approval', 'rejected'],
    default: 'pending_approval'
  },
  onboardingCompleted: {
    type: Boolean,
    default: false
  },
  onboardingSteps: {
    businessInfo: {
      type: Boolean,
      default: false
    },
    kycSubmission: {
      type: Boolean,
      default: false
    },
    payoutSetup: {
      type: Boolean,
      default: false
    },
    storeSetup: {
      type: Boolean,
      default: false
    },
    firstProduct: {
      type: Boolean,
      default: false
    }
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Update the updatedAt field before saving
VendorSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

// Generate store slug from store name
VendorSchema.pre('save', function(next) {
  if (this.isModified('storeSettings.storeName') && !this.storeSettings.storeSlug) {
    this.storeSettings.storeSlug = this.storeSettings.storeName
      .toLowerCase()
      .replace(/[^a-z0-9]/g, '-')
      .replace(/-+/g, '-')
      .replace(/^-|-$/g, '');
  }
  next();
});

// Check verification criteria
VendorSchema.methods.checkVerificationCriteria = function() {
  this.verification.verificationCriteria.kycCompleted = this.kycStatus === 'verified';
  this.verification.verificationCriteria.minimumOrders = this.analytics.totalOrders >= 10;
  this.verification.verificationCriteria.goodRating = this.analytics.averageRating >= 4.0;

  const monthsActive = (Date.now() - this.createdAt) / (1000 * 60 * 60 * 24 * 30);
  this.verification.verificationCriteria.activeForMonths = monthsActive >= 3;

  // Check if all criteria are met
  const allCriteriaMet = Object.values(this.verification.verificationCriteria).every(Boolean);

  if (allCriteriaMet && !this.verification.isVerified) {
    this.verification.isVerified = true;
    this.verification.verifiedAt = new Date();

    // Assign verification badge based on performance
    if (this.analytics.totalOrders >= 1000 && this.analytics.averageRating >= 4.8) {
      this.verification.verificationBadge = 'platinum';
    } else if (this.analytics.totalOrders >= 500 && this.analytics.averageRating >= 4.5) {
      this.verification.verificationBadge = 'gold';
    } else if (this.analytics.totalOrders >= 100 && this.analytics.averageRating >= 4.2) {
      this.verification.verificationBadge = 'silver';
    }
  }

  return this.verification.isVerified;
};

// Update analytics
VendorSchema.methods.updateAnalytics = async function() {
  const Product = mongoose.model('Product');
  const Order = mongoose.model('Order');

  try {
    // Get total products
    this.analytics.totalProducts = await Product.countDocuments({ user: this.user });

    // Get vendor's product IDs
    const productIds = await Product.find({ user: this.user }).distinct('_id');

    if (productIds.length > 0) {
      // Get orders and revenue
      const orderStats = await Order.aggregate([
        {
          $match: {
            'items.product': { $in: productIds },
            status: { $ne: 'cancelled' }
          }
        },
        {
          $group: {
            _id: null,
            totalOrders: { $sum: 1 },
            totalRevenue: { $sum: '$total' }
          }
        }
      ]);

      if (orderStats.length > 0) {
        this.analytics.totalOrders = orderStats[0].totalOrders;
        this.analytics.totalRevenue = orderStats[0].totalRevenue;
      }

      // Calculate total views for all products
      const viewStats = await Product.aggregate([
        { $match: { user: this.user } },
        {
          $group: {
            _id: null,
            totalViews: { $sum: '$views' }
          }
        }
      ]);

      if (viewStats.length > 0) {
        this.analytics.totalViews = viewStats[0].totalViews || 0;
      }
    }

    return this.save();
  } catch (error) {
    console.error('Error updating vendor analytics:', error);
    return this;
  }
};

// Get onboarding progress
VendorSchema.methods.getOnboardingProgress = function() {
  const steps = Object.values(this.onboardingSteps);
  const completedSteps = steps.filter(Boolean).length;
  const totalSteps = steps.length;

  return {
    completed: completedSteps,
    total: totalSteps,
    percentage: Math.round((completedSteps / totalSteps) * 100),
    isCompleted: this.onboardingCompleted
  };
};

// Create indexes for better performance
VendorSchema.index({ user: 1 });
VendorSchema.index({ 'storeSettings.storeSlug': 1 });
VendorSchema.index({ status: 1 });
VendorSchema.index({ kycStatus: 1 });
VendorSchema.index({ 'verification.isVerified': 1 });
VendorSchema.index({ 'analytics.totalRevenue': -1 });
VendorSchema.index({ 'analytics.averageRating': -1 });

module.exports = mongoose.model('Vendor', VendorSchema);
