const asyncHandler = require('../middleware/async');
const ErrorResponse = require('../utils/errorResponse');
const LiveStream = require('../models/LiveStream');
const User = require('../models/User');
const Follow = require('../models/Follow');
const StreamView = require('../models/StreamView');
const mongoose = require('mongoose');

/**
 * @desc    Get personalized "For You LIVE" feed
 * @route   GET /api/feeds/for-you-live
 * @access  Private
 */
exports.getForYouLiveFeed = asyncHandler(async (req, res, next) => {
  const page = parseInt(req.query.page, 10) || 1;
  const limit = parseInt(req.query.limit, 10) || 10;
  const startIndex = (page - 1) * limit;

  // Get user's followed creators
  const following = await Follow.find({ follower: req.user.id }).select('following');
  const followingIds = following.map(follow => follow.following);

  // Get user's interests from profile
  const user = await User.findById(req.user.id).select('interests');
  const userInterests = user.interests || [];

  // Get user's previously viewed streams
  const viewedStreams = await StreamView.find({ user: req.user.id })
    .sort('-createdAt')
    .limit(20)
    .select('stream');

  const viewedStreamIds = viewedStreams.map(view => view.stream);

  // Build query for personalized feed
  const query = {
    status: 'live',
    $or: [
      // Streams from followed creators
      { user: { $in: followingIds } },
      // Streams with matching interests
      { tags: { $in: userInterests } },
      // Streams from creators of previously viewed streams
      { user: { $in: await LiveStream.find({ _id: { $in: viewedStreamIds } }).distinct('user') } },
      // Popular streams (high viewer count)
      { viewCount: { $gt: 10 } }
    ]
  };

  // Execute query with pagination
  const streams = await LiveStream.find(query)
    .populate('user', 'username name profilePicture isVerified')
    .sort('-viewCount -startedAt')
    .skip(startIndex)
    .limit(limit);

  // Get total count for pagination
  const total = await LiveStream.countDocuments(query);

  // Pagination result
  const pagination = {};

  if (startIndex + limit < total) {
    pagination.next = {
      page: page + 1,
      limit
    };
  }

  if (startIndex > 0) {
    pagination.prev = {
      page: page - 1,
      limit
    };
  }

  res.status(200).json({
    success: true,
    count: streams.length,
    pagination,
    total,
    data: streams
  });
});

/**
 * @desc    Get local live streams feed
 * @route   GET /api/feeds/local-lives
 * @access  Private
 */
exports.getLocalLivesFeed = asyncHandler(async (req, res, next) => {
  const page = parseInt(req.query.page, 10) || 1;
  const limit = parseInt(req.query.limit, 10) || 10;
  const startIndex = (page - 1) * limit;

  // Get user's location
  const user = await User.findById(req.user.id).select('location');

  if (!user.location || !user.location.coordinates ||
      (user.location.coordinates[0] === 0 && user.location.coordinates[1] === 0)) {
    return next(new ErrorResponse('Location information not available. Please update your location in profile settings.', 400));
  }

  const [longitude, latitude] = user.location.coordinates;
  const maxDistance = parseInt(req.query.radius, 10) || 50000; // 50km default radius

  // Find streams near user's location
  const query = {
    status: 'live',
    'location.coordinates': {
      $near: {
        $geometry: {
          type: 'Point',
          coordinates: [longitude, latitude]
        },
        $maxDistance: maxDistance
      }
    }
  };

  // Execute query with pagination
  const streams = await LiveStream.find(query)
    .populate('user', 'username name profilePicture isVerified')
    .sort('-viewCount -startedAt')
    .skip(startIndex)
    .limit(limit);

  // Get total count for pagination
  const total = await LiveStream.countDocuments(query);

  // Pagination result
  const pagination = {};

  if (startIndex + limit < total) {
    pagination.next = {
      page: page + 1,
      limit
    };
  }

  if (startIndex > 0) {
    pagination.prev = {
      page: page - 1,
      limit
    };
  }

  res.status(200).json({
    success: true,
    count: streams.length,
    pagination,
    total,
    data: streams
  });
});

/**
 * @desc    Get rising stars feed
 * @route   GET /api/feeds/rising-stars
 * @access  Public
 */
exports.getRisingStarsFeed = asyncHandler(async (req, res, next) => {
  const page = parseInt(req.query.page, 10) || 1;
  const limit = parseInt(req.query.limit, 10) || 10;
  const startIndex = (page - 1) * limit;

  // Get streams from creators who are gaining popularity quickly
  const risingCreators = await User.aggregate([
    // Join with followers collection to get follower counts
    {
      $lookup: {
        from: 'follows',
        localField: '_id',
        foreignField: 'following',
        as: 'followers'
      }
    },
    // Count followers
    {
      $addFields: {
        followerCount: { $size: '$followers' },
        // Only consider followers from the last 7 days
        recentFollowerCount: {
          $size: {
            $filter: {
              input: '$followers',
              as: 'follower',
              cond: {
                $gte: ['$$follower.createdAt', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)]
              }
            }
          }
        }
      }
    },
    // Calculate growth rate
    {
      $addFields: {
        growthRate: {
          $cond: [
            { $eq: ['$followerCount', 0] },
            0,
            { $divide: ['$recentFollowerCount', '$followerCount'] }
          ]
        }
      }
    },
    // Filter for users with significant growth
    {
      $match: {
        growthRate: { $gt: 0.1 }, // 10% growth in the last week
        followerCount: { $gt: 10 } // At least 10 followers
      }
    },
    // Sort by growth rate
    { $sort: { growthRate: -1 } },
    // Limit to top rising creators
    { $limit: 50 },
    // Project only needed fields
    { $project: { _id: 1 } }
  ]);

  const risingCreatorIds = risingCreators.map(creator => creator._id);

  // Find live streams from rising creators
  const query = {
    status: 'live',
    user: { $in: risingCreatorIds }
  };

  // Execute query with pagination
  const streams = await LiveStream.find(query)
    .populate('user', 'username name profilePicture isVerified')
    .sort('-viewCount -startedAt')
    .skip(startIndex)
    .limit(limit);

  // Get total count for pagination
  const total = await LiveStream.countDocuments(query);

  // Pagination result
  const pagination = {};

  if (startIndex + limit < total) {
    pagination.next = {
      page: page + 1,
      limit
    };
  }

  if (startIndex > 0) {
    pagination.prev = {
      page: page - 1,
      limit
    };
  }

  res.status(200).json({
    success: true,
    count: streams.length,
    pagination,
    total,
    data: streams
  });
});
