const express = require('express');
const router = express.Router();
const { protect, optionalAuth } = require('../middleware/auth');
const {
  getCampaigns,
  createCampaign,
  updateCampaign,
  deleteCampaign,
  getCampaignAnalytics,
  applyCoupon,
  getSeasonalTemplates,
  getBanners,
  createBanner,
  updateBanner,
  deleteBanner,
  getActiveBanners,
  recordBannerImpression,
  recordBannerClick,
  sendCampaign
} = require('../controllers/marketingController');

// Campaign routes
router.get('/campaigns', protect, getCampaigns);
router.post('/campaigns', protect, createCampaign);
router.put('/campaigns/:id', protect, updateCampaign);
router.delete('/campaigns/:id', protect, deleteCampaign);
router.get('/campaigns/:id/analytics', protect, getCampaignAnalytics);

// Coupon routes
router.post('/coupons/apply', protect, applyCoupon);

// Campaign sending routes
router.post('/send-campaign', protect, sendCampaign);

// Template routes
router.get('/templates', protect, getSeasonalTemplates);

// Banner routes
router.get('/banners', protect, getBanners);
router.post('/banners', protect, createBanner);
router.put('/banners/:id', protect, updateBanner);
router.delete('/banners/:id', protect, deleteBanner);

// Public banner routes
router.get('/banners/active', getActiveBanners);
router.post('/banners/:id/impression', recordBannerImpression);
router.post('/banners/:id/click', recordBannerClick);

module.exports = router;
