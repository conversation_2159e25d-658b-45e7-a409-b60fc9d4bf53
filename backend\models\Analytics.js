const mongoose = require('mongoose');

const AnalyticsSchema = new mongoose.Schema({
  type: {
    type: String,
    enum: ['sales', 'product_performance', 'visitor_tracking', 'cart_abandonment', 'campaign_performance'],
    required: true
  },
  date: {
    type: Date,
    required: true,
    default: Date.now
  },
  user: {
    type: mongoose.Schema.ObjectId,
    ref: 'User'
  },
  product: {
    type: mongoose.Schema.ObjectId,
    ref: 'Product'
  },
  campaign: {
    type: mongoose.Schema.ObjectId,
    ref: 'Campaign'
  },
  order: {
    type: mongoose.Schema.ObjectId,
    ref: 'Order'
  },
  // Sales Analytics
  salesData: {
    revenue: Number,
    orderCount: Number,
    averageOrderValue: Number,
    totalItems: Number,
    refunds: Number,
    netRevenue: Number
  },
  // Product Performance
  productData: {
    views: Number,
    clicks: Number,
    addToCart: Number,
    purchases: Number,
    conversionRate: Number,
    revenue: Number,
    inventory: Number,
    rating: Number,
    reviews: Number
  },
  // Visitor Tracking
  visitorData: {
    sessionId: String,
    ipAddress: String,
    userAgent: String,
    referrer: String,
    landingPage: String,
    exitPage: String,
    sessionDuration: Number,
    pageViews: Number,
    bounceRate: Number,
    location: {
      country: String,
      city: String,
      coordinates: {
        lat: Number,
        lng: Number
      }
    },
    device: {
      type: String,
      browser: String,
      os: String,
      screenResolution: String
    }
  },
  // Cart Abandonment
  cartData: {
    cartId: String,
    itemCount: Number,
    cartValue: Number,
    abandonedAt: Date,
    recoveryEmailSent: Boolean,
    recovered: Boolean,
    recoveredAt: Date,
    recoveryRevenue: Number
  },
  // Heatmap Data
  heatmapData: {
    page: String,
    elementId: String,
    elementType: String,
    clickX: Number,
    clickY: Number,
    scrollDepth: Number,
    timeOnElement: Number
  },
  // Campaign Performance
  campaignData: {
    impressions: Number,
    clicks: Number,
    conversions: Number,
    revenue: Number,
    cost: Number,
    ctr: Number,
    conversionRate: Number,
    roas: Number,
    cpc: Number,
    cpm: Number
  },
  metadata: {
    source: String,
    medium: String,
    campaign: String,
    content: String,
    term: String,
    customDimensions: mongoose.Schema.Types.Mixed
  },
  createdAt: {
    type: Date,
    default: Date.now
  }
});

// Create compound indexes for better query performance
AnalyticsSchema.index({ type: 1, date: -1 });
AnalyticsSchema.index({ user: 1, date: -1 });
AnalyticsSchema.index({ product: 1, date: -1 });
AnalyticsSchema.index({ campaign: 1, date: -1 });
AnalyticsSchema.index({ 'visitorData.sessionId': 1 });
AnalyticsSchema.index({ 'cartData.cartId': 1 });
AnalyticsSchema.index({ date: -1, type: 1 });

// Static method to get sales analytics
AnalyticsSchema.statics.getSalesAnalytics = function(startDate, endDate, groupBy = 'day') {
  const matchStage = {
    type: 'sales',
    date: {
      $gte: new Date(startDate),
      $lte: new Date(endDate)
    }
  };

  let groupStage;
  switch (groupBy) {
    case 'hour':
      groupStage = {
        $group: {
          _id: {
            year: { $year: '$date' },
            month: { $month: '$date' },
            day: { $dayOfMonth: '$date' },
            hour: { $hour: '$date' }
          },
          revenue: { $sum: '$salesData.revenue' },
          orders: { $sum: '$salesData.orderCount' },
          avgOrderValue: { $avg: '$salesData.averageOrderValue' },
          items: { $sum: '$salesData.totalItems' }
        }
      };
      break;
    case 'month':
      groupStage = {
        $group: {
          _id: {
            year: { $year: '$date' },
            month: { $month: '$date' }
          },
          revenue: { $sum: '$salesData.revenue' },
          orders: { $sum: '$salesData.orderCount' },
          avgOrderValue: { $avg: '$salesData.averageOrderValue' },
          items: { $sum: '$salesData.totalItems' }
        }
      };
      break;
    default: // day
      groupStage = {
        $group: {
          _id: {
            year: { $year: '$date' },
            month: { $month: '$date' },
            day: { $dayOfMonth: '$date' }
          },
          revenue: { $sum: '$salesData.revenue' },
          orders: { $sum: '$salesData.orderCount' },
          avgOrderValue: { $avg: '$salesData.averageOrderValue' },
          items: { $sum: '$salesData.totalItems' }
        }
      };
  }

  return this.aggregate([
    { $match: matchStage },
    groupStage,
    { $sort: { '_id': 1 } }
  ]);
};

// Static method to get product performance
AnalyticsSchema.statics.getProductPerformance = function(productId, startDate, endDate) {
  return this.aggregate([
    {
      $match: {
        type: 'product_performance',
        product: mongoose.Types.ObjectId(productId),
        date: {
          $gte: new Date(startDate),
          $lte: new Date(endDate)
        }
      }
    },
    {
      $group: {
        _id: '$product',
        totalViews: { $sum: '$productData.views' },
        totalClicks: { $sum: '$productData.clicks' },
        totalAddToCart: { $sum: '$productData.addToCart' },
        totalPurchases: { $sum: '$productData.purchases' },
        totalRevenue: { $sum: '$productData.revenue' },
        avgConversionRate: { $avg: '$productData.conversionRate' },
        avgRating: { $avg: '$productData.rating' }
      }
    }
  ]);
};

// Static method to get visitor analytics
AnalyticsSchema.statics.getVisitorAnalytics = function(startDate, endDate) {
  return this.aggregate([
    {
      $match: {
        type: 'visitor_tracking',
        date: {
          $gte: new Date(startDate),
          $lte: new Date(endDate)
        }
      }
    },
    {
      $group: {
        _id: null,
        totalVisitors: { $addToSet: '$visitorData.sessionId' },
        totalPageViews: { $sum: '$visitorData.pageViews' },
        avgSessionDuration: { $avg: '$visitorData.sessionDuration' },
        avgBounceRate: { $avg: '$visitorData.bounceRate' },
        topCountries: { $push: '$visitorData.location.country' },
        topDevices: { $push: '$visitorData.device.type' },
        topBrowsers: { $push: '$visitorData.device.browser' }
      }
    },
    {
      $project: {
        totalVisitors: { $size: '$totalVisitors' },
        totalPageViews: 1,
        avgSessionDuration: 1,
        avgBounceRate: 1,
        topCountries: 1,
        topDevices: 1,
        topBrowsers: 1
      }
    }
  ]);
};

// Static method to get cart abandonment data
AnalyticsSchema.statics.getCartAbandonmentData = function(startDate, endDate) {
  return this.aggregate([
    {
      $match: {
        type: 'cart_abandonment',
        date: {
          $gte: new Date(startDate),
          $lte: new Date(endDate)
        }
      }
    },
    {
      $group: {
        _id: null,
        totalAbandoned: { $sum: 1 },
        totalAbandonedValue: { $sum: '$cartData.cartValue' },
        recoveryEmailsSent: { $sum: { $cond: ['$cartData.recoveryEmailSent', 1, 0] } },
        recovered: { $sum: { $cond: ['$cartData.recovered', 1, 0] } },
        recoveredRevenue: { $sum: '$cartData.recoveryRevenue' },
        avgCartValue: { $avg: '$cartData.cartValue' },
        avgItemCount: { $avg: '$cartData.itemCount' }
      }
    },
    {
      $project: {
        totalAbandoned: 1,
        totalAbandonedValue: 1,
        recoveryEmailsSent: 1,
        recovered: 1,
        recoveredRevenue: 1,
        recoveryRate: { $divide: ['$recovered', '$totalAbandoned'] },
        avgCartValue: 1,
        avgItemCount: 1
      }
    }
  ]);
};

module.exports = mongoose.model('Analytics', AnalyticsSchema);
