import React, { useState, useEffect } from 'react'
import { Navigate } from 'react-router-dom'
import {
  Box,
  Card,
  CardContent,
  TextField,
  Button,
  Typography,
  Container,
  Tab,
  Tabs,
  CircularProgress,
  Alert
} from '@mui/material'
import { useAuth } from '../context/AuthContext'
import { useSnackbar } from 'notistack'

const AuthPage = () => {
  const [tabValue, setTabValue] = useState(0)
  const [formData, setFormData] = useState({
    name: '',
    username: '',
    email: '',
    password: '',
    confirmPassword: ''
  })

  const { register, login, isAuthenticated, loading, error, clearErrors } = useAuth()
  const { enqueueSnackbar } = useSnackbar()

  useEffect(() => {
    if (error) {
      enqueueSnackbar(error, { variant: 'error' })
      clearErrors()
    }
  }, [error, clearErrors, enqueueSnackbar])

  const { name, username, email, password, confirmPassword } = formData

  const onChange = (e) => {
    setFormData({ ...formData, [e.target.name]: e.target.value })
  }

  const onSubmit = async (e) => {
    e.preventDefault()

    if (tabValue === 0) {
      login({ email, password })
    } else {
      if (password !== confirmPassword) {
        enqueueSnackbar('Passwords do not match', { variant: 'error' })
        return
      }
      register({ name, username, email, password })
    }
  }

  if (isAuthenticated) {
    return <Navigate to="/" />
  }

  return (
    <Container maxWidth="sm" sx={{ minHeight: '100vh', display: 'flex', alignItems: 'center' }}>
      <Card sx={{ width: '100%', maxWidth: 400, mx: 'auto' }}>
        <CardContent sx={{ p: 4 }}>
          <Box textAlign="center" mb={3}>
            <Typography variant="h4" component="h1" gutterBottom color="primary" fontWeight="bold">
              Let's Talk
            </Typography>
            <Typography variant="body1" color="text.secondary">
              {tabValue === 0 ? 'Welcome back!' : 'Join our community'}
            </Typography>
          </Box>

          <Tabs value={tabValue} onChange={(e, newValue) => setTabValue(newValue)} centered sx={{ mb: 3 }}>
            <Tab label="Sign In" />
            <Tab label="Sign Up" />
          </Tabs>

          <Box component="form" onSubmit={onSubmit} sx={{ mt: 2 }}>
            {tabValue === 1 && (
              <>
                <TextField
                  fullWidth
                  label="Full Name"
                  name="name"
                  value={name}
                  onChange={onChange}
                  margin="normal"
                  required
                />
                <TextField
                  fullWidth
                  label="Username"
                  name="username"
                  value={username}
                  onChange={onChange}
                  margin="normal"
                  required
                />
              </>
            )}

            <TextField
              fullWidth
              label="Email"
              name="email"
              type="email"
              value={email}
              onChange={onChange}
              margin="normal"
              required
            />

            <TextField
              fullWidth
              label="Password"
              name="password"
              type="password"
              value={password}
              onChange={onChange}
              margin="normal"
              required
            />

            {tabValue === 1 && (
              <TextField
                fullWidth
                label="Confirm Password"
                name="confirmPassword"
                type="password"
                value={confirmPassword}
                onChange={onChange}
                margin="normal"
                required
              />
            )}

            <Button
              type="submit"
              fullWidth
              variant="contained"
              size="large"
              disabled={loading}
              sx={{ mt: 3, mb: 2 }}
            >
              {loading ? (
                <Box display="flex" alignItems="center">
                  <CircularProgress size={20} sx={{ mr: 1 }} />
                  {tabValue === 0 ? 'Signing In...' : 'Creating Account...'}
                </Box>
              ) : (
                tabValue === 0 ? 'Sign In' : 'Create Account'
              )}
            </Button>
          </Box>
        </CardContent>
      </Card>
    </Container>
  )
}

export default AuthPage
