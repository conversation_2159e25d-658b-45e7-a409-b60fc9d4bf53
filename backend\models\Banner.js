const mongoose = require('mongoose');

const BannerSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Please add a banner name'],
    trim: true,
    maxlength: [100, 'Name cannot be more than 100 characters']
  },
  type: {
    type: String,
    enum: ['promotional', 'seasonal', 'welcome', 'exit_intent', 'product_boost', 'announcement'],
    required: true
  },
  template: {
    type: String,
    enum: ['basic', 'gradient', 'image_overlay', 'countdown', 'carousel', 'video', 'interactive'],
    default: 'basic'
  },
  position: {
    type: String,
    enum: ['top', 'bottom', 'popup', 'sidebar', 'inline', 'floating', 'header', 'footer'],
    default: 'top'
  },
  status: {
    type: String,
    enum: ['draft', 'active', 'paused', 'completed'],
    default: 'draft'
  },
  priority: {
    type: Number,
    default: 1,
    min: 1,
    max: 10
  },
  design: {
    layout: {
      width: {
        type: String,
        default: '100%'
      },
      height: {
        type: String,
        default: 'auto'
      },
      padding: {
        type: String,
        default: '20px'
      },
      margin: {
        type: String,
        default: '0'
      },
      borderRadius: {
        type: String,
        default: '8px'
      }
    },
    colors: {
      background: {
        type: String,
        default: '#ffffff'
      },
      text: {
        type: String,
        default: '#333333'
      },
      button: {
        type: String,
        default: '#007bff'
      },
      buttonText: {
        type: String,
        default: '#ffffff'
      },
      accent: {
        type: String,
        default: '#ff6b6b'
      }
    },
    gradient: {
      enabled: {
        type: Boolean,
        default: false
      },
      direction: {
        type: String,
        default: 'to right'
      },
      colors: [String]
    },
    typography: {
      headlineFont: {
        type: String,
        default: 'Arial, sans-serif'
      },
      headlineSize: {
        type: String,
        default: '24px'
      },
      headlineWeight: {
        type: String,
        default: 'bold'
      },
      bodyFont: {
        type: String,
        default: 'Arial, sans-serif'
      },
      bodySize: {
        type: String,
        default: '16px'
      }
    },
    animation: {
      enabled: {
        type: Boolean,
        default: false
      },
      type: {
        type: String,
        enum: ['fade', 'slide', 'bounce', 'zoom', 'pulse'],
        default: 'fade'
      },
      duration: {
        type: String,
        default: '0.5s'
      },
      delay: {
        type: String,
        default: '0s'
      }
    }
  },
  content: {
    headline: {
      type: String,
      maxlength: [100, 'Headline cannot be more than 100 characters']
    },
    subheadline: {
      type: String,
      maxlength: [200, 'Subheadline cannot be more than 200 characters']
    },
    description: {
      type: String,
      maxlength: [500, 'Description cannot be more than 500 characters']
    },
    buttonText: {
      type: String,
      maxlength: [50, 'Button text cannot be more than 50 characters']
    },
    buttonUrl: String,
    images: [{
      url: String,
      alt: String,
      position: {
        type: String,
        enum: ['left', 'right', 'center', 'background'],
        default: 'center'
      }
    }],
    video: {
      url: String,
      thumbnail: String,
      autoplay: {
        type: Boolean,
        default: false
      },
      muted: {
        type: Boolean,
        default: true
      }
    },
    countdown: {
      enabled: {
        type: Boolean,
        default: false
      },
      endDate: Date,
      format: {
        type: String,
        default: 'DD:HH:MM:SS'
      }
    }
  },
  targeting: {
    audience: {
      type: String,
      enum: ['all', 'new_visitors', 'returning_visitors', 'logged_in', 'logged_out', 'custom'],
      default: 'all'
    },
    devices: [{
      type: String,
      enum: ['desktop', 'tablet', 'mobile']
    }],
    locations: [String],
    pages: [{
      type: String,
      enum: ['all', 'homepage', 'product', 'category', 'cart', 'checkout', 'custom']
    }],
    customPages: [String],
    timeSchedule: {
      enabled: {
        type: Boolean,
        default: false
      },
      startTime: String, // HH:MM format
      endTime: String,   // HH:MM format
      days: [{
        type: String,
        enum: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']
      }],
      timezone: {
        type: String,
        default: 'UTC'
      }
    }
  },
  triggers: {
    exitIntent: {
      enabled: {
        type: Boolean,
        default: false
      },
      sensitivity: {
        type: String,
        enum: ['low', 'medium', 'high'],
        default: 'medium'
      }
    },
    timeDelay: {
      enabled: {
        type: Boolean,
        default: false
      },
      seconds: {
        type: Number,
        default: 5
      }
    },
    scrollPercentage: {
      enabled: {
        type: Boolean,
        default: false
      },
      percentage: {
        type: Number,
        default: 50
      }
    },
    pageViews: {
      enabled: {
        type: Boolean,
        default: false
      },
      count: {
        type: Number,
        default: 3
      }
    }
  },
  campaign: {
    type: mongoose.Schema.ObjectId,
    ref: 'Campaign'
  },
  analytics: {
    impressions: {
      type: Number,
      default: 0
    },
    clicks: {
      type: Number,
      default: 0
    },
    conversions: {
      type: Number,
      default: 0
    },
    ctr: {
      type: Number,
      default: 0
    },
    conversionRate: {
      type: Number,
      default: 0
    },
    revenue: {
      type: Number,
      default: 0
    }
  },
  startDate: {
    type: Date,
    default: Date.now
  },
  endDate: Date,
  creator: {
    type: mongoose.Schema.ObjectId,
    ref: 'User',
    required: true
  },
  isActive: {
    type: Boolean,
    default: true
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Update the updatedAt field before saving
BannerSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

// Calculate analytics before saving
BannerSchema.pre('save', function(next) {
  if (this.analytics.clicks > 0 && this.analytics.impressions > 0) {
    this.analytics.ctr = (this.analytics.clicks / this.analytics.impressions) * 100;
  }
  
  if (this.analytics.conversions > 0 && this.analytics.clicks > 0) {
    this.analytics.conversionRate = (this.analytics.conversions / this.analytics.clicks) * 100;
  }
  
  next();
});

// Instance method to check if banner should be displayed
BannerSchema.methods.shouldDisplay = function(context = {}) {
  const now = new Date();
  
  // Check if banner is active and within date range
  if (this.status !== 'active') return false;
  if (this.startDate && this.startDate > now) return false;
  if (this.endDate && this.endDate < now) return false;
  
  // Check device targeting
  if (this.targeting.devices.length > 0 && context.device) {
    if (!this.targeting.devices.includes(context.device)) return false;
  }
  
  // Check page targeting
  if (this.targeting.pages.length > 0 && context.page) {
    if (!this.targeting.pages.includes('all') && !this.targeting.pages.includes(context.page)) {
      if (this.targeting.customPages.length > 0) {
        if (!this.targeting.customPages.includes(context.currentUrl)) return false;
      } else {
        return false;
      }
    }
  }
  
  // Check time schedule
  if (this.targeting.timeSchedule.enabled) {
    const currentTime = now.toTimeString().slice(0, 5); // HH:MM format
    const currentDay = now.toLocaleDateString('en-US', { weekday: 'lowercase' });
    
    if (this.targeting.timeSchedule.startTime && currentTime < this.targeting.timeSchedule.startTime) return false;
    if (this.targeting.timeSchedule.endTime && currentTime > this.targeting.timeSchedule.endTime) return false;
    if (this.targeting.timeSchedule.days.length > 0 && !this.targeting.timeSchedule.days.includes(currentDay)) return false;
  }
  
  return true;
};

// Instance method to record impression
BannerSchema.methods.recordImpression = function() {
  this.analytics.impressions += 1;
  return this.save();
};

// Instance method to record click
BannerSchema.methods.recordClick = function() {
  this.analytics.clicks += 1;
  return this.save();
};

// Instance method to record conversion
BannerSchema.methods.recordConversion = function(revenue = 0) {
  this.analytics.conversions += 1;
  this.analytics.revenue += revenue;
  return this.save();
};

// Static method to get active banners for a context
BannerSchema.statics.getActiveBanners = function(context = {}) {
  return this.find({ 
    status: 'active',
    isActive: true,
    $or: [
      { startDate: { $lte: new Date() } },
      { startDate: { $exists: false } }
    ],
    $or: [
      { endDate: { $gte: new Date() } },
      { endDate: { $exists: false } }
    ]
  })
  .sort({ priority: -1, createdAt: -1 })
  .populate('campaign', 'name type')
  .then(banners => {
    return banners.filter(banner => banner.shouldDisplay(context));
  });
};

// Create indexes for better performance
BannerSchema.index({ status: 1, isActive: 1 });
BannerSchema.index({ type: 1, status: 1 });
BannerSchema.index({ position: 1, status: 1 });
BannerSchema.index({ priority: -1, createdAt: -1 });
BannerSchema.index({ startDate: 1, endDate: 1 });
BannerSchema.index({ creator: 1, createdAt: -1 });
BannerSchema.index({ campaign: 1 });

module.exports = mongoose.model('Banner', BannerSchema);
