const express = require('express');
const router = express.Router();
const { protect, authorize, optionalAuth } = require('../middleware/auth');
const { upload, processUploadedFiles } = require('../middleware/fileUpload');
const {
  getUsers,
  getUser,
  getUserByUsername,
  updateProfile,
  updateProfilePicture,
  searchUsers,
  getCurrentUserProfile,
  getSuggestedUsers,
} = require('../controllers/userController');

// Public routes
router.get('/search', searchUsers);
router.get('/username/:username', optionalAuth, getUserByUsername);

// Protected routes
router.use(protect);
router.get('/me', getCurrentUserProfile);
router.get('/suggested', getSuggestedUsers);
router.put('/profile', updateProfile);
router.put('/profile-picture', upload.single('profilePicture'), processUploadedFiles, updateProfilePicture);

// Admin routes
router.use(authorize('admin'));
router.get('/', getUsers);
router.get('/:id', getUser);

module.exports = router;
