const asyncHandler = require('express-async-handler');
const Post = require('../models/Post');

// @desc    Get gallery (popular images)
// @route   GET /api/gallery
// @access  Public
const getGallery = asyncHandler(async (req, res) => {
  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 20;
  const skip = (page - 1) * limit;

  // Get popular posts
  const posts = await Post.find({ isArchived: false })
    .sort({ likesCount: -1, commentsCount: -1 })
    .skip(skip)
    .limit(limit)
    .populate('user', 'username fullName avatar');

  const total = await Post.countDocuments({ isArchived: false });

  res.status(200).json({
    success: true,
    gallery: posts,
    pagination: {
      page,
      limit,
      total,
      pages: Math.ceil(total / limit)
    }
  });
});

module.exports = {
  getGallery
};
