const mongoose = require('mongoose');

const BlockedWordSchema = new mongoose.Schema({
  // The blocked word or phrase
  word: {
    type: String,
    required: true,
    trim: true,
    lowercase: true
  },
  // Severity level
  severity: {
    type: String,
    enum: ['low', 'medium', 'high'],
    default: 'medium'
  },
  // Category of the blocked word
  category: {
    type: String,
    enum: ['profanity', 'hate_speech', 'sexual', 'violence', 'spam', 'custom'],
    default: 'custom'
  },
  // Scope of the block
  scope: {
    type: String,
    enum: ['global', 'stream', 'user'],
    default: 'global'
  },
  // Reference to stream or user if scope is not global
  scopeId: {
    type: mongoose.Schema.Types.ObjectId,
    refPath: 'scopeModel',
    default: null
  },
  // Model reference for scopeId
  scopeModel: {
    type: String,
    enum: ['LiveStream', 'User'],
    default: null
  },
  // Action to take when word is detected
  action: {
    type: String,
    enum: ['flag', 'replace', 'block'],
    default: 'flag'
  },
  // Replacement text if action is 'replace'
  replacement: {
    type: String,
    default: '****'
  },
  // Whether to use exact match or partial match
  exactMatch: {
    type: Boolean,
    default: false
  },
  // Whether to use regex pattern matching
  isRegex: {
    type: Boolean,
    default: false
  },
  // Created by user
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  // Active status
  isActive: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true
});

// Create indexes for efficient queries
BlockedWordSchema.index({ word: 1, scope: 1, scopeId: 1 }, { unique: true });
BlockedWordSchema.index({ category: 1 });
BlockedWordSchema.index({ severity: 1 });
BlockedWordSchema.index({ scope: 1, scopeId: 1 });

module.exports = mongoose.model('BlockedWord', BlockedWordSchema);
