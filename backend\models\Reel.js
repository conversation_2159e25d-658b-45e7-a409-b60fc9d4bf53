const mongoose = require('mongoose');

const ReelSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  video: {
    url: {
      type: String,
      required: true,
    },
    publicId: {
      type: String,
      required: true,
    },
    duration: {
      type: Number,
      required: true,
    },
  },
  thumbnail: {
    url: {
      type: String,
    },
    publicId: {
      type: String,
    },
  },
  caption: {
    type: String,
    maxlength: [2200, 'Caption cannot be more than 2200 characters'],
  },
  audio: {
    name: {
      type: String,
    },
    artist: {
      type: String,
    },
    originalAudio: {
      type: <PERSON><PERSON><PERSON>,
      default: true,
    },
    sourceReelId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Reel',
    },
  },
  emotions: [
    {
      emotion: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Emotion',
        required: true,
      },
      intensity: {
        type: Number,
        min: 1,
        max: 10,
        default: 5,
      },
      position: {
        x: {
          type: Number,
          min: 0,
          max: 100,
          default: 50,
        },
        y: {
          type: Number,
          min: 0,
          max: 100,
          default: 50,
        },
      },
      timestamp: {
        start: {
          type: Number,
          default: 0,
        },
        end: {
          type: Number,
        },
      },
      audio: {
        enabled: {
          type: Boolean,
          default: false,
        },
        cue: {
          type: mongoose.Schema.Types.ObjectId,
          ref: 'AudioCue',
        },
        volume: {
          type: Number,
          min: 0,
          max: 1,
          default: 0.5,
        },
      },
      visualEffect: {
        enabled: {
          type: Boolean,
          default: false,
        },
        type: {
          type: String,
          enum: ['pulse', 'glow', 'particles', 'color', 'none'],
          default: 'none',
        },
        intensity: {
          type: Number,
          min: 1,
          max: 10,
          default: 5,
        },
      },
    },
  ],
  arEffects: [
    {
      type: {
        type: String,
        enum: ['filter', 'object', 'background', 'animation'],
        required: true,
      },
      effectId: {
        type: String,
        required: true,
      },
      position: {
        x: {
          type: Number,
          default: 50,
        },
        y: {
          type: Number,
          default: 50,
        },
        z: {
          type: Number,
          default: 0,
        },
      },
      scale: {
        x: {
          type: Number,
          default: 1,
        },
        y: {
          type: Number,
          default: 1,
        },
        z: {
          type: Number,
          default: 1,
        },
      },
      rotation: {
        x: {
          type: Number,
          default: 0,
        },
        y: {
          type: Number,
          default: 0,
        },
        z: {
          type: Number,
          default: 0,
        },
      },
      timestamp: {
        start: {
          type: Number,
          default: 0,
        },
        end: {
          type: Number,
        },
      },
    },
  ],
  tags: [
    {
      type: String,
    },
  ],
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true },
});

// Virtual fields for likes and comments counts
ReelSchema.virtual('likesCount', {
  ref: 'Like',
  localField: '_id',
  foreignField: 'reel',
  count: true,
});

ReelSchema.virtual('commentsCount', {
  ref: 'Comment',
  localField: '_id',
  foreignField: 'reel',
  count: true,
});

ReelSchema.virtual('viewsCount', {
  ref: 'View',
  localField: '_id',
  foreignField: 'reel',
  count: true,
});

module.exports = mongoose.model('Reel', ReelSchema);
