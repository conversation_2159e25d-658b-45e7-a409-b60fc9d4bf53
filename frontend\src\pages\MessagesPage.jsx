import React, { useState, useEffect, useRef } from 'react'
import {
  Box,
  Container,
  Typo<PERSON>,
  Card,
  CardContent,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Avatar,
  TextField,
  IconButton,
  Paper,
  Divider,
  Badge,
  InputAdornment,
  Chip,
  CircularProgress,
  Button
} from '@mui/material'
import {
  Send,
  Search,
  AttachFile,
  EmojiEmotions,
  MoreVert,
  Phone,
  VideoCall,
  Add,
  Circle
} from '@mui/icons-material'
import { useSnackbar } from 'notistack'
import { useAuth } from '../context/AuthContext'
import { useSocket } from '../context/SocketContext'
import axios from '../utils/fixedAxios'

const MessagesPage = () => {
  const [conversations, setConversations] = useState([])
  const [selectedConversation, setSelectedConversation] = useState(null)
  const [messages, setMessages] = useState([])
  const [newMessage, setNewMessage] = useState('')
  const [loading, setLoading] = useState(true)
  const [sendingMessage, setSendingMessage] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const [onlineUsers, setOnlineUsers] = useState(new Set())

  const { user } = useAuth()
  const { socket } = useSocket()
  const { enqueueSnackbar } = useSnackbar()
  const messagesEndRef = useRef(null)

  useEffect(() => {
    fetchConversations()
  }, [])

  useEffect(() => {
    if (selectedConversation) {
      fetchMessages(selectedConversation._id)
    }
  }, [selectedConversation])

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  useEffect(() => {
    if (socket) {
      socket.on('newMessage', handleNewMessage)
      socket.on('getOnlineUsers', (users) => {
        setOnlineUsers(new Set(users))
      })

      return () => {
        socket.off('newMessage')
        socket.off('getOnlineUsers')
      }
    }
  }, [socket])

  const fetchConversations = async () => {
    try {
      setLoading(true)
      const response = await axios.get('/api/conversations')
      setConversations(response.data.data || response.data.conversations || [])
    } catch (error) {
      console.error('Error fetching conversations:', error)
      enqueueSnackbar('Failed to load conversations', { variant: 'error' })
    } finally {
      setLoading(false)
    }
  }

  const fetchMessages = async (conversationId) => {
    try {
      const response = await axios.get(`/api/messages/${conversationId}`)
      setMessages(response.data.data || response.data.messages || [])

      // Mark conversation as read
      await axios.put(`/api/messages/${conversationId}/read`)
    } catch (error) {
      console.error('Error fetching messages:', error)
      enqueueSnackbar('Failed to load messages', { variant: 'error' })
    }
  }

  const handleNewMessage = (message) => {
    if (selectedConversation && message.conversation === selectedConversation._id) {
      setMessages(prev => [...prev, message])
    }

    // Update conversation list
    setConversations(prev => prev.map(conv =>
      conv._id === message.conversation
        ? { ...conv, lastMessage: message, unreadCount: conv._id === selectedConversation?._id ? 0 : (conv.unreadCount || 0) + 1 }
        : conv
    ))
  }

  const sendMessage = async (e) => {
    e.preventDefault()

    if (!newMessage.trim() || !selectedConversation) return

    try {
      setSendingMessage(true)
      const response = await axios.post(`/api/messages/${selectedConversation._id}`, {
        text: newMessage.trim()
      })

      const message = response.data.data || response.data.message
      setMessages(prev => [...prev, message])
      setNewMessage('')

      // Emit socket event
      if (socket) {
        socket.emit('sendMessage', message)
      }

    } catch (error) {
      console.error('Error sending message:', error)
      enqueueSnackbar('Failed to send message', { variant: 'error' })
    } finally {
      setSendingMessage(false)
    }
  }

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  const filteredConversations = conversations.filter(conv =>
    conv.participants?.some(participant =>
      participant.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      participant.username?.toLowerCase().includes(searchQuery.toLowerCase())
    )
  )

  const getOtherParticipant = (conversation) => {
    return conversation.participants?.find(p => p._id !== user?.id)
  }

  const isUserOnline = (userId) => {
    return onlineUsers.has(userId)
  }

  return (
    <Box sx={{ height: 'calc(100vh - 100px)', display: 'flex' }}>
      {/* Conversations Sidebar */}
      <Box sx={{ width: '350px', borderRight: 1, borderColor: 'divider', display: 'flex', flexDirection: 'column' }}>
        {/* Header */}
        <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
          <Typography variant="h5" fontWeight="bold" gutterBottom>
            Messages
          </Typography>
          <TextField
            fullWidth
            size="small"
            placeholder="Search conversations..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Search />
                </InputAdornment>
              )
            }}
          />
        </Box>

        {/* Conversations List */}
        <Box sx={{ flex: 1, overflow: 'auto' }}>
          {loading ? (
            <Box display="flex" justifyContent="center" py={4}>
              <CircularProgress />
            </Box>
          ) : filteredConversations.length === 0 ? (
            <Box textAlign="center" py={4}>
              <Typography variant="body2" color="text.secondary">
                {searchQuery ? 'No conversations found' : 'No conversations yet'}
              </Typography>
              {!searchQuery && (
                <Button variant="outlined" startIcon={<Add />} sx={{ mt: 2 }}>
                  Start New Chat
                </Button>
              )}
            </Box>
          ) : (
            <List sx={{ p: 0 }}>
              {filteredConversations.map((conversation) => {
                const otherParticipant = getOtherParticipant(conversation)
                const isOnline = isUserOnline(otherParticipant?._id)

                return (
                  <ListItem
                    key={conversation._id}
                    button
                    selected={selectedConversation?._id === conversation._id}
                    onClick={() => setSelectedConversation(conversation)}
                    sx={{
                      borderBottom: 1,
                      borderColor: 'divider',
                      '&.Mui-selected': {
                        bgcolor: 'primary.light',
                        '&:hover': {
                          bgcolor: 'primary.light',
                        }
                      }
                    }}
                  >
                    <ListItemAvatar>
                      <Badge
                        overlap="circular"
                        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
                        badgeContent={
                          isOnline ? (
                            <Circle sx={{ color: 'success.main', fontSize: 12 }} />
                          ) : null
                        }
                      >
                        <Avatar src={otherParticipant?.avatar}>
                          {otherParticipant?.name?.charAt(0)?.toUpperCase()}
                        </Avatar>
                      </Badge>
                    </ListItemAvatar>
                    <ListItemText
                      primary={
                        <Box display="flex" justifyContent="space-between" alignItems="center">
                          <Typography variant="subtitle1" fontWeight="medium">
                            {otherParticipant?.name}
                          </Typography>
                          {conversation.unreadCount > 0 && (
                            <Chip
                              label={conversation.unreadCount}
                              size="small"
                              color="primary"
                              sx={{ minWidth: 20, height: 20 }}
                            />
                          )}
                        </Box>
                      }
                      secondary={
                        <Typography variant="body2" color="text.secondary" noWrap>
                          {conversation.lastMessage?.content || 'No messages yet'}
                        </Typography>
                      }
                    />
                  </ListItem>
                )
              })}
            </List>
          )}
        </Box>
      </Box>

      {/* Chat Area */}
      <Box sx={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
        {selectedConversation ? (
          <>
            {/* Chat Header */}
            <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider', display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <Box display="flex" alignItems="center">
                <Avatar src={getOtherParticipant(selectedConversation)?.avatar} sx={{ mr: 2 }}>
                  {getOtherParticipant(selectedConversation)?.name?.charAt(0)?.toUpperCase()}
                </Avatar>
                <Box>
                  <Typography variant="h6" fontWeight="medium">
                    {getOtherParticipant(selectedConversation)?.name}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    {isUserOnline(getOtherParticipant(selectedConversation)?._id) ? 'Online' : 'Offline'}
                  </Typography>
                </Box>
              </Box>
              <Box>
                <IconButton>
                  <Phone />
                </IconButton>
                <IconButton>
                  <VideoCall />
                </IconButton>
                <IconButton>
                  <MoreVert />
                </IconButton>
              </Box>
            </Box>

            {/* Messages Area */}
            <Box sx={{ flex: 1, overflow: 'auto', p: 2, display: 'flex', flexDirection: 'column', gap: 1 }}>
              {messages.map((message) => {
                const isOwnMessage = message.sender === user?.id

                return (
                  <Box
                    key={message._id}
                    sx={{
                      display: 'flex',
                      justifyContent: isOwnMessage ? 'flex-end' : 'flex-start',
                      mb: 1
                    }}
                  >
                    <Paper
                      sx={{
                        p: 1.5,
                        maxWidth: '70%',
                        bgcolor: isOwnMessage ? 'primary.main' : 'grey.100',
                        color: isOwnMessage ? 'white' : 'text.primary',
                        borderRadius: 2,
                        borderTopRightRadius: isOwnMessage ? 0.5 : 2,
                        borderTopLeftRadius: isOwnMessage ? 2 : 0.5
                      }}
                    >
                      <Typography variant="body1">
                        {message.content}
                      </Typography>
                      <Typography
                        variant="caption"
                        sx={{
                          opacity: 0.7,
                          display: 'block',
                          textAlign: 'right',
                          mt: 0.5
                        }}
                      >
                        {new Date(message.createdAt).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                      </Typography>
                    </Paper>
                  </Box>
                )
              })}
              <div ref={messagesEndRef} />
            </Box>

            {/* Message Input */}
            <Box sx={{ p: 2, borderTop: 1, borderColor: 'divider' }}>
              <Box component="form" onSubmit={sendMessage} sx={{ display: 'flex', gap: 1, alignItems: 'flex-end' }}>
                <IconButton>
                  <AttachFile />
                </IconButton>
                <TextField
                  fullWidth
                  multiline
                  maxRows={4}
                  placeholder="Type a message..."
                  value={newMessage}
                  onChange={(e) => setNewMessage(e.target.value)}
                  onKeyPress={(e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                      e.preventDefault()
                      sendMessage(e)
                    }
                  }}
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">
                        <IconButton>
                          <EmojiEmotions />
                        </IconButton>
                      </InputAdornment>
                    )
                  }}
                />
                <IconButton
                  type="submit"
                  color="primary"
                  disabled={!newMessage.trim() || sendingMessage}
                  sx={{
                    bgcolor: 'primary.main',
                    color: 'white',
                    '&:hover': { bgcolor: 'primary.dark' },
                    '&:disabled': { bgcolor: 'grey.300' }
                  }}
                >
                  {sendingMessage ? <CircularProgress size={20} /> : <Send />}
                </IconButton>
              </Box>
            </Box>
          </>
        ) : (
          <Box sx={{ flex: 1, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
            <Box textAlign="center">
              <Typography variant="h6" gutterBottom>
                Select a conversation
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Choose a conversation from the sidebar to start messaging
              </Typography>
            </Box>
          </Box>
        )}
      </Box>
    </Box>
  )
}

export default MessagesPage
