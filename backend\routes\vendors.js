const express = require('express');
const router = express.Router();
const multer = require('multer');
const { protect, authorize } = require('../middleware/auth');
const {
  registerVendor,
  submitKYC,
  setupPayout,
  setupStore,
  getDashboard,
  getProfile,
  updateProfile,
  getStorefront,
  getOrders,
  updateOrderStatus,
  getSalesReports,
  getReviews,
  respondToReview,
  completeOnboarding,
  followVendor,
  unfollowVendor
} = require('../controllers/vendorController');

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, 'uploads/');
  },
  filename: function (req, file, cb) {
    cb(null, `${Date.now()}-${file.originalname}`);
  }
});

const upload = multer({
  storage,
  limits: {
    fileSize: 10 * 1024 * 1024 // 10MB limit
  },
  fileFilter: function (req, file, cb) {
    // Allow images and documents
    if (file.mimetype.startsWith('image/') ||
        file.mimetype === 'application/pdf' ||
        file.mimetype.startsWith('application/')) {
      cb(null, true);
    } else {
      cb(new Error('Invalid file type'), false);
    }
  }
});

// Public routes
router.get('/store/:slug', getStorefront);
router.get('/storefront/:id', getStorefront); // Alias for storefront by ID

// Protected routes (require authentication)
router.use(protect);

// Vendor registration and onboarding
router.post('/register', registerVendor);
router.post('/kyc', upload.fields([
  { name: 'idDocument', maxCount: 1 },
  { name: 'addressProof', maxCount: 1 },
  { name: 'businessLicense', maxCount: 1 },
  { name: 'taxDocument', maxCount: 1 }
]), submitKYC);
router.post('/payout', setupPayout);
router.put('/store', upload.fields([
  { name: 'logo', maxCount: 1 },
  { name: 'banner', maxCount: 1 }
]), setupStore);
router.post('/complete-onboarding', completeOnboarding);

// Vendor-only routes (require vendor role or admin)
// Note: Some routes like dashboard need special handling for new vendors
router.use((req, res, next) => {
  // Allow access if user is admin or vendor, or if accessing certain onboarding routes
  if (req.user.role === 'admin' || req.user.role === 'vendor') {
    return next();
  }

  // For dashboard and profile routes, check if user has a vendor account
  if (req.path === '/dashboard' || req.path === '/profile') {
    return next(); // Let the controller handle vendor existence check
  }

  return res.status(403).json({
    success: false,
    message: 'Access denied. Vendor account required.'
  });
});

// Dashboard and profile
router.get('/dashboard', getDashboard);
router.get('/dashboard/stats', getDashboard); // Alias for dashboard stats
router.get('/profile', getProfile);
router.put('/profile', updateProfile);

// Order management
router.get('/orders', getOrders);
router.put('/orders/:id/status', updateOrderStatus);

// Reports and analytics
router.get('/reports', getSalesReports);

// Review management
router.get('/reviews', getReviews);
router.post('/reviews/:id/respond', respondToReview);

// Follow/Unfollow vendors (public endpoints)
router.post('/follow/:id', protect, followVendor);
router.delete('/unfollow/:id', protect, unfollowVendor);

module.exports = router;
