const AREffect = require('../models/AREffect');
const Post = require('../models/Post');
const Reel = require('../models/Reel');
const Story = require('../models/Story');
const { createError } = require('../utils/error');

/**
 * Get all AR effects
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.getEffects = async (req, res, next) => {
  try {
    const { type, category, search, limit = 20, page = 1 } = req.query;
    const skip = (page - 1) * limit;

    // Build query
    const query = { isPublic: true };

    if (type) {
      query.type = type;
    }

    if (category) {
      query.category = category;
    }

    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { tags: { $regex: search, $options: 'i' } },
      ];
    }

    // Get effects
    const [effects, total] = await Promise.all([
      AREffect.find(query)
        .sort({ usageCount: -1, createdAt: -1 })
        .skip(skip)
        .limit(parseInt(limit))
        .populate('creator', 'username name profilePicture')
        .populate('relatedEmotions', 'name color icon category'),

      AREffect.countDocuments(query),
    ]);

    res.status(200).json({
      success: true,
      count: effects.length,
      total,
      data: effects,
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Get a single AR effect
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.getEffect = async (req, res, next) => {
  try {
    const effect = await AREffect.findById(req.params.id)
      .populate('creator', 'username name profilePicture')
      .populate('relatedEmotions', 'name color icon category');

    if (!effect) {
      return next(createError(404, 'AR effect not found'));
    }

    res.status(200).json({
      success: true,
      data: effect,
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Create a new AR effect
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.createEffect = async (req, res, next) => {
  try {
    const { name, description, type, category, thumbnail, assetUrl, tags, relatedEmotions, isPublic } = req.body;

    // Create effect
    const effect = await AREffect.create({
      name,
      description,
      type,
      category,
      thumbnail,
      assetUrl,
      creator: req.user.id,
      isPublic: isPublic !== undefined ? isPublic : true,
      tags: tags || [],
      relatedEmotions: relatedEmotions || [],
    });

    res.status(201).json({
      success: true,
      data: effect,
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Update an AR effect
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.updateEffect = async (req, res, next) => {
  try {
    const { name, description, type, category, thumbnail, assetUrl, tags, relatedEmotions, isPublic } = req.body;

    // Find effect
    let effect = await AREffect.findById(req.params.id);

    if (!effect) {
      return next(createError(404, 'AR effect not found'));
    }

    // Check ownership
    if (effect.creator.toString() !== req.user.id && req.user.role !== 'admin') {
      return next(createError(403, 'Not authorized to update this effect'));
    }

    // Update effect
    effect = await AREffect.findByIdAndUpdate(
      req.params.id,
      {
        name,
        description,
        type,
        category,
        thumbnail,
        assetUrl,
        isPublic,
        tags,
        relatedEmotions,
      },
      { new: true, runValidators: true }
    );

    res.status(200).json({
      success: true,
      data: effect,
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Delete an AR effect
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.deleteEffect = async (req, res, next) => {
  try {
    // Find effect
    const effect = await AREffect.findById(req.params.id);

    if (!effect) {
      return next(createError(404, 'AR effect not found'));
    }

    // Check ownership
    if (effect.creator.toString() !== req.user.id && req.user.role !== 'admin') {
      return next(createError(403, 'Not authorized to delete this effect'));
    }

    await effect.remove();

    res.status(200).json({
      success: true,
      data: {},
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Increment usage count for an AR effect
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.incrementUsage = async (req, res, next) => {
  try {
    const effect = await AREffect.findByIdAndUpdate(
      req.params.id,
      { $inc: { usageCount: 1 } },
      { new: true }
    );

    if (!effect) {
      return next(createError(404, 'AR effect not found'));
    }

    res.status(200).json({
      success: true,
      data: effect,
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Get trending AR effects
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.getTrendingEffects = async (req, res, next) => {
  try {
    const { limit = 10, timeFrame = 'week' } = req.query;

    // Build time filter based on timeFrame
    const timeFilter = { isPublic: true };
    if (timeFrame !== 'all') {
      const now = new Date();
      let startDate;

      switch (timeFrame) {
        case 'day':
          startDate = new Date(now.setDate(now.getDate() - 1));
          break;
        case 'week':
          startDate = new Date(now.setDate(now.getDate() - 7));
          break;
        case 'month':
          startDate = new Date(now.setMonth(now.getMonth() - 1));
          break;
        default:
          startDate = new Date(now.setDate(now.getDate() - 7)); // Default to week
      }

      timeFilter.createdAt = { $gte: startDate };
    }

    // Get trending effects based on usage count
    const effects = await AREffect.find(timeFilter)
      .sort({ usageCount: -1, createdAt: -1 })
      .limit(parseInt(limit))
      .populate('creator', 'username name profilePicture')
      .populate('relatedEmotions', 'name color icon category');

    res.status(200).json({
      success: true,
      count: effects.length,
      data: effects,
    });
  } catch (err) {
    next(err);
  }
};
