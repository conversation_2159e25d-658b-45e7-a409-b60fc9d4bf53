const mongoose = require('mongoose');

const ComplianceSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.ObjectId,
    ref: 'User',
    required: true,
    unique: true
  },
  gdpr: {
    consentGiven: {
      type: Boolean,
      default: false
    },
    consentDate: Date,
    consentVersion: String,
    consentMethod: {
      type: String,
      enum: ['registration', 'explicit', 'updated_terms', 'cookie_banner']
    },
    dataProcessingPurposes: [{
      purpose: {
        type: String,
        enum: ['account_management', 'order_processing', 'marketing', 'analytics', 'customer_support', 'legal_compliance']
      },
      consented: Boolean,
      consentDate: Date
    }],
    rightToBeForgotten: {
      requested: {
        type: Boolean,
        default: false
      },
      requestDate: Date,
      processedDate: Date,
      status: {
        type: String,
        enum: ['pending', 'processing', 'completed', 'rejected']
      },
      rejectionReason: String
    },
    dataPortability: {
      requested: {
        type: Boolean,
        default: false
      },
      requestDate: Date,
      exportedDate: Date,
      downloadUrl: String,
      expiryDate: Date
    },
    dataRetention: {
      retentionPeriod: {
        type: Number, // in days
        default: 2555 // 7 years
      },
      retentionUntil: Date,
      autoDeleteScheduled: {
        type: Boolean,
        default: false
      },
      legalHoldActive: {
        type: Boolean,
        default: false
      }
    },
    breachNotifications: [{
      breachId: String,
      notifiedAt: Date,
      breachType: String,
      affectedData: [String],
      mitigationSteps: String
    }],
    cookieConsent: {
      essential: {
        type: Boolean,
        default: true
      },
      functional: Boolean,
      analytics: Boolean,
      marketing: Boolean,
      consentDate: Date,
      lastUpdated: Date
    }
  },
  pciDss: {
    isApplicable: {
      type: Boolean,
      default: false
    },
    complianceLevel: {
      type: String,
      enum: ['level_1', 'level_2', 'level_3', 'level_4', 'not_applicable'],
      default: 'not_applicable'
    },
    lastAssessment: Date,
    nextAssessment: Date,
    assessmentStatus: {
      type: String,
      enum: ['compliant', 'non_compliant', 'pending', 'not_assessed'],
      default: 'not_assessed'
    },
    cardDataHandling: {
      storesCardData: {
        type: Boolean,
        default: false
      },
      transmitsCardData: {
        type: Boolean,
        default: false
      },
      processesCardData: {
        type: Boolean,
        default: false
      },
      encryptionMethod: String,
      tokenizationUsed: {
        type: Boolean,
        default: false
      }
    },
    securityMeasures: {
      firewallConfigured: Boolean,
      defaultPasswordsChanged: Boolean,
      dataEncrypted: Boolean,
      antivirusUpdated: Boolean,
      accessControlImplemented: Boolean,
      networkMonitored: Boolean,
      vulnerabilityManaged: Boolean,
      accessTested: Boolean,
      physicalAccessRestricted: Boolean,
      networkAccessMonitored: Boolean,
      securityPoliciesMaintained: Boolean,
      informationSecurityProgram: Boolean
    },
    violations: [{
      violationType: String,
      discoveredAt: Date,
      resolvedAt: Date,
      description: String,
      remediation: String,
      reportedToAuthorities: Boolean
    }]
  },
  kyc: {
    status: {
      type: String,
      enum: ['not_started', 'in_progress', 'pending_review', 'verified', 'rejected', 'expired'],
      default: 'not_started'
    },
    level: {
      type: String,
      enum: ['basic', 'enhanced', 'premium'],
      default: 'basic'
    },
    verificationDate: Date,
    expiryDate: Date,
    documents: [{
      type: {
        type: String,
        enum: ['passport', 'drivers_license', 'national_id', 'utility_bill', 'bank_statement', 'tax_document', 'business_license']
      },
      documentId: String,
      uploadedAt: Date,
      verifiedAt: Date,
      status: {
        type: String,
        enum: ['pending', 'verified', 'rejected'],
        default: 'pending'
      },
      rejectionReason: String,
      expiryDate: Date
    }],
    personalInfo: {
      fullName: String,
      dateOfBirth: Date,
      nationality: String,
      address: {
        street: String,
        city: String,
        state: String,
        postalCode: String,
        country: String
      },
      verifiedAt: Date
    },
    biometricData: {
      faceVerification: {
        completed: Boolean,
        verifiedAt: Date,
        confidence: Number
      },
      livenessCheck: {
        completed: Boolean,
        verifiedAt: Date
      }
    },
    riskAssessment: {
      riskLevel: {
        type: String,
        enum: ['low', 'medium', 'high'],
        default: 'low'
      },
      factors: [String],
      lastAssessed: Date,
      sanctions: {
        checked: Boolean,
        checkedAt: Date,
        found: Boolean,
        details: String
      },
      pep: { // Politically Exposed Person
        checked: Boolean,
        checkedAt: Date,
        found: Boolean,
        details: String
      }
    },
    verificationProvider: {
      name: String, // Jumio, Onfido, etc.
      transactionId: String,
      confidence: Number
    }
  },
  aml: { // Anti-Money Laundering
    riskProfile: {
      type: String,
      enum: ['low', 'medium', 'high'],
      default: 'low'
    },
    transactionMonitoring: {
      enabled: {
        type: Boolean,
        default: true
      },
      thresholds: {
        daily: Number,
        monthly: Number,
        suspicious: Number
      },
      alerts: [{
        alertId: String,
        triggeredAt: Date,
        reason: String,
        resolved: Boolean,
        resolvedAt: Date,
        action: String
      }]
    },
    suspiciousActivity: [{
      reportId: String,
      reportedAt: Date,
      description: String,
      amount: Number,
      status: {
        type: String,
        enum: ['reported', 'investigating', 'cleared', 'escalated']
      },
      reportedToAuthorities: Boolean
    }]
  },
  audit: {
    lastAudit: Date,
    nextAudit: Date,
    auditTrail: [{
      action: String,
      performedBy: {
        type: mongoose.Schema.ObjectId,
        ref: 'User'
      },
      timestamp: {
        type: Date,
        default: Date.now
      },
      ipAddress: String,
      userAgent: String,
      details: mongoose.Schema.Types.Mixed
    }]
  },
  certifications: [{
    type: {
      type: String,
      enum: ['iso_27001', 'soc_2', 'gdpr_certified', 'pci_dss', 'hipaa', 'other']
    },
    issuer: String,
    issuedDate: Date,
    expiryDate: Date,
    certificateUrl: String,
    status: {
      type: String,
      enum: ['active', 'expired', 'suspended', 'revoked'],
      default: 'active'
    }
  }],
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Update the updatedAt field before saving
ComplianceSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

// Add audit trail entry
ComplianceSchema.methods.addAuditEntry = function(action, performedBy, ipAddress, userAgent, details = {}) {
  this.audit.auditTrail.push({
    action,
    performedBy,
    ipAddress,
    userAgent,
    details
  });
  return this.save();
};

// Give GDPR consent
ComplianceSchema.methods.giveGDPRConsent = function(purposes = [], method = 'explicit', version = '1.0') {
  this.gdpr.consentGiven = true;
  this.gdpr.consentDate = new Date();
  this.gdpr.consentVersion = version;
  this.gdpr.consentMethod = method;
  
  purposes.forEach(purpose => {
    this.gdpr.dataProcessingPurposes.push({
      purpose,
      consented: true,
      consentDate: new Date()
    });
  });
  
  return this.save();
};

// Request data deletion (Right to be Forgotten)
ComplianceSchema.methods.requestDataDeletion = function() {
  this.gdpr.rightToBeForgotten.requested = true;
  this.gdpr.rightToBeForgotten.requestDate = new Date();
  this.gdpr.rightToBeForgotten.status = 'pending';
  return this.save();
};

// Request data export (Data Portability)
ComplianceSchema.methods.requestDataExport = function() {
  this.gdpr.dataPortability.requested = true;
  this.gdpr.dataPortability.requestDate = new Date();
  return this.save();
};

// Update KYC status
ComplianceSchema.methods.updateKYCStatus = function(status, level = null) {
  this.kyc.status = status;
  if (level) this.kyc.level = level;
  
  if (status === 'verified') {
    this.kyc.verificationDate = new Date();
    this.kyc.expiryDate = new Date(Date.now() + 365 * 24 * 60 * 60 * 1000); // 1 year
  }
  
  return this.save();
};

// Add KYC document
ComplianceSchema.methods.addKYCDocument = function(type, documentId) {
  this.kyc.documents.push({
    type,
    documentId,
    uploadedAt: new Date(),
    status: 'pending'
  });
  
  if (this.kyc.status === 'not_started') {
    this.kyc.status = 'in_progress';
  }
  
  return this.save();
};

// Check compliance status
ComplianceSchema.methods.getComplianceStatus = function() {
  return {
    gdpr: {
      compliant: this.gdpr.consentGiven,
      consentDate: this.gdpr.consentDate
    },
    kyc: {
      compliant: this.kyc.status === 'verified',
      level: this.kyc.level,
      expiryDate: this.kyc.expiryDate
    },
    pciDss: {
      compliant: this.pciDss.assessmentStatus === 'compliant',
      applicable: this.pciDss.isApplicable
    },
    aml: {
      riskLevel: this.aml.riskProfile,
      monitored: this.aml.transactionMonitoring.enabled
    }
  };
};

// Check if KYC is expired
ComplianceSchema.methods.isKYCExpired = function() {
  return this.kyc.expiryDate && new Date() > this.kyc.expiryDate;
};

// Create indexes for better performance
ComplianceSchema.index({ user: 1 });
ComplianceSchema.index({ 'kyc.status': 1 });
ComplianceSchema.index({ 'kyc.expiryDate': 1 });
ComplianceSchema.index({ 'gdpr.consentGiven': 1 });
ComplianceSchema.index({ 'pciDss.assessmentStatus': 1 });
ComplianceSchema.index({ 'aml.riskProfile': 1 });
ComplianceSchema.index({ createdAt: -1 });

module.exports = mongoose.model('Compliance', ComplianceSchema);
