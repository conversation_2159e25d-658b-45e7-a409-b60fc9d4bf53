const User = require('../models/User');
const CoinTransaction = require('../models/CoinTransaction');
const asyncHandler = require('../middleware/async');
const ErrorResponse = require('../utils/errorResponse');

/**
 * Get user's coin balance
 * @route GET /api/users/me/coins
 * @access Private
 */
exports.getUserCoins = asyncHandler(async (req, res, next) => {
  const user = await User.findById(req.user.id);

  if (!user) {
    return next(new ErrorResponse('User not found', 404));
  }

  res.status(200).json({
    success: true,
    data: {
      coins: user.coins || 0
    }
  });
});

/**
 * Purchase coins
 * @route POST /api/users/me/coins/purchase
 * @access Private
 */
exports.purchaseCoins = asyncHandler(async (req, res, next) => {
  const { amount, paymentMethod, paymentDetails } = req.body;

  if (!amount || amount <= 0) {
    return next(new ErrorResponse('Please provide a valid amount', 400));
  }

  // In a real implementation, you would process the payment here
  // For now, we'll just add the coins to the user's account

  // Update user's coin balance
  const user = await User.findByIdAndUpdate(
    req.user.id,
    { $inc: { coins: amount } },
    { new: true }
  );

  // Create transaction record
  const transaction = await CoinTransaction.create({
    user: req.user.id,
    type: 'purchase',
    amount,
    paymentMethod,
    paymentDetails,
    status: 'completed'
  });

  res.status(200).json({
    success: true,
    data: {
      transaction,
      newBalance: user.coins
    }
  });
});

/**
 * Spend coins
 * @route POST /api/users/me/coins/spend
 * @access Private
 */
exports.spendCoins = asyncHandler(async (req, res, next) => {
  const { amount, reason, itemId, itemType } = req.body;

  if (!amount || amount <= 0) {
    return next(new ErrorResponse('Please provide a valid amount', 400));
  }

  // Get user
  const user = await User.findById(req.user.id);

  // Check if user has enough coins
  if (user.coins < amount) {
    return next(new ErrorResponse('Insufficient coins', 400));
  }

  // Update user's coin balance
  const updatedUser = await User.findByIdAndUpdate(
    req.user.id,
    { $inc: { coins: -amount } },
    { new: true }
  );

  // Create transaction record
  const transaction = await CoinTransaction.create({
    user: req.user.id,
    type: 'spend',
    amount: -amount,
    reason,
    itemId,
    itemType,
    status: 'completed'
  });

  res.status(200).json({
    success: true,
    data: {
      transaction,
      newBalance: updatedUser.coins
    }
  });
});

/**
 * Get coin transaction history
 * @route GET /api/users/me/coins/transactions
 * @access Private
 */
exports.getCoinTransactions = asyncHandler(async (req, res, next) => {
  const { page = 1, limit = 10, type } = req.query;
  const skip = (page - 1) * limit;

  // Build query
  let query = { user: req.user.id };
  
  // Add type filter if provided
  if (type) {
    query.type = type;
  }

  // Execute query
  const transactions = await CoinTransaction.find(query)
    .sort({ createdAt: -1 })
    .skip(skip)
    .limit(parseInt(limit));

  // Get total count
  const total = await CoinTransaction.countDocuments(query);

  res.status(200).json({
    success: true,
    count: transactions.length,
    total,
    pagination: {
      page: parseInt(page),
      limit: parseInt(limit),
      totalPages: Math.ceil(total / limit),
    },
    data: transactions,
  });
});
