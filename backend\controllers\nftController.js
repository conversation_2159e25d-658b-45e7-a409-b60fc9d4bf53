const NFT = require('../models/NFT');
const User = require('../models/User');
const LiveStream = require('../models/LiveStream');
const asyncHandler = require('../middleware/async');
const ErrorResponse = require('../utils/errorResponse');

/**
 * Get all NFTs
 * @route GET /api/nfts
 * @access Private
 */
exports.getNFTs = asyncHandler(async (req, res, next) => {
  const { page = 1, limit = 10, category, creator } = req.query;
  const skip = (page - 1) * limit;

  // Build query
  let query = {};
  
  // Add category filter if provided
  if (category) {
    query.category = category;
  }

  // Add creator filter if provided
  if (creator) {
    query.creator = creator;
  }

  // Execute query
  const nfts = await NFT.find(query)
    .sort({ createdAt: -1 })
    .skip(skip)
    .limit(parseInt(limit))
    .populate('creator', 'username name profilePicture isVerified')
    .populate('owner', 'username name profilePicture isVerified');

  // Get total count
  const total = await NFT.countDocuments(query);

  res.status(200).json({
    success: true,
    count: nfts.length,
    total,
    pagination: {
      page: parseInt(page),
      limit: parseInt(limit),
      totalPages: Math.ceil(total / limit),
    },
    data: nfts,
  });
});

/**
 * Get a single NFT
 * @route GET /api/nfts/:id
 * @access Private
 */
exports.getNFT = asyncHandler(async (req, res, next) => {
  const nft = await NFT.findById(req.params.id)
    .populate('creator', 'username name profilePicture isVerified')
    .populate('owner', 'username name profilePicture isVerified')
    .populate('stream', 'title thumbnail');

  if (!nft) {
    return next(new ErrorResponse(`NFT not found with id of ${req.params.id}`, 404));
  }

  res.status(200).json({
    success: true,
    data: nft,
  });
});

/**
 * Mint a new NFT
 * @route POST /api/nfts/mint
 * @access Private
 */
exports.mintNFT = asyncHandler(async (req, res, next) => {
  const { name, description, image, category, streamId, price, supply, royaltyPercentage } = req.body;

  // Check if stream exists if streamId is provided
  if (streamId) {
    const stream = await LiveStream.findById(streamId);
    if (!stream) {
      return next(new ErrorResponse(`Live stream not found with id of ${streamId}`, 404));
    }

    // Check if user is the stream owner
    if (stream.user.toString() !== req.user.id) {
      return next(new ErrorResponse('Not authorized to mint NFTs for this stream', 403));
    }
  }

  // Create NFT
  const nft = await NFT.create({
    name,
    description,
    image,
    category,
    stream: streamId,
    creator: req.user.id,
    owner: req.user.id,
    price,
    supply,
    royaltyPercentage,
  });

  // Populate creator and owner
  await nft.populate('creator', 'username name profilePicture isVerified');
  await nft.populate('owner', 'username name profilePicture isVerified');

  res.status(201).json({
    success: true,
    data: nft,
  });
});

/**
 * Transfer an NFT to another user
 * @route POST /api/nfts/:id/transfer
 * @access Private
 */
exports.transferNFT = asyncHandler(async (req, res, next) => {
  const { recipientId } = req.body;

  // Check if NFT exists
  const nft = await NFT.findById(req.params.id);
  if (!nft) {
    return next(new ErrorResponse(`NFT not found with id of ${req.params.id}`, 404));
  }

  // Check if user is the NFT owner
  if (nft.owner.toString() !== req.user.id) {
    return next(new ErrorResponse('Not authorized to transfer this NFT', 403));
  }

  // Check if recipient exists
  const recipient = await User.findById(recipientId);
  if (!recipient) {
    return next(new ErrorResponse(`Recipient not found with id of ${recipientId}`, 404));
  }

  // Update NFT owner
  nft.owner = recipientId;
  nft.transferHistory.push({
    from: req.user.id,
    to: recipientId,
    timestamp: Date.now(),
  });
  await nft.save();

  // Populate creator and owner
  await nft.populate('creator', 'username name profilePicture isVerified');
  await nft.populate('owner', 'username name profilePicture isVerified');

  res.status(200).json({
    success: true,
    data: nft,
  });
});

/**
 * Get NFTs owned by a user
 * @route GET /api/users/:userId/nfts
 * @access Public
 */
exports.getUserNFTs = asyncHandler(async (req, res, next) => {
  const { userId } = req.params;
  const { page = 1, limit = 10, category } = req.query;
  const skip = (page - 1) * limit;

  // Check if user exists
  const user = await User.findById(userId);
  if (!user) {
    return next(new ErrorResponse(`User not found with id of ${userId}`, 404));
  }

  // Build query
  let query = { owner: userId };
  
  // Add category filter if provided
  if (category) {
    query.category = category;
  }

  // Execute query
  const nfts = await NFT.find(query)
    .sort({ createdAt: -1 })
    .skip(skip)
    .limit(parseInt(limit))
    .populate('creator', 'username name profilePicture isVerified')
    .populate('stream', 'title thumbnail');

  // Get total count
  const total = await NFT.countDocuments(query);

  res.status(200).json({
    success: true,
    count: nfts.length,
    total,
    pagination: {
      page: parseInt(page),
      limit: parseInt(limit),
      totalPages: Math.ceil(total / limit),
    },
    data: nfts,
  });
});

/**
 * Get NFTs related to a stream
 * @route GET /api/live-streams/:streamId/nfts
 * @access Public
 */
exports.getStreamNFTs = asyncHandler(async (req, res, next) => {
  const { streamId } = req.params;
  const { page = 1, limit = 10 } = req.query;
  const skip = (page - 1) * limit;

  // Check if stream exists
  const stream = await LiveStream.findById(streamId);
  if (!stream) {
    return next(new ErrorResponse(`Live stream not found with id of ${streamId}`, 404));
  }

  // Execute query
  const nfts = await NFT.find({ stream: streamId })
    .sort({ createdAt: -1 })
    .skip(skip)
    .limit(parseInt(limit))
    .populate('creator', 'username name profilePicture isVerified')
    .populate('owner', 'username name profilePicture isVerified');

  // Get total count
  const total = await NFT.countDocuments({ stream: streamId });

  res.status(200).json({
    success: true,
    count: nfts.length,
    total,
    pagination: {
      page: parseInt(page),
      limit: parseInt(limit),
      totalPages: Math.ceil(total / limit),
    },
    data: nfts,
  });
});
