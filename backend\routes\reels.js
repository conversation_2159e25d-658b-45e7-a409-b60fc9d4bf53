const express = require('express');
const router = express.Router();
const { protect, optionalAuth } = require('../middleware/auth');
const {
  getReels,
  getReel,
  createReel,
  updateReel,
  deleteReel,
  getUserReels,
  likeReel,
  unlikeReel,
  getReelLikes,
  addComment,
  getReelComments,
  deleteComment,
  getTrendingReels,
  viewReel,
  getRelatedReels,
  getFeedReels,
} = require('../controllers/reelController');

// Public routes
router.get('/', getReels);
router.get('/trending', getTrendingReels);
router.get('/feed', optionalAuth, getFeedReels);
router.get('/user/:userId', getUserReels);
router.get('/related/:id', getRelatedReels);
router.get('/:id', getReel);
router.get('/:id/likes', getReelLikes);
router.get('/:id/comments', getReelComments);

// Protected routes
router.use(protect);
router.post('/', createReel);
router.put('/:id', updateReel);
router.delete('/:id', deleteReel);
router.post('/:id/like', likeReel);
router.delete('/:id/like', unlikeReel);
router.post('/:id/view', viewReel);
router.post('/:id/comments', addComment);
router.delete('/:id/comments/:commentId', deleteComment);

module.exports = router;
