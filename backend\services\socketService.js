const socketIo = require('socket.io');
const jwt = require('jsonwebtoken');
const User = require('../models/User');

class SocketService {
  constructor() {
    this.io = null;
    this.connectedUsers = new Map(); // userId -> socketId mapping
    this.userSockets = new Map(); // socketId -> user data mapping
  }

  // Initialize Socket.IO server
  initialize(server) {
    this.io = socketIo(server, {
      cors: {
        origin: process.env.FRONTEND_URL || "http://localhost:50000",
        methods: ["GET", "POST"],
        credentials: true
      },
      transports: ['websocket', 'polling']
    });

    this.setupMiddleware();
    this.setupEventHandlers();
    
    console.log('✅ Socket.IO service initialized');
    return this.io;
  }

  // Setup authentication middleware
  setupMiddleware() {
    this.io.use(async (socket, next) => {
      try {
        const token = socket.handshake.auth.token || socket.handshake.headers.authorization?.replace('Bearer ', '');
        
        if (!token) {
          return next(new Error('Authentication error: No token provided'));
        }

        const decoded = jwt.verify(token, process.env.JWT_SECRET);
        const user = await User.findById(decoded.id).select('-password');
        
        if (!user) {
          return next(new Error('Authentication error: User not found'));
        }

        socket.userId = user._id.toString();
        socket.user = user;
        next();
      } catch (error) {
        console.error('Socket authentication error:', error);
        next(new Error('Authentication error: Invalid token'));
      }
    });
  }

  // Setup event handlers
  setupEventHandlers() {
    this.io.on('connection', (socket) => {
      this.handleConnection(socket);
      this.handleDisconnection(socket);
      this.handleCustomEvents(socket);
    });
  }

  // Handle new connection
  handleConnection(socket) {
    const userId = socket.userId;
    
    // Store user connection
    this.connectedUsers.set(userId, socket.id);
    this.userSockets.set(socket.id, {
      userId,
      user: socket.user,
      connectedAt: new Date()
    });

    // Join user-specific room
    socket.join(`user_${userId}`);
    
    // Join role-specific rooms
    if (socket.user.role === 'admin') {
      socket.join('admin_room');
    }
    if (socket.user.role === 'vendor') {
      socket.join('vendor_room');
    }

    console.log(`✅ User ${socket.user.name} connected (${socket.id})`);

    // Emit connection success
    socket.emit('connected', {
      message: 'Connected successfully',
      userId,
      timestamp: new Date()
    });

    // Broadcast user online status to friends/followers
    this.broadcastUserStatus(userId, 'online');
  }

  // Handle disconnection
  handleDisconnection(socket) {
    socket.on('disconnect', (reason) => {
      const userId = socket.userId;
      
      // Remove user connection
      this.connectedUsers.delete(userId);
      this.userSockets.delete(socket.id);

      console.log(`❌ User ${socket.user?.name} disconnected (${reason})`);

      // Broadcast user offline status
      this.broadcastUserStatus(userId, 'offline');
    });
  }

  // Handle custom events
  handleCustomEvents(socket) {
    // Order notifications
    socket.on('order_placed', (data) => {
      this.handleOrderPlaced(socket, data);
    });

    // Message notifications
    socket.on('message_sent', (data) => {
      this.handleMessageSent(socket, data);
    });

    // Live stream events
    socket.on('join_stream', (data) => {
      this.handleJoinStream(socket, data);
    });

    socket.on('leave_stream', (data) => {
      this.handleLeaveStream(socket, data);
    });

    // Vendor events
    socket.on('vendor_status_update', (data) => {
      this.handleVendorStatusUpdate(socket, data);
    });

    // Admin events
    socket.on('admin_broadcast', (data) => {
      this.handleAdminBroadcast(socket, data);
    });
  }

  // Handle order placed event
  handleOrderPlaced(socket, data) {
    const { orderId, vendorId, amount } = data;
    
    // Notify vendor
    this.emit(`user_${vendorId}`, 'new_order', {
      orderId,
      amount,
      buyerId: socket.userId,
      timestamp: new Date()
    });

    // Notify admins
    this.emitToRoom('admin_room', 'new_order_admin', {
      orderId,
      vendorId,
      amount,
      buyerId: socket.userId,
      timestamp: new Date()
    });
  }

  // Handle message sent event
  handleMessageSent(socket, data) {
    const { recipientId, messageId, content } = data;
    
    // Notify recipient
    this.emit(`user_${recipientId}`, 'new_message', {
      messageId,
      senderId: socket.userId,
      senderName: socket.user.name,
      content,
      timestamp: new Date()
    });
  }

  // Handle join stream event
  handleJoinStream(socket, data) {
    const { streamId } = data;
    
    socket.join(`stream_${streamId}`);
    
    // Notify other viewers
    socket.to(`stream_${streamId}`).emit('viewer_joined', {
      userId: socket.userId,
      userName: socket.user.name,
      timestamp: new Date()
    });
  }

  // Handle leave stream event
  handleLeaveStream(socket, data) {
    const { streamId } = data;
    
    socket.leave(`stream_${streamId}`);
    
    // Notify other viewers
    socket.to(`stream_${streamId}`).emit('viewer_left', {
      userId: socket.userId,
      userName: socket.user.name,
      timestamp: new Date()
    });
  }

  // Handle vendor status update
  handleVendorStatusUpdate(socket, data) {
    if (socket.user.role !== 'admin') return;
    
    const { vendorId, status, reason } = data;
    
    // Notify vendor
    this.emit(`user_${vendorId}`, 'vendor_status_changed', {
      status,
      reason,
      updatedBy: socket.userId,
      timestamp: new Date()
    });
  }

  // Handle admin broadcast
  handleAdminBroadcast(socket, data) {
    if (socket.user.role !== 'admin') return;
    
    const { message, type, targetRole } = data;
    
    if (targetRole) {
      this.emitToRoom(`${targetRole}_room`, 'admin_announcement', {
        message,
        type,
        from: socket.user.name,
        timestamp: new Date()
      });
    } else {
      this.broadcast('admin_announcement', {
        message,
        type,
        from: socket.user.name,
        timestamp: new Date()
      });
    }
  }

  // Broadcast user status
  broadcastUserStatus(userId, status) {
    // This would typically notify friends/followers
    // For now, just emit to admin room
    this.emitToRoom('admin_room', 'user_status_changed', {
      userId,
      status,
      timestamp: new Date()
    });
  }

  // Emit to specific user
  emit(room, event, data) {
    if (this.io) {
      this.io.to(room).emit(event, data);
    }
  }

  // Emit to specific room
  emitToRoom(room, event, data) {
    if (this.io) {
      this.io.to(room).emit(event, data);
    }
  }

  // Broadcast to all connected users
  broadcast(event, data) {
    if (this.io) {
      this.io.emit(event, data);
    }
  }

  // Get connected users count
  getConnectedUsersCount() {
    return this.connectedUsers.size;
  }

  // Get connected users in room
  getConnectedUsersInRoom(room) {
    if (!this.io) return [];
    
    const roomSockets = this.io.sockets.adapter.rooms.get(room);
    if (!roomSockets) return [];
    
    return Array.from(roomSockets).map(socketId => {
      const userData = this.userSockets.get(socketId);
      return userData ? userData.user : null;
    }).filter(Boolean);
  }

  // Check if user is online
  isUserOnline(userId) {
    return this.connectedUsers.has(userId);
  }

  // Get user socket
  getUserSocket(userId) {
    const socketId = this.connectedUsers.get(userId);
    return socketId ? this.io.sockets.sockets.get(socketId) : null;
  }

  // Emit notification to user
  emitNotification(userId, notification) {
    this.emit(`user_${userId}`, 'notification', {
      ...notification,
      timestamp: new Date()
    });
  }

  // Emit order update
  emitOrderUpdate(orderId, update) {
    this.broadcast('order_update', {
      orderId,
      ...update,
      timestamp: new Date()
    });
  }

  // Emit payment update
  emitPaymentUpdate(userId, paymentData) {
    this.emit(`user_${userId}`, 'payment_update', {
      ...paymentData,
      timestamp: new Date()
    });
  }

  // Emit payout update
  emitPayoutUpdate(vendorId, payoutData) {
    this.emit(`user_${vendorId}`, 'payout_update', {
      ...payoutData,
      timestamp: new Date()
    });
  }
}

// Create singleton instance
const socketService = new SocketService();

module.exports = socketService;
