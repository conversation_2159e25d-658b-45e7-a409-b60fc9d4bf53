const mongoose = require('mongoose');

const StreamReminderSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  stream: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'LiveStream',
    required: true
  },
  reminderTime: {
    type: Date,
    required: true
  },
  notified: {
    type: Boolean,
    default: false
  },
  rsvpStatus: {
    type: String,
    enum: ['going', 'interested', 'not_going'],
    default: 'going'
  },
  addedToCalendar: {
    type: Boolean,
    default: false
  },
  calendarProvider: {
    type: String,
    enum: ['google', 'apple', 'outlook', 'other', null],
    default: null
  },
  notificationSettings: {
    email: {
      type: Boolean,
      default: true
    },
    push: {
      type: Boolean,
      default: true
    },
    inApp: {
      type: Boolean,
      default: true
    }
  },
  createdAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

// Create compound index to ensure a user can only have one reminder per stream
StreamReminderSchema.index({ user: 1, stream: 1 }, { unique: true });

// Create index for finding reminders that need to be sent
StreamReminderSchema.index({ reminderTime: 1, notified: 1 });

module.exports = mongoose.model('StreamReminder', StreamReminderSchema);
