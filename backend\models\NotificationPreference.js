const mongoose = require('mongoose');

const NotificationPreferenceSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    unique: true
  },
  liveStream: {
    followedCreatorStart: {
      type: Boolean,
      default: true
    },
    scheduledStreamStart: {
      type: Boolean,
      default: true
    },
    trendingInInterests: {
      type: Boolean,
      default: true
    },
    localStreamsStart: {
      type: Boolean,
      default: true
    },
    coHostInvitations: {
      type: Boolean,
      default: true
    },
    streamMentions: {
      type: Boolean,
      default: true
    }
  },
  content: {
    newFollowerContent: {
      type: Boolean,
      default: true
    },
    recommendedContent: {
      type: Boolean,
      default: true
    },
    contentMentions: {
      type: Boolean,
      default: true
    }
  },
  social: {
    newFollowers: {
      type: Boolean,
      default: true
    },
    messageRequests: {
      type: Boolean,
      default: true
    },
    commentReplies: {
      type: Boolean,
      default: true
    }
  },
  channels: {
    email: {
      type: Boolean,
      default: true
    },
    push: {
      type: Boolean,
      default: true
    },
    inApp: {
      type: Boolean,
      default: true
    },
    sms: {
      type: Boolean,
      default: false
    }
  },
  quietHours: {
    enabled: {
      type: Boolean,
      default: false
    },
    start: {
      type: String,
      default: '22:00' // 10 PM
    },
    end: {
      type: String,
      default: '08:00' // 8 AM
    },
    timezone: {
      type: String,
      default: 'UTC'
    }
  },
  locationBasedAlerts: {
    enabled: {
      type: Boolean,
      default: false
    },
    radius: {
      type: Number,
      default: 10 // in kilometers
    }
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

module.exports = mongoose.model('NotificationPreference', NotificationPreferenceSchema);
