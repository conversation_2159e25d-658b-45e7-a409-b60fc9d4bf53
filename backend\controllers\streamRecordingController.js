/**
 * Stream Recording Controller
 * Handles the creation, retrieval, and management of stream recordings
 */

const StreamRecording = require('../models/StreamRecording');
const LiveStream = require('../models/LiveStream');
const User = require('../models/User');
const { cloudinary } = require('../config/cloudinary');
const { createError } = require('../utils/error');
const fs = require('fs');
const path = require('path');
const { promisify } = require('util');
const writeFileAsync = promisify(fs.writeFile);
const unlinkAsync = promisify(fs.unlink);

/**
 * Create a new stream recording
 * @route POST /api/live-streams/recordings
 * @access Private
 */
exports.createStreamRecording = async (req, res, next) => {
  try {
    const { streamId, duration } = req.body;

    if (!streamId) {
      return next(createError(400, 'Stream ID is required'));
    }

    if (!req.file && !req.files?.recordingFile) {
      return next(createError(400, 'Recording file is required'));
    }

    // Get the recording file
    const recordingFile = req.file || req.files.recordingFile;

    // Check if the stream exists
    const stream = await LiveStream.findById(streamId);
    if (!stream) {
      return next(createError(404, 'Stream not found'));
    }

    // Check if the user is the owner of the stream
    if (stream.user.toString() !== req.user.id) {
      return next(createError(403, 'You are not authorized to create recordings for this stream'));
    }

    // Upload the recording to Cloudinary
    const result = await cloudinary.uploader.upload(recordingFile.path, {
      resource_type: 'video',
      folder: 'letstalk/recordings',
      public_id: `stream_${streamId}_${Date.now()}`,
      overwrite: true,
      resource_type: 'video'
    });

    // Create a new stream recording
    const streamRecording = new StreamRecording({
      stream: streamId,
      user: req.user.id,
      url: result.secure_url,
      publicId: result.public_id,
      duration: duration || 0,
      size: result.bytes,
      format: result.format,
      status: 'published'
    });

    // Save the stream recording
    await streamRecording.save();

    // Update the stream with the recording
    stream.recordings.push(streamRecording._id);
    await stream.save();

    // Remove the temporary file
    if (recordingFile.path) {
      await unlinkAsync(recordingFile.path);
    }

    res.status(201).json({
      success: true,
      message: 'Stream recording created successfully',
      data: streamRecording
    });
  } catch (err) {
    console.error('Error creating stream recording:', err);
    next(err);
  }
};

/**
 * Get all recordings for a stream
 * @route GET /api/live-streams/:streamId/recordings
 * @access Public
 */
exports.getStreamRecordings = async (req, res, next) => {
  try {
    const { streamId } = req.params;

    // Check if the stream exists
    const stream = await LiveStream.findById(streamId);
    if (!stream) {
      return next(createError(404, 'Stream not found'));
    }

    // Get all recordings for the stream
    const recordings = await StreamRecording.find({ stream: streamId, status: 'published' })
      .sort({ createdAt: -1 });

    res.status(200).json({
      success: true,
      count: recordings.length,
      data: recordings
    });
  } catch (err) {
    console.error('Error getting stream recordings:', err);
    next(err);
  }
};

/**
 * Get a single stream recording
 * @route GET /api/live-streams/recordings/:id
 * @access Public
 */
exports.getStreamRecording = async (req, res, next) => {
  try {
    const { id } = req.params;

    // Get the recording
    const recording = await StreamRecording.findById(id);
    if (!recording) {
      return next(createError(404, 'Recording not found'));
    }

    res.status(200).json({
      success: true,
      data: recording
    });
  } catch (err) {
    console.error('Error getting stream recording:', err);
    next(err);
  }
};

/**
 * Update a stream recording
 * @route PUT /api/live-streams/recordings/:id
 * @access Private
 */
exports.updateStreamRecording = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { title, description, visibility, thumbnail } = req.body;

    // Get the recording
    const recording = await StreamRecording.findById(id);
    if (!recording) {
      return next(createError(404, 'Recording not found'));
    }

    // Check if the user is the owner of the recording
    if (recording.user.toString() !== req.user.id) {
      return next(createError(403, 'You are not authorized to update this recording'));
    }

    // Update the recording
    if (title) recording.title = title;
    if (description) recording.description = description;
    if (visibility) recording.visibility = visibility;

    // Upload thumbnail if provided
    if (req.file && req.file.path) {
      // Upload the thumbnail to Cloudinary
      const result = await cloudinary.uploader.upload(req.file.path, {
        folder: 'letstalk/thumbnails',
        public_id: `recording_${id}_thumbnail_${Date.now()}`,
        overwrite: true
      });

      // Update the recording with the new thumbnail
      recording.thumbnail = {
        url: result.secure_url,
        publicId: result.public_id
      };

      // Remove the temporary file
      await unlinkAsync(req.file.path);
    }

    // Save the updated recording
    await recording.save();

    res.status(200).json({
      success: true,
      message: 'Recording updated successfully',
      data: recording
    });
  } catch (err) {
    console.error('Error updating stream recording:', err);
    next(err);
  }
};

/**
 * Delete a stream recording
 * @route DELETE /api/live-streams/recordings/:id
 * @access Private
 */
exports.deleteStreamRecording = async (req, res, next) => {
  try {
    const { id } = req.params;

    // Get the recording
    const recording = await StreamRecording.findById(id);
    if (!recording) {
      return next(createError(404, 'Recording not found'));
    }

    // Check if the user is the owner of the recording
    if (recording.user.toString() !== req.user.id) {
      return next(createError(403, 'You are not authorized to delete this recording'));
    }

    // Delete the recording from Cloudinary
    if (recording.publicId) {
      await cloudinary.uploader.destroy(recording.publicId, { resource_type: 'video' });
    }

    // Delete the thumbnail from Cloudinary
    if (recording.thumbnail && recording.thumbnail.publicId) {
      await cloudinary.uploader.destroy(recording.thumbnail.publicId);
    }

    // Remove the recording from the stream
    await LiveStream.updateOne(
      { _id: recording.stream },
      { $pull: { recordings: id } }
    );

    // Delete the recording
    await StreamRecording.findByIdAndDelete(id);

    res.status(200).json({
      success: true,
      message: 'Recording deleted successfully'
    });
  } catch (err) {
    console.error('Error deleting stream recording:', err);
    next(err);
  }
};

/**
 * Toggle like on a recording
 * @route POST /api/live-streams/recordings/:id/like
 * @access Private
 */
exports.toggleLike = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { action } = req.body;

    // Validate action
    if (action !== 'like' && action !== 'unlike') {
      return next(createError(400, 'Invalid action. Must be "like" or "unlike"'));
    }

    // Get the recording
    const recording = await StreamRecording.findById(id);
    if (!recording) {
      return next(createError(404, 'Recording not found'));
    }

    // Check if user has already liked the recording
    const Like = require('../models/Like');
    const existingLike = await Like.findOne({
      user: req.user.id,
      recording: id
    });

    if (action === 'like') {
      // If already liked, return success
      if (existingLike) {
        return res.status(200).json({
          success: true,
          message: 'Recording already liked',
          data: {
            isLiked: true,
            likesCount: recording.likes
          }
        });
      }

      // Create new like
      const newLike = new Like({
        user: req.user.id,
        recording: id
      });

      await newLike.save();

      // Increment likes count
      recording.likes += 1;
      await recording.save();

      res.status(200).json({
        success: true,
        message: 'Recording liked successfully',
        data: {
          isLiked: true,
          likesCount: recording.likes
        }
      });
    } else {
      // If not liked, return success
      if (!existingLike) {
        return res.status(200).json({
          success: true,
          message: 'Recording not liked',
          data: {
            isLiked: false,
            likesCount: recording.likes
          }
        });
      }

      // Remove like
      await Like.findByIdAndDelete(existingLike._id);

      // Decrement likes count
      recording.likes = Math.max(0, recording.likes - 1);
      await recording.save();

      res.status(200).json({
        success: true,
        message: 'Recording unliked successfully',
        data: {
          isLiked: false,
          likesCount: recording.likes
        }
      });
    }
  } catch (err) {
    console.error('Error toggling like:', err);
    next(err);
  }
};

/**
 * Get recording categories
 * @route GET /api/live-streams/recordings/categories
 * @access Public
 */
exports.getCategories = async (req, res, next) => {
  try {
    // Define available categories
    const categories = [
      { value: 'general', label: 'General' },
      { value: 'gaming', label: 'Gaming' },
      { value: 'music', label: 'Music' },
      { value: 'art', label: 'Art & Creative' },
      { value: 'chatting', label: 'Just Chatting' },
      { value: 'education', label: 'Education' },
      { value: 'sports', label: 'Sports & Fitness' },
      { value: 'food', label: 'Food & Cooking' },
      { value: 'travel', label: 'Travel & Outdoors' },
      { value: 'tech', label: 'Technology' },
      { value: 'beauty', label: 'Beauty & Fashion' },
    ];

    res.status(200).json({
      success: true,
      data: categories
    });
  } catch (err) {
    console.error('Error getting categories:', err);
    next(err);
  }
};

/**
 * Edit a stream recording
 * @route PUT /api/live-streams/recordings/:id/edit
 * @access Private
 */
exports.editRecording = async (req, res, next) => {
  try {
    const { id } = req.params;
    const {
      title,
      description,
      category,
      visibility,
      trimStart,
      trimEnd,
      brightness,
      contrast,
      saturation
    } = req.body;

    // Get the recording
    const recording = await StreamRecording.findById(id);
    if (!recording) {
      return next(createError(404, 'Recording not found'));
    }

    // Check if the user is the owner of the recording
    if (recording.user.toString() !== req.user.id) {
      return next(createError(403, 'You are not authorized to edit this recording'));
    }

    // Update basic metadata
    if (title) recording.title = title;
    if (description) recording.description = description;
    if (category) recording.category = category;
    if (visibility) recording.visibility = visibility;

    // Process video edits if needed
    if (trimStart !== undefined || trimEnd !== undefined ||
        brightness !== undefined || contrast !== undefined || saturation !== undefined) {

      // Create a job for video processing
      const videoProcessingJob = {
        recordingId: id,
        userId: req.user.id,
        edits: {
          trimStart: trimStart !== undefined ? parseFloat(trimStart) : null,
          trimEnd: trimEnd !== undefined ? parseFloat(trimEnd) : null,
          brightness: brightness !== undefined ? parseInt(brightness) : 100,
          contrast: contrast !== undefined ? parseInt(contrast) : 100,
          saturation: saturation !== undefined ? parseInt(saturation) : 100
        },
        status: 'pending',
        createdAt: new Date()
      };

      // In a real implementation, you would:
      // 1. Save this job to a database
      // 2. Trigger a background worker to process the video
      // 3. Update the recording when processing is complete

      // For this implementation, we'll just mark that edits are in progress
      recording.status = 'processing';

      // Simulate video processing (in a real app, this would be done by a background worker)
      setTimeout(async () => {
        try {
          // In a real implementation, this would be where you'd:
          // 1. Download the original video
          // 2. Apply the edits using ffmpeg or similar
          // 3. Upload the edited video to replace or create a new version

          // For now, we'll just update the status
          recording.status = 'published';
          await recording.save();

          console.log(`Video processing completed for recording ${id}`);
        } catch (err) {
          console.error(`Error processing video for recording ${id}:`, err);

          // Update status to failed
          recording.status = 'failed';
          await recording.save();
        }
      }, 5000); // Simulate a 5-second processing time
    }

    // Upload thumbnail if provided
    if (req.file && req.file.path) {
      // Upload the thumbnail to Cloudinary
      const result = await cloudinary.uploader.upload(req.file.path, {
        folder: 'letstalk/thumbnails',
        public_id: `recording_${id}_thumbnail_${Date.now()}`,
        overwrite: true
      });

      // Update the recording with the new thumbnail
      recording.thumbnail = {
        url: result.secure_url,
        publicId: result.public_id
      };

      // Remove the temporary file
      await unlinkAsync(req.file.path);
    }

    // Save the updated recording
    await recording.save();

    res.status(200).json({
      success: true,
      message: 'Recording updated successfully',
      data: recording
    });
  } catch (err) {
    console.error('Error editing stream recording:', err);
    next(err);
  }
};
