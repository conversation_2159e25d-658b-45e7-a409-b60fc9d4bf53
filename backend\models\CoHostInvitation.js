const mongoose = require('mongoose');

const CoHostInvitationSchema = new mongoose.Schema({
  stream: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'LiveStream',
    required: true,
  },
  inviter: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  invitee: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  status: {
    type: String,
    enum: ['pending', 'accepted', 'declined', 'expired'],
    default: 'pending',
  },
  message: {
    type: String,
    maxlength: [200, 'Message cannot be more than 200 characters'],
  },
  expiresAt: {
    type: Date,
    default: function() {
      // Default expiration is 24 hours from now
      return new Date(Date.now() + 24 * 60 * 60 * 1000);
    },
  },
  respondedAt: {
    type: Date,
  },
}, {
  timestamps: true,
});

// Create compound index to ensure a user can only have one pending invitation per stream
CoHostInvitationSchema.index({ stream: 1, invitee: 1, status: 1 }, { unique: true });

// Create index for faster queries
CoHostInvitationSchema.index({ invitee: 1, status: 1 });
CoHostInvitationSchema.index({ stream: 1, status: 1 });
CoHostInvitationSchema.index({ expiresAt: 1 }, { expireAfterSeconds: 0 }); // TTL index for auto-deletion

module.exports = mongoose.model('CoHostInvitation', CoHostInvitationSchema);
