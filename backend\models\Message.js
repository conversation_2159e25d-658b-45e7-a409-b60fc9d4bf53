const mongoose = require('mongoose');

// Poll option schema
const PollOptionSchema = new mongoose.Schema({
  text: {
    type: String,
    required: true,
    trim: true,
  },
  voters: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
  }],
}, { _id: true });

// Location schema
const LocationSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
  },
  latitude: {
    type: Number,
    required: true,
  },
  longitude: {
    type: Number,
    required: true,
  },
  address: {
    type: String,
  },
}, { _id: false });

// GIF schema
const GifSchema = new mongoose.Schema({
  url: {
    type: String,
    required: true,
  },
  source: {
    type: String,
    enum: ['giphy', 'tenor', 'other'],
    default: 'giphy',
  },
  id: {
    type: String,
  },
}, { _id: false });

// Voice note schema
const VoiceNoteSchema = new mongoose.Schema({
  url: {
    type: String,
    required: true,
  },
  publicId: {
    type: String,
    required: true,
  },
  duration: {
    type: Number, // in seconds
    required: true,
  },
  waveform: {
    type: [Number], // Array of numbers representing the waveform
    default: [],
  },
}, { _id: false });

const MessageSchema = new mongoose.Schema({
  sender: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  conversation: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Conversation',
    required: true,
  },
  text: {
    type: String,
    trim: true,
  },
  media: [
    {
      url: {
        type: String,
        required: true,
      },
      type: {
        type: String,
        enum: ['image', 'video', 'audio', 'file'],
        required: true,
      },
      publicId: {
        type: String,
        required: true,
      },
      fileName: {
        type: String,
      },
      fileSize: {
        type: Number,
      },
    },
  ],
  // New message types
  messageType: {
    type: String,
    enum: ['text', 'media', 'poll', 'location', 'gif', 'voice', 'command'],
    default: 'text',
  },
  poll: {
    question: {
      type: String,
      trim: true,
    },
    options: [PollOptionSchema],
    expiresAt: {
      type: Date,
    },
    allowMultipleVotes: {
      type: Boolean,
      default: false,
    },
    isAnonymous: {
      type: Boolean,
      default: false,
    },
  },
  location: LocationSchema,
  gif: GifSchema,
  voiceNote: VoiceNoteSchema,
  command: {
    type: {
      type: String,
      enum: ['ai', 'remind', 'poll', 'gif', 'location', 'voice'],
    },
    params: {
      type: Map,
      of: mongoose.Schema.Types.Mixed,
    },
  },
  emotions: [
    {
      emotion: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Emotion',
      },
      intensity: {
        type: Number,
        min: 1,
        max: 10,
        default: 5,
      },
    },
  ],
  reactions: [
    {
      user: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true,
      },
      emoji: {
        type: String,
        required: true,
      },
    },
  ],
  replyTo: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Message',
  },
  readBy: [
    {
      user: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true,
      },
      readAt: {
        type: Date,
        default: Date.now,
      },
    },
  ],
  deliveredTo: [
    {
      user: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true,
      },
      deliveredAt: {
        type: Date,
        default: Date.now,
      },
    },
  ],
  isForwarded: {
    type: Boolean,
    default: false,
  },
  isEdited: {
    type: Boolean,
    default: false,
  },
  isPinned: {
    type: Boolean,
    default: false,
  },
  isDeleted: {
    type: Boolean,
    default: false,
  },
  scheduledFor: {
    type: Date,
    default: null,
  },
  isScheduled: {
    type: Boolean,
    default: false,
  },
}, {
  timestamps: true,
});

// Virtual for checking if message has media
MessageSchema.virtual('hasMedia').get(function() {
  return this.media && this.media.length > 0;
});

// Virtual for checking if message has text
MessageSchema.virtual('hasText').get(function() {
  return this.text && this.text.trim().length > 0;
});

// Virtual for checking if message is read by a specific user
MessageSchema.methods.isReadBy = function(userId) {
  return this.readBy.some(read => read.user.toString() === userId.toString());
};

// Virtual for checking if message is delivered to a specific user
MessageSchema.methods.isDeliveredTo = function(userId) {
  return this.deliveredTo.some(delivery => delivery.user.toString() === userId.toString());
};

module.exports = mongoose.model('Message', MessageSchema);
