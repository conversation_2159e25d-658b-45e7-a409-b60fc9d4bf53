const express = require('express');
const router = express.Router({ mergeParams: true });
const { protect } = require('../middleware/auth');
const {
  getVoiceCommands,
  createVoiceCommand,
  updateVoiceCommand,
  deleteVoiceCommand,
  executeVoiceCommand,
  getVoiceCommandSettings,
  updateVoiceCommandSettings,
} = require('../controllers/voiceCommandController');

// All routes are protected
router.use(protect);

router.get('/', getVoiceCommands);
router.post('/', createVoiceCommand);
router.put('/:id', updateVoiceCommand);
router.delete('/:id', deleteVoiceCommand);
router.post('/execute', executeVoiceCommand);

// Voice command settings routes
router.get('/settings', getVoiceCommandSettings);
router.put('/settings', updateVoiceCommandSettings);

module.exports = router;
