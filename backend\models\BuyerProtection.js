const mongoose = require('mongoose');

const BuyerProtectionSchema = new mongoose.Schema({
  order: {
    type: mongoose.Schema.ObjectId,
    ref: 'Order',
    required: true,
    unique: true
  },
  buyer: {
    type: mongoose.Schema.ObjectId,
    ref: 'User',
    required: true
  },
  vendor: {
    type: mongoose.Schema.ObjectId,
    ref: 'Vendor',
    required: true
  },
  protectionType: {
    type: String,
    enum: ['purchase_protection', 'money_back_guarantee', 'shipping_protection', 'quality_guarantee'],
    required: true
  },
  status: {
    type: String,
    enum: ['active', 'claimed', 'resolved', 'expired', 'cancelled'],
    default: 'active'
  },
  coverage: {
    amount: {
      type: Number,
      required: true
    },
    currency: {
      type: String,
      default: 'USD'
    },
    percentage: {
      type: Number,
      default: 100,
      min: 0,
      max: 100
    }
  },
  terms: {
    expiryDate: {
      type: Date,
      required: true
    },
    conditions: [String],
    exclusions: [String],
    claimDeadline: {
      type: Date,
      required: true
    }
  },
  claim: {
    isActive: {
      type: Boolean,
      default: false
    },
    claimedAt: Date,
    reason: {
      type: String,
      enum: ['item_not_received', 'item_not_as_described', 'damaged_item', 'wrong_item', 'quality_issue', 'other']
    },
    description: String,
    evidence: [{
      type: String, // URLs to uploaded evidence
      description: String,
      uploadedAt: {
        type: Date,
        default: Date.now
      }
    }],
    requestedAction: {
      type: String,
      enum: ['full_refund', 'partial_refund', 'replacement', 'repair', 'store_credit']
    },
    requestedAmount: Number
  },
  resolution: {
    resolvedAt: Date,
    resolvedBy: {
      type: mongoose.Schema.ObjectId,
      ref: 'User'
    },
    action: {
      type: String,
      enum: ['refund_issued', 'replacement_sent', 'store_credit_issued', 'claim_denied', 'partial_refund']
    },
    amount: Number,
    notes: String,
    buyerSatisfied: Boolean
  },
  timeline: [{
    action: String,
    description: String,
    performedBy: {
      type: mongoose.Schema.ObjectId,
      ref: 'User'
    },
    timestamp: {
      type: Date,
      default: Date.now
    },
    metadata: mongoose.Schema.Types.Mixed
  }],
  paymentGateway: {
    provider: {
      type: String,
      enum: ['stripe', 'paypal', 'square', 'razorpay'],
      required: true
    },
    transactionId: String,
    paymentIntentId: String,
    chargeId: String,
    refundId: String,
    escrowId: String
  },
  escrow: {
    isActive: {
      type: Boolean,
      default: false
    },
    amount: Number,
    releaseConditions: [String],
    holdUntil: Date,
    releasedAt: Date,
    releasedTo: {
      type: String,
      enum: ['buyer', 'vendor', 'platform']
    }
  },
  compliance: {
    gdprConsent: {
      type: Boolean,
      default: false
    },
    dataRetentionUntil: Date,
    pciCompliant: {
      type: Boolean,
      default: true
    },
    kycVerified: {
      buyer: Boolean,
      vendor: Boolean
    }
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Update the updatedAt field before saving
BuyerProtectionSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

// Add timeline entry
BuyerProtectionSchema.methods.addTimelineEntry = function(action, description, performedBy, metadata = {}) {
  this.timeline.push({
    action,
    description,
    performedBy,
    metadata
  });
  return this.save();
};

// Activate claim
BuyerProtectionSchema.methods.activateClaim = function(reason, description, evidence, requestedAction, requestedAmount) {
  this.claim = {
    isActive: true,
    claimedAt: new Date(),
    reason,
    description,
    evidence,
    requestedAction,
    requestedAmount
  };
  this.status = 'claimed';
  
  return this.addTimelineEntry(
    'claim_activated',
    `Buyer protection claim activated: ${reason}`,
    this.buyer
  );
};

// Resolve claim
BuyerProtectionSchema.methods.resolveClaim = function(action, amount, notes, resolvedBy, buyerSatisfied = true) {
  this.resolution = {
    resolvedAt: new Date(),
    resolvedBy,
    action,
    amount,
    notes,
    buyerSatisfied
  };
  this.status = 'resolved';
  
  return this.addTimelineEntry(
    'claim_resolved',
    `Claim resolved with action: ${action}`,
    resolvedBy,
    { action, amount, buyerSatisfied }
  );
};

// Check if protection is still valid
BuyerProtectionSchema.methods.isValid = function() {
  return this.status === 'active' && 
         new Date() <= this.terms.expiryDate && 
         new Date() <= this.terms.claimDeadline;
};

// Calculate refund amount
BuyerProtectionSchema.methods.calculateRefund = function(requestedAmount) {
  const maxRefund = (this.coverage.amount * this.coverage.percentage) / 100;
  return Math.min(requestedAmount || this.coverage.amount, maxRefund);
};

// Activate escrow
BuyerProtectionSchema.methods.activateEscrow = function(amount, releaseConditions, holdUntil) {
  this.escrow = {
    isActive: true,
    amount,
    releaseConditions,
    holdUntil
  };
  
  return this.addTimelineEntry(
    'escrow_activated',
    `Escrow activated for amount: ${amount}`,
    null,
    { amount, holdUntil }
  );
};

// Release escrow
BuyerProtectionSchema.methods.releaseEscrow = function(releasedTo, performedBy) {
  this.escrow.releasedAt = new Date();
  this.escrow.releasedTo = releasedTo;
  this.escrow.isActive = false;
  
  return this.addTimelineEntry(
    'escrow_released',
    `Escrow released to: ${releasedTo}`,
    performedBy,
    { releasedTo, amount: this.escrow.amount }
  );
};

// Create indexes for better performance
BuyerProtectionSchema.index({ order: 1 });
BuyerProtectionSchema.index({ buyer: 1, status: 1 });
BuyerProtectionSchema.index({ vendor: 1, status: 1 });
BuyerProtectionSchema.index({ status: 1, createdAt: -1 });
BuyerProtectionSchema.index({ 'terms.expiryDate': 1 });
BuyerProtectionSchema.index({ 'claim.isActive': 1 });
BuyerProtectionSchema.index({ 'escrow.isActive': 1 });

module.exports = mongoose.model('BuyerProtection', BuyerProtectionSchema);
