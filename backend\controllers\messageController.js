const Message = require('../models/Message');
const Conversation = require('../models/Conversation');
const User = require('../models/User');
const { createError } = require('../utils/error');
const socketEmitter = require('../utils/socketEmitter');
const { cloudinary, uploadToCloudinary } = require('../config/cloudinary');
const schedulerService = require('../services/schedulerService');

/**
 * Get messages for a conversation
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.getMessages = async (req, res, next) => {
  try {
    const { conversationId } = req.params;
    const { page = 1, limit = 20 } = req.query;

    // Check if conversation exists
    const conversation = await Conversation.findById(conversationId);
    if (!conversation) {
      return next(createError(404, 'Conversation not found'));
    }

    // Check if user is a participant
    if (!conversation.isParticipant(req.user.id)) {
      return next(createError(403, 'You are not a participant in this conversation'));
    }

    // Calculate pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);

    // Get messages with pagination (newest first, then reverse for display)
    const messages = await Message.find({
      conversation: conversationId,
      isDeleted: { $ne: true }
    })
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit))
      .populate('sender', 'username name profilePicture')
      .populate('replyTo')
      .lean();

    // Reverse messages to show oldest first
    const orderedMessages = messages.reverse();

    // Mark messages as read
    await Message.updateMany(
      {
        conversation: conversationId,
        sender: { $ne: req.user.id },
        'readBy.user': { $ne: req.user.id }
      },
      {
        $push: {
          readBy: {
            user: req.user.id,
            readAt: new Date()
          }
        }
      }
    );

    // Reset unread count for user
    await Conversation.findByIdAndUpdate(
      conversationId,
      { $set: { [`unreadCount.${req.user.id}`]: 0 } }
    );

    // Emit read status to other participants
    socketEmitter.emitMessagesRead(conversationId, req.user.id);

    // Get total count for pagination
    const totalMessages = await Message.countDocuments({
      conversation: conversationId,
      isDeleted: { $ne: true }
    });

    res.status(200).json({
      success: true,
      data: orderedMessages,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages: Math.ceil(totalMessages / parseInt(limit)),
        totalMessages,
      },
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Send a new message
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.sendMessage = async (req, res, next) => {
  try {
    const { conversationId } = req.params;
    const {
      text,
      replyTo,
      emotions,
      messageType,
      poll,
      location,
      gif,
      command
    } = req.body;

    let media = [];
    let voiceNote = null;

    // Check if conversation exists
    const conversation = await Conversation.findById(conversationId);
    if (!conversation) {
      return next(createError(404, 'Conversation not found'));
    }

    // Check if user is a participant
    if (!conversation.isParticipant(req.user.id)) {
      return next(createError(403, 'You are not a participant in this conversation'));
    }

    // Determine message type and validate content
    let finalMessageType = messageType || 'text';

    // Check if message has content based on type
    if (finalMessageType === 'text' && !text && (!req.files || req.files.length === 0)) {
      return next(createError(400, 'Message cannot be empty'));
    } else if (finalMessageType === 'poll' && (!poll || !poll.question || !poll.options || poll.options.length < 2)) {
      return next(createError(400, 'Poll must have a question and at least 2 options'));
    } else if (finalMessageType === 'location' && (!location || !location.latitude || !location.longitude || !location.name)) {
      return next(createError(400, 'Location must have name, latitude and longitude'));
    } else if (finalMessageType === 'gif' && (!gif || !gif.url)) {
      return next(createError(400, 'GIF must have a URL'));
    } else if (finalMessageType === 'command' && (!command || !command.type)) {
      return next(createError(400, 'Command must have a type'));
    }

    // Handle media uploads if any
    if (req.files && req.files.length > 0) {
      for (const file of req.files) {
        // Upload to Cloudinary
        const result = await cloudinary.uploader.upload(file.path, {
          folder: 'letstalk/messages',
          resource_type: 'auto',
        });

        // Determine media type
        let mediaType = 'file';
        if (file.mimetype.startsWith('image/')) {
          mediaType = 'image';
          finalMessageType = finalMessageType === 'text' ? 'media' : finalMessageType;
        } else if (file.mimetype.startsWith('video/')) {
          mediaType = 'video';
          finalMessageType = finalMessageType === 'text' ? 'media' : finalMessageType;
        } else if (file.mimetype.startsWith('audio/')) {
          // Check if this is a voice note
          if (file.originalname.includes('voice_note') || req.body.isVoiceNote) {
            finalMessageType = 'voice';

            // Process voice note
            voiceNote = {
              url: result.secure_url,
              publicId: result.public_id,
              duration: req.body.duration || 0,
              waveform: req.body.waveform ? JSON.parse(req.body.waveform) : []
            };
          } else {
            mediaType = 'audio';
            finalMessageType = finalMessageType === 'text' ? 'media' : finalMessageType;

            media.push({
              url: result.secure_url,
              type: mediaType,
              publicId: result.public_id,
              fileName: file.originalname,
              fileSize: file.size,
            });
          }
        } else {
          mediaType = 'file';
          finalMessageType = finalMessageType === 'text' ? 'media' : finalMessageType;

          media.push({
            url: result.secure_url,
            type: mediaType,
            publicId: result.public_id,
            fileName: file.originalname,
            fileSize: file.size,
          });
        }

        // Only add to media array if it's not a voice note
        if (mediaType !== 'audio' || finalMessageType !== 'voice') {
          media.push({
            url: result.secure_url,
            type: mediaType,
            publicId: result.public_id,
            fileName: file.originalname,
            fileSize: file.size,
          });
        }
      }
    }

    // Create message with appropriate fields based on type
    const scheduledFor = req.body.scheduledFor ? new Date(req.body.scheduledFor) : null;
    const isScheduled = !!scheduledFor && scheduledFor > new Date();

    const messageData = {
      sender: req.user.id,
      conversation: conversationId,
      messageType: finalMessageType,
      text: finalMessageType === 'text' || finalMessageType === 'media' ? text : undefined,
      media: finalMessageType === 'media' ? media : [],
      replyTo,
      emotions,
      deliveredTo: [{ user: req.user.id }], // Mark as delivered to sender
      scheduledFor,
      isScheduled,
    };

    // Add type-specific data
    if (finalMessageType === 'poll' && poll) {
      messageData.poll = {
        question: poll.question,
        options: poll.options.map(option => ({ text: option, voters: [] })),
        expiresAt: poll.expiresAt,
        allowMultipleVotes: poll.allowMultipleVotes || false,
        isAnonymous: poll.isAnonymous || false
      };
    } else if (finalMessageType === 'location' && location) {
      messageData.location = {
        name: location.name,
        latitude: location.latitude,
        longitude: location.longitude,
        address: location.address
      };
    } else if (finalMessageType === 'gif' && gif) {
      messageData.gif = {
        url: gif.url,
        source: gif.source || 'giphy',
        id: gif.id
      };
    } else if (finalMessageType === 'voice' && voiceNote) {
      messageData.voiceNote = voiceNote;
    } else if (finalMessageType === 'command' && command) {
      messageData.command = {
        type: command.type,
        params: command.params || {}
      };
    }

    // Create message
    const message = await Message.create(messageData);

    // Update conversation with last message
    conversation.lastMessage = message._id;

    // Increment unread count for all participants except sender
    conversation.incrementUnreadCount(req.user.id);
    await conversation.save();

    // Populate sender details
    await message.populate('sender', 'username name profilePicture');

    // Populate reply details if any
    if (replyTo) {
      await message.populate('replyTo');
    }

    // Handle scheduled messages
    if (message.isScheduled && message.scheduledFor) {
      // Schedule the message to be sent at the specified time
      schedulerService.scheduleMessage(message);
    } else {
      // Emit message to participants immediately
      socketEmitter.emitNewMessage(message);
    }

    res.status(201).json({
      success: true,
      data: message,
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Mark messages as read
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.markMessagesAsRead = async (req, res, next) => {
  try {
    const { conversationId } = req.params;

    // Check if conversation exists
    const conversation = await Conversation.findById(conversationId);
    if (!conversation) {
      return next(createError(404, 'Conversation not found'));
    }

    // Check if user is a participant
    if (!conversation.isParticipant(req.user.id)) {
      return next(createError(403, 'You are not a participant in this conversation'));
    }

    // Mark all messages as read
    await Message.updateMany(
      {
        conversation: conversationId,
        sender: { $ne: req.user.id },
        'readBy.user': { $ne: req.user.id }
      },
      {
        $push: {
          readBy: {
            user: req.user.id,
            readAt: new Date()
          }
        }
      }
    );

    // Reset unread count for user
    await Conversation.findByIdAndUpdate(
      conversationId,
      { $set: { [`unreadCount.${req.user.id}`]: 0 } }
    );

    // Emit read status to other participants
    socketEmitter.emitMessagesRead(conversationId, req.user.id);

    res.status(200).json({
      success: true,
      message: 'Messages marked as read',
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Delete a message
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.deleteMessage = async (req, res, next) => {
  try {
    const { messageId } = req.params;

    // Find message
    const message = await Message.findById(messageId);
    if (!message) {
      return next(createError(404, 'Message not found'));
    }

    // Check if user is the sender
    if (message.sender.toString() !== req.user.id) {
      return next(createError(403, 'You can only delete your own messages'));
    }

    // Soft delete the message
    message.isDeleted = true;
    await message.save();

    // Emit message deletion to all participants
    socketEmitter.emitMessageDeleted(message.conversation, messageId);

    res.status(200).json({
      success: true,
      message: 'Message deleted',
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Add reaction to a message
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.addReaction = async (req, res, next) => {
  try {
    const { messageId } = req.params;
    const { emoji } = req.body;

    if (!emoji) {
      return next(createError(400, 'Emoji is required'));
    }

    // Find message
    const message = await Message.findById(messageId);
    if (!message) {
      return next(createError(404, 'Message not found'));
    }

    // Check if user is a participant in the conversation
    const conversation = await Conversation.findById(message.conversation);
    if (!conversation.isParticipant(req.user.id)) {
      return next(createError(403, 'You are not a participant in this conversation'));
    }

    // Remove existing reaction from the same user if any
    message.reactions = message.reactions.filter(
      reaction => reaction.user.toString() !== req.user.id
    );

    // Add new reaction
    message.reactions.push({
      user: req.user.id,
      emoji,
    });

    await message.save();

    // Emit reaction to all participants
    socketEmitter.emitMessageReaction(message.conversation, messageId, req.user.id, emoji);

    res.status(200).json({
      success: true,
      data: message.reactions,
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Remove reaction from a message
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.removeReaction = async (req, res, next) => {
  try {
    const { messageId } = req.params;

    // Find message
    const message = await Message.findById(messageId);
    if (!message) {
      return next(createError(404, 'Message not found'));
    }

    // Check if user is a participant in the conversation
    const conversation = await Conversation.findById(message.conversation);
    if (!conversation.isParticipant(req.user.id)) {
      return next(createError(403, 'You are not a participant in this conversation'));
    }

    // Remove reaction
    message.reactions = message.reactions.filter(
      reaction => reaction.user.toString() !== req.user.id
    );

    await message.save();

    // Emit reaction removal to all participants
    socketEmitter.emitMessageReactionRemoved(message.conversation, messageId, req.user.id);

    res.status(200).json({
      success: true,
      data: message.reactions,
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Search messages
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.searchMessages = async (req, res, next) => {
  try {
    const { conversationId } = req.params;
    const { query, mediaType } = req.query;

    // Check if conversation exists
    const conversation = await Conversation.findById(conversationId);
    if (!conversation) {
      return next(createError(404, 'Conversation not found'));
    }

    // Check if user is a participant
    if (!conversation.isParticipant(req.user.id)) {
      return next(createError(403, 'You are not a participant in this conversation'));
    }

    // Build search query
    const searchQuery = {
      conversation: conversationId,
      isDeleted: { $ne: true },
    };

    if (query) {
      searchQuery.text = { $regex: query, $options: 'i' };
    }

    if (mediaType) {
      searchQuery['media.type'] = mediaType;
    }

    // Get messages
    const messages = await Message.find(searchQuery)
      .sort({ createdAt: -1 })
      .populate('sender', 'username name profilePicture')
      .populate('replyTo')
      .lean();

    res.status(200).json({
      success: true,
      data: messages,
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Pin a message
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.pinMessage = async (req, res, next) => {
  try {
    const { messageId } = req.params;

    // Find message
    const message = await Message.findById(messageId);
    if (!message) {
      return next(createError(404, 'Message not found'));
    }

    // Check if user is a participant in the conversation
    const conversation = await Conversation.findById(message.conversation);
    if (!conversation.isParticipant(req.user.id)) {
      return next(createError(403, 'You are not a participant in this conversation'));
    }

    // Pin the message
    message.isPinned = true;
    await message.save();

    // Emit pin status to all participants
    socketEmitter.emitMessagePinned(message.conversation, messageId);

    res.status(200).json({
      success: true,
      message: 'Message pinned',
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Unpin a message
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.unpinMessage = async (req, res, next) => {
  try {
    const { messageId } = req.params;

    // Find message
    const message = await Message.findById(messageId);
    if (!message) {
      return next(createError(404, 'Message not found'));
    }

    // Check if user is a participant in the conversation
    const conversation = await Conversation.findById(message.conversation);
    if (!conversation.isParticipant(req.user.id)) {
      return next(createError(403, 'You are not a participant in this conversation'));
    }

    // Unpin the message
    message.isPinned = false;
    await message.save();

    // Emit unpin status to all participants
    socketEmitter.emitMessageUnpinned(message.conversation, messageId);

    res.status(200).json({
      success: true,
      message: 'Message unpinned',
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Vote on a poll
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.votePoll = async (req, res, next) => {
  try {
    const { messageId, optionId } = req.params;

    // Find message
    const message = await Message.findById(messageId);
    if (!message) {
      return next(createError(404, 'Message not found'));
    }

    // Check if message is a poll
    if (message.messageType !== 'poll') {
      return next(createError(400, 'Message is not a poll'));
    }

    // Check if poll has expired
    if (message.poll.expiresAt && new Date(message.poll.expiresAt) < new Date()) {
      return next(createError(400, 'Poll has expired'));
    }

    // Check if user is a participant in the conversation
    const conversation = await Conversation.findById(message.conversation);
    if (!conversation.isParticipant(req.user.id)) {
      return next(createError(403, 'You are not a participant in this conversation'));
    }

    // Find the option
    const option = message.poll.options.id(optionId);
    if (!option) {
      return next(createError(404, 'Option not found'));
    }

    // Check if user has already voted and remove previous vote if not allowing multiple votes
    if (!message.poll.allowMultipleVotes) {
      message.poll.options.forEach(opt => {
        opt.voters = opt.voters.filter(voter => voter.toString() !== req.user.id);
      });
    }

    // Add vote
    if (!option.voters.includes(req.user.id)) {
      option.voters.push(req.user.id);
    }

    await message.save();

    // Emit poll update to all participants
    socketEmitter.emitPollUpdated(message.conversation, messageId);

    res.status(200).json({
      success: true,
      data: message.poll,
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Remove vote from a poll
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.removeVote = async (req, res, next) => {
  try {
    const { messageId, optionId } = req.params;

    // Find message
    const message = await Message.findById(messageId);
    if (!message) {
      return next(createError(404, 'Message not found'));
    }

    // Check if message is a poll
    if (message.messageType !== 'poll') {
      return next(createError(400, 'Message is not a poll'));
    }

    // Check if poll has expired
    if (message.poll.expiresAt && new Date(message.poll.expiresAt) < new Date()) {
      return next(createError(400, 'Poll has expired'));
    }

    // Check if user is a participant in the conversation
    const conversation = await Conversation.findById(message.conversation);
    if (!conversation.isParticipant(req.user.id)) {
      return next(createError(403, 'You are not a participant in this conversation'));
    }

    // Find the option
    const option = message.poll.options.id(optionId);
    if (!option) {
      return next(createError(404, 'Option not found'));
    }

    // Remove vote
    option.voters = option.voters.filter(voter => voter.toString() !== req.user.id);

    await message.save();

    // Emit poll update to all participants
    socketEmitter.emitPollUpdated(message.conversation, messageId);

    res.status(200).json({
      success: true,
      data: message.poll,
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Process a command message
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.processCommand = async (req, res, next) => {
  try {
    const { messageId } = req.params;

    // Find message
    const message = await Message.findById(messageId);
    if (!message) {
      return next(createError(404, 'Message not found'));
    }

    // Check if message is a command
    if (message.messageType !== 'command') {
      return next(createError(400, 'Message is not a command'));
    }

    // Check if user is a participant in the conversation
    const conversation = await Conversation.findById(message.conversation);
    if (!conversation.isParticipant(req.user.id)) {
      return next(createError(403, 'You are not a participant in this conversation'));
    }

    let result = null;

    // Process command based on type
    switch (message.command.type) {
      case 'remind':
        // Schedule a reminder
        const reminderTime = message.command.params.get('time');
        const reminderText = message.command.params.get('text');

        if (!reminderTime || !reminderText) {
          return next(createError(400, 'Reminder requires time and text'));
        }

        // Schedule the reminder
        const job = schedule.scheduleJob(new Date(reminderTime), async function() {
          // Create a new message for the reminder
          const reminderMessage = await Message.create({
            sender: req.user.id,
            conversation: message.conversation,
            messageType: 'text',
            text: `🔔 Reminder: ${reminderText}`,
            deliveredTo: [{ user: req.user.id }],
          });

          // Update conversation with last message
          await Conversation.findByIdAndUpdate(message.conversation, {
            lastMessage: reminderMessage._id,
          });

          // Emit new message to all participants
          socketEmitter.emitNewMessage(reminderMessage);
        });

        result = {
          type: 'remind',
          time: reminderTime,
          text: reminderText,
        };
        break;

      case 'ai':
        // Process AI command (placeholder for future implementation)
        result = {
          type: 'ai',
          response: 'AI processing is not implemented yet.',
        };
        break;

      default:
        return next(createError(400, `Unknown command type: ${message.command.type}`));
    }

    res.status(200).json({
      success: true,
      data: result,
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Cancel a scheduled message
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.cancelScheduledMessage = async (req, res, next) => {
  try {
    const { messageId } = req.params;

    // Find message
    const message = await Message.findById(messageId);
    if (!message) {
      return next(createError(404, 'Message not found'));
    }

    // Check if user is the sender
    if (message.sender.toString() !== req.user.id) {
      return next(createError(403, 'You can only cancel your own scheduled messages'));
    }

    // Check if message is scheduled
    if (!message.isScheduled) {
      return next(createError(400, 'Message is not scheduled'));
    }

    // Cancel scheduled message
    schedulerService.cancelScheduledMessage(message._id.toString());

    // Update message
    message.isScheduled = false;
    await message.save();

    res.status(200).json({
      success: true,
      message: 'Scheduled message cancelled',
    });
  } catch (err) {
    next(err);
  }
};