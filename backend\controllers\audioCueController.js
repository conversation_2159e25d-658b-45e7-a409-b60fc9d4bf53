const AudioCue = require('../models/AudioCue');
const { createError } = require('../utils/error');
const { cloudinary, uploadToCloudinary } = require('../config/cloudinary');
const mongoose = require('mongoose');

/**
 * Get all audio cues
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.getAudioCues = async (req, res, next) => {
  try {
    const { emotion, limit = 20 } = req.query;

    // Build query
    const query = { isPublic: true };
    if (emotion) {
      query.emotion = emotion;
    }

    // Find audio cues
    const audioCues = await AudioCue.find(query)
      .sort({ isDefault: -1, createdAt: -1 })
      .limit(parseInt(limit));

    // If no audio cues found, return default ones
    if (audioCues.length === 0) {
      // Create default audio cues
      const defaultCues = [
        {
          _id: new mongoose.Types.ObjectId(),
          name: 'Joy Sound',
          description: 'A cheerful sound for positive emotions',
          url: 'https://res.cloudinary.com/dtx9ed1hh/video/upload/v1/audio_cues/joy.mp3',
          duration: 2,
          emotion: 'positive',
          intensity: 5,
          isDefault: true,
          isPublic: true,
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          _id: new mongoose.Types.ObjectId(),
          name: 'Sadness Sound',
          description: 'A melancholic sound for negative emotions',
          url: 'https://res.cloudinary.com/dtx9ed1hh/video/upload/v1/audio_cues/sadness.mp3',
          duration: 2,
          emotion: 'negative',
          intensity: 5,
          isDefault: true,
          isPublic: true,
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          _id: new mongoose.Types.ObjectId(),
          name: 'Neutral Sound',
          description: 'A balanced sound for neutral emotions',
          url: 'https://res.cloudinary.com/dtx9ed1hh/video/upload/v1/audio_cues/neutral.mp3',
          duration: 2,
          emotion: 'neutral',
          intensity: 5,
          isDefault: true,
          isPublic: true,
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          _id: new mongoose.Types.ObjectId(),
          name: 'Surprise Sound',
          description: 'An unexpected sound for complex emotions',
          url: 'https://res.cloudinary.com/dtx9ed1hh/video/upload/v1/audio_cues/surprise.mp3',
          duration: 2,
          emotion: 'complex',
          intensity: 5,
          isDefault: true,
          isPublic: true,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      ];

      // Try to save these default cues to the database
      try {
        await AudioCue.insertMany(defaultCues);
        console.log('Default audio cues created');
      } catch (saveErr) {
        console.error('Error saving default audio cues:', saveErr);
        // Continue with the response even if saving fails
      }

      return res.status(200).json({
        success: true,
        count: defaultCues.length,
        data: defaultCues,
      });
    }

    res.status(200).json({
      success: true,
      count: audioCues.length,
      data: audioCues,
    });
  } catch (err) {
    console.error('Error in getAudioCues:', err);
    // Return default cues even on error
    const fallbackCues = [
      {
        _id: 'fallback-joy',
        name: 'Joy Sound',
        description: 'A cheerful sound for positive emotions',
        url: 'https://res.cloudinary.com/dtx9ed1hh/video/upload/v1/audio_cues/joy.mp3',
        duration: 2,
        emotion: 'positive',
        intensity: 5,
        isDefault: true,
        isPublic: true
      },
      {
        _id: 'fallback-sadness',
        name: 'Sadness Sound',
        description: 'A melancholic sound for negative emotions',
        url: 'https://res.cloudinary.com/dtx9ed1hh/video/upload/v1/audio_cues/sadness.mp3',
        duration: 2,
        emotion: 'negative',
        intensity: 5,
        isDefault: true,
        isPublic: true
      },
      {
        _id: 'fallback-neutral',
        name: 'Neutral Sound',
        description: 'A balanced sound for neutral emotions',
        url: 'https://res.cloudinary.com/dtx9ed1hh/video/upload/v1/audio_cues/neutral.mp3',
        duration: 2,
        emotion: 'neutral',
        intensity: 5,
        isDefault: true,
        isPublic: true
      }
    ];

    return res.status(200).json({
      success: true,
      count: fallbackCues.length,
      data: fallbackCues,
    });
  }
};

/**
 * Get a single audio cue
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.getAudioCue = async (req, res, next) => {
  try {
    const audioCue = await AudioCue.findById(req.params.id);

    if (!audioCue) {
      return next(createError(404, 'Audio cue not found'));
    }

    // Check if audio cue is public or belongs to the user
    if (!audioCue.isPublic && (!req.user || audioCue.creator.toString() !== req.user.id)) {
      return next(createError(403, 'Not authorized to access this audio cue'));
    }

    res.status(200).json({
      success: true,
      data: audioCue,
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Create a new audio cue
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.createAudioCue = async (req, res, next) => {
  try {
    const { name, description, emotion, intensity, isPublic } = req.body;

    // Check if audio file is provided
    if (!req.files || !req.files.audio) {
      return next(createError(400, 'Please upload an audio file'));
    }

    // Upload audio to cloudinary
    const result = await cloudinary.uploader.upload(req.files.audio.tempFilePath, {
      resource_type: 'auto',
      folder: 'audio_cues',
    });

    // Create audio cue
    const audioCue = await AudioCue.create({
      name,
      description,
      url: result.secure_url,
      duration: result.duration || 3,
      emotion,
      intensity: intensity || 5,
      isPublic: isPublic !== undefined ? isPublic : true,
      creator: req.user.id,
    });

    res.status(201).json({
      success: true,
      data: audioCue,
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Update an audio cue
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.updateAudioCue = async (req, res, next) => {
  try {
    const { name, description, emotion, intensity, isPublic } = req.body;

    // Find audio cue
    let audioCue = await AudioCue.findById(req.params.id);

    if (!audioCue) {
      return next(createError(404, 'Audio cue not found'));
    }

    // Check if user is the creator
    if (audioCue.creator && audioCue.creator.toString() !== req.user.id) {
      return next(createError(403, 'Not authorized to update this audio cue'));
    }

    // Update fields
    if (name) audioCue.name = name;
    if (description) audioCue.description = description;
    if (emotion) audioCue.emotion = emotion;
    if (intensity) audioCue.intensity = intensity;
    if (isPublic !== undefined) audioCue.isPublic = isPublic;

    // Upload new audio file if provided
    if (req.files && req.files.audio) {
      const result = await cloudinary.uploader.upload(req.files.audio.tempFilePath, {
        resource_type: 'auto',
        folder: 'audio_cues',
      });

      audioCue.url = result.secure_url;
      audioCue.duration = result.duration || 3;
    }

    // Save changes
    await audioCue.save();

    res.status(200).json({
      success: true,
      data: audioCue,
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Delete an audio cue
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.deleteAudioCue = async (req, res, next) => {
  try {
    // Find audio cue
    const audioCue = await AudioCue.findById(req.params.id);

    if (!audioCue) {
      return next(createError(404, 'Audio cue not found'));
    }

    // Check if user is the creator
    if (audioCue.creator && audioCue.creator.toString() !== req.user.id) {
      return next(createError(403, 'Not authorized to delete this audio cue'));
    }

    // Delete audio cue
    await audioCue.remove();

    res.status(200).json({
      success: true,
      message: 'Audio cue deleted successfully',
    });
  } catch (err) {
    next(err);
  }
};
