const Product = require('../models/Product');
const Category = require('../models/Category');
const User = require('../models/User');
const Cart = require('../models/Cart');
const Wishlist = require('../models/Wishlist');
const Order = require('../models/Order');
const Vendor = require('../models/Vendor');

// Import payments service with error handling
let paymentsPayoutsService;
try {
  paymentsPayoutsService = require('../services/paymentsPayoutsService');
} catch (error) {
  console.warn('Payments service not available:', error.message);
  paymentsPayoutsService = {
    calculateCommission: async () => {
      console.log('Mock commission calculation - payments service not available');
      return null;
    }
  };
}

// @desc    Get all products
// @route   GET /api/shop/products
// @access  Public
exports.getProducts = async (req, res) => {
  try {
    const page = parseInt(req.query.page, 10) || 1;
    const limit = parseInt(req.query.limit, 10) || 10;
    const startIndex = (page - 1) * limit;
    const endIndex = page * limit;

    const total = await Product.countDocuments();

    const products = await Product.find()
      .sort({ createdAt: -1 })
      .skip(startIndex)
      .limit(limit)
      .populate('seller', 'username avatar businessInfo')
      .populate('category', 'name');

    // Pagination result
    const pagination = {};

    if (endIndex < total) {
      pagination.next = {
        page: page + 1,
        limit
      };
    }

    if (startIndex > 0) {
      pagination.prev = {
        page: page - 1,
        limit
      };
    }

    res.status(200).json({
      success: true,
      count: products.length,
      pagination,
      data: products
    });
  } catch (error) {
    console.error('Error getting products:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Get a single product
// @route   GET /api/shop/products/:id
// @access  Public
exports.getProductById = async (req, res) => {
  try {
    const product = await Product.findById(req.params.id)
      .populate('seller', 'username avatar businessInfo')
      .populate('category', 'name');

    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }

    res.status(200).json({
      success: true,
      data: product
    });
  } catch (error) {
    console.error('Error getting product:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Get all products with filters and pagination
// @route   GET /api/shop/products
// @access  Public
exports.getProducts = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      category,
      minPrice,
      maxPrice,
      rating,
      inStock,
      sortBy = 'createdAt',
      sortOrder = 'desc',
      search
    } = req.query;

    // Build query
    let query = { isAvailable: true };

    if (category) query.category = category;
    if (minPrice || maxPrice) {
      query.price = {};
      if (minPrice) query.price.$gte = parseFloat(minPrice);
      if (maxPrice) query.price.$lte = parseFloat(maxPrice);
    }
    if (inStock === 'true') query.inventory = { $gt: 0 };
    if (search) {
      query.$text = { $search: search };
    }

    // Build sort object
    const sort = {};
    sort[sortBy] = sortOrder === 'desc' ? -1 : 1;

    // Execute query with pagination
    const products = await Product.find(query)
      .populate('user', 'name username profilePicture isVerified')
      .sort(sort)
      .limit(limit * 1)
      .skip((page - 1) * limit)
      .lean();

    // Get total count for pagination
    const total = await Product.countDocuments(query);

    // Add calculated fields
    const productsWithExtras = products.map(product => ({
      ...product,
      averageRating: product.averageRating || 0,
      reviewCount: product.reviewCount || 0,
      isInStock: product.inventory > 0,
      discountPercentage: product.originalPrice ?
        Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100) : 0
    }));

    res.status(200).json({
      success: true,
      data: productsWithExtras,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error getting products:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Get single product
// @route   GET /api/shop/products/:id
// @access  Public
exports.getProduct = async (req, res) => {
  try {
    const product = await Product.findById(req.params.id)
      .populate('user', 'name username profilePicture isVerified')
      .lean();

    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }

    // Add calculated fields
    const productWithExtras = {
      ...product,
      averageRating: product.averageRating || 0,
      reviewCount: product.reviewCount || 0,
      isInStock: product.inventory > 0,
      discountPercentage: product.originalPrice ?
        Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100) : 0
    };

    res.status(200).json({
      success: true,
      data: productWithExtras
    });
  } catch (error) {
    console.error('Error getting product:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Create a new product
// @route   POST /api/shop/products
// @access  Private (Business accounts only)
exports.createProduct = async (req, res) => {
  try {
    const user = await User.findById(req.user.id);

    // Check if user has a business account
    if (!user.isBusinessAccount && user.role !== 'vendor') {
      return res.status(403).json({
        success: false,
        message: 'Only business accounts can create products'
      });
    }

    // Create product
    const product = new Product({
      ...req.body,
      user: req.user.id
    });

    await product.save();

    res.status(201).json({
      success: true,
      data: product
    });
  } catch (error) {
    console.error('Error creating product:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Update a product
// @route   PUT /api/shop/products/:id
// @access  Private (Product owner only)
exports.updateProduct = async (req, res) => {
  try {
    let product = await Product.findById(req.params.id);

    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }

    // Check if user is the product owner
    if (product.user.toString() !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: 'Not authorized to update this product'
      });
    }

    // Update product
    product = await Product.findByIdAndUpdate(
      req.params.id,
      req.body,
      {
        new: true,
        runValidators: true
      }
    );

    res.status(200).json({
      success: true,
      data: product
    });
  } catch (error) {
    console.error('Error updating product:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Delete a product
// @route   DELETE /api/shop/products/:id
// @access  Private (Product owner only)
exports.deleteProduct = async (req, res) => {
  try {
    const product = await Product.findById(req.params.id);

    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }

    // Check if user is the product owner
    if (product.user.toString() !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: 'Not authorized to delete this product'
      });
    }

    await product.deleteOne();

    res.status(200).json({
      success: true,
      data: {}
    });
  } catch (error) {
    console.error('Error deleting product:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Get user's products (for vendors)
// @route   GET /api/shop/products/my-products
// @access  Private
exports.getMyProducts = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      status,
      category,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    // Build query
    let query = { user: req.user.id };

    if (status === 'active') query.isAvailable = true;
    if (status === 'inactive') query.isAvailable = false;
    if (category) query.category = category;

    // Build sort object
    const sort = {};
    sort[sortBy] = sortOrder === 'desc' ? -1 : 1;

    // Execute query with pagination
    const products = await Product.find(query)
      .populate('vendor', 'businessName storeSettings.storeName')
      .sort(sort)
      .limit(limit * 1)
      .skip((page - 1) * limit)
      .lean();

    // Get total count for pagination
    const total = await Product.countDocuments(query);

    // Add calculated fields
    const productsWithExtras = products.map(product => ({
      ...product,
      averageRating: product.averageRating || 0,
      reviewCount: product.reviewCount || 0,
      isInStock: product.stock > 0,
      discountPercentage: product.originalPrice ?
        Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100) : 0
    }));

    res.status(200).json({
      success: true,
      data: productsWithExtras,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error getting user products:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Get all categories
// @route   GET /api/shop/categories
// @access  Public
exports.getCategories = async (req, res) => {
  try {
    const categories = await Category.find().sort({ name: 1 });

    res.status(200).json({
      success: true,
      count: categories.length,
      data: categories
    });
  } catch (error) {
    console.error('Error getting categories:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Get products by category
// @route   GET /api/shop/categories/:id/products
// @access  Public
exports.getProductsByCategory = async (req, res) => {
  try {
    const category = await Category.findById(req.params.id);

    if (!category) {
      return res.status(404).json({
        success: false,
        message: 'Category not found'
      });
    }

    const products = await Product.find({ category: req.params.id })
      .sort({ createdAt: -1 })
      .populate('seller', 'username avatar businessInfo');

    res.status(200).json({
      success: true,
      count: products.length,
      data: products
    });
  } catch (error) {
    console.error('Error getting products by category:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Search products with auto-suggestions
// @route   GET /api/shop/products/search
// @access  Public
exports.searchProducts = async (req, res) => {
  try {
    const { q, suggestions = false, limit = 10 } = req.query;

    if (!q) {
      return res.status(400).json({
        success: false,
        message: 'Search query is required'
      });
    }

    if (suggestions === 'true') {
      // Return search suggestions
      const products = await Product.find(
        { $text: { $search: q }, isAvailable: true },
        { score: { $meta: 'textScore' } }
      )
        .sort({ score: { $meta: 'textScore' } })
        .limit(parseInt(limit))
        .select('name category')
        .lean();

      const suggestions = products.map(p => ({
        text: p.name,
        category: p.category,
        id: p._id
      }));

      return res.status(200).json({
        success: true,
        data: suggestions
      });
    }

    // Full search with pagination
    const products = await Product.find(
      { $text: { $search: q }, isAvailable: true },
      { score: { $meta: 'textScore' } }
    )
      .populate('user', 'name username profilePicture')
      .sort({ score: { $meta: 'textScore' } })
      .limit(parseInt(limit))
      .lean();

    res.status(200).json({
      success: true,
      data: products,
      count: products.length
    });
  } catch (error) {
    console.error('Error searching products:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Get trending products
// @route   GET /api/shop/products/trending
// @access  Public
exports.getTrendingProducts = async (req, res) => {
  try {
    const { limit = 10 } = req.query;

    const products = await Product.find({ isAvailable: true })
      .populate('user', 'name username profilePicture')
      .sort({ views: -1, purchases: -1, createdAt: -1 })
      .limit(parseInt(limit))
      .lean();

    res.status(200).json({
      success: true,
      data: products
    });
  } catch (error) {
    console.error('Error getting trending products:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Get AI-powered recommendations
// @route   GET /api/shop/recommendations
// @access  Private
exports.getRecommendedProducts = async (req, res) => {
  try {
    const userId = req.user.id;
    const { limit = 10, type = 'general' } = req.query;

    // Simple recommendation based on user's viewed products
    let query = { isAvailable: true };

    const recommendations = await Product.find(query)
      .populate('user', 'name username profilePicture')
      .sort({ views: -1, averageRating: -1 })
      .limit(parseInt(limit))
      .lean();

    res.status(200).json({
      success: true,
      data: recommendations,
      type,
      confidence: 85
    });
  } catch (error) {
    console.error('Error getting recommendations:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Get cart
// @route   GET /api/shop/cart
// @access  Private
exports.getCart = async (req, res) => {
  try {
    const cart = await Cart.findOne({ user: req.user.id })
      .populate('items.product')
      .lean();

    if (!cart) {
      return res.status(200).json({
        success: true,
        data: { items: [], total: 0, itemCount: 0 }
      });
    }

    // Calculate totals
    let total = 0;
    let itemCount = 0;

    cart.items.forEach(item => {
      if (item.product) {
        total += item.product.price * item.quantity;
        itemCount += item.quantity;
      }
    });

    res.status(200).json({
      success: true,
      data: {
        ...cart,
        total,
        itemCount
      }
    });
  } catch (error) {
    console.error('Error getting cart:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Add item to cart
// @route   POST /api/shop/cart
// @access  Private
exports.addToCart = async (req, res) => {
  try {
    const { productId, quantity = 1 } = req.body;

    // Check if product exists and is available
    const product = await Product.findById(productId);
    if (!product || !product.isAvailable) {
      return res.status(404).json({
        success: false,
        message: 'Product not found or unavailable'
      });
    }

    // Check inventory
    if (product.inventory < quantity) {
      return res.status(400).json({
        success: false,
        message: 'Insufficient inventory'
      });
    }

    let cart = await Cart.findOne({ user: req.user.id });

    if (!cart) {
      cart = new Cart({
        user: req.user.id,
        items: []
      });
    }

    // Check if item already exists in cart
    const existingItemIndex = cart.items.findIndex(
      item => item.product.toString() === productId
    );

    if (existingItemIndex > -1) {
      cart.items[existingItemIndex].quantity += quantity;
    } else {
      cart.items.push({
        product: productId,
        quantity
      });
    }

    await cart.save();
    await cart.populate('items.product');

    res.status(200).json({
      success: true,
      data: cart,
      message: 'Item added to cart'
    });
  } catch (error) {
    console.error('Error adding to cart:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Get wishlist
// @route   GET /api/shop/wishlist
// @access  Private
exports.getWishlist = async (req, res) => {
  try {
    const wishlist = await Wishlist.findOne({ user: req.user.id })
      .populate('products')
      .lean();

    if (!wishlist) {
      return res.status(200).json({
        success: true,
        data: { products: [] }
      });
    }

    res.status(200).json({
      success: true,
      data: wishlist
    });
  } catch (error) {
    console.error('Error getting wishlist:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Add item to wishlist
// @route   POST /api/shop/wishlist
// @access  Private
exports.addToWishlist = async (req, res) => {
  try {
    const { productId } = req.body;

    // Check if product exists
    const product = await Product.findById(productId);
    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }

    let wishlist = await Wishlist.findOne({ user: req.user.id });

    if (!wishlist) {
      wishlist = new Wishlist({
        user: req.user.id,
        products: []
      });
    }

    // Check if product already in wishlist
    if (!wishlist.products.includes(productId)) {
      wishlist.products.push(productId);
      await wishlist.save();
    }

    await wishlist.populate('products');

    res.status(200).json({
      success: true,
      data: wishlist,
      message: 'Item added to wishlist'
    });
  } catch (error) {
    console.error('Error adding to wishlist:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Remove item from wishlist
// @route   DELETE /api/shop/wishlist/:productId
// @access  Private
exports.removeFromWishlist = async (req, res) => {
  try {
    const wishlist = await Wishlist.findOne({ user: req.user.id });

    if (!wishlist) {
      return res.status(404).json({
        success: false,
        message: 'Wishlist not found'
      });
    }

    wishlist.products = wishlist.products.filter(
      product => product.toString() !== req.params.productId
    );

    await wishlist.save();
    await wishlist.populate('products');

    res.status(200).json({
      success: true,
      data: wishlist,
      message: 'Item removed from wishlist'
    });
  } catch (error) {
    console.error('Error removing from wishlist:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Update cart item quantity
// @route   PUT /api/shop/cart/:productId
// @access  Private
exports.updateCartItem = async (req, res) => {
  try {
    const { quantity } = req.body;
    const cart = await Cart.findOne({ user: req.user.id });

    if (!cart) {
      return res.status(404).json({
        success: false,
        message: 'Cart not found'
      });
    }

    const itemIndex = cart.items.findIndex(
      item => item.product.toString() === req.params.productId
    );

    if (itemIndex === -1) {
      return res.status(404).json({
        success: false,
        message: 'Item not found in cart'
      });
    }

    if (quantity <= 0) {
      cart.items.splice(itemIndex, 1);
    } else {
      cart.items[itemIndex].quantity = quantity;
    }

    await cart.save();
    await cart.populate('items.product');

    res.status(200).json({
      success: true,
      data: cart,
      message: 'Cart updated'
    });
  } catch (error) {
    console.error('Error updating cart:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Remove item from cart
// @route   DELETE /api/shop/cart/:productId
// @access  Private
exports.removeFromCart = async (req, res) => {
  try {
    const cart = await Cart.findOne({ user: req.user.id });

    if (!cart) {
      return res.status(404).json({
        success: false,
        message: 'Cart not found'
      });
    }

    cart.items = cart.items.filter(
      item => item.product.toString() !== req.params.productId
    );

    await cart.save();
    await cart.populate('items.product');

    res.status(200).json({
      success: true,
      data: cart,
      message: 'Item removed from cart'
    });
  } catch (error) {
    console.error('Error removing from cart:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Clear cart
// @route   DELETE /api/shop/cart
// @access  Private
exports.clearCart = async (req, res) => {
  try {
    const cart = await Cart.findOne({ user: req.user.id });

    if (!cart) {
      return res.status(404).json({
        success: false,
        message: 'Cart not found'
      });
    }

    cart.items = [];
    await cart.save();

    res.status(200).json({
      success: true,
      data: cart,
      message: 'Cart cleared'
    });
  } catch (error) {
    console.error('Error clearing cart:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Initiate checkout
// @route   POST /api/shop/checkout
// @access  Private
exports.initiateCheckout = async (req, res) => {
  try {
    const { shippingAddress, paymentMethod, items } = req.body;

    // Validate cart items
    let total = 0;
    const orderItems = [];

    for (const item of items) {
      const product = await Product.findById(item.productId);
      if (!product || !product.isAvailable) {
        return res.status(400).json({
          success: false,
          message: `Product ${product?.name || 'unknown'} is not available`
        });
      }

      if (product.inventory < item.quantity) {
        return res.status(400).json({
          success: false,
          message: `Insufficient inventory for ${product.name}`
        });
      }

      const itemTotal = product.price * item.quantity;
      total += itemTotal;

      orderItems.push({
        product: product._id,
        quantity: item.quantity,
        price: product.price,
        total: itemTotal
      });
    }

    // Calculate tax and shipping (simplified)
    const tax = total * 0.08; // 8% tax
    const shipping = total > 50 ? 0 : 9.99; // Free shipping over $50
    const finalTotal = total + tax + shipping;

    // Create order
    const order = new Order({
      user: req.user.id,
      items: orderItems,
      shippingAddress,
      paymentMethod,
      subtotal: total,
      tax,
      shipping,
      total: finalTotal,
      status: 'pending',
      paymentStatus: 'pending'
    });

    await order.save();

    res.status(201).json({
      success: true,
      data: order,
      message: 'Checkout initiated successfully'
    });
  } catch (error) {
    console.error('Error initiating checkout:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Process payment and complete order
// @route   POST /api/shop/payment/process
// @access  Private
exports.processPayment = async (req, res) => {
  try {
    const { orderId, paymentDetails } = req.body;

    const order = await Order.findById(orderId).populate('items.product');
    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Order not found'
      });
    }

    if (order.user.toString() !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: 'Not authorized to process this order'
      });
    }

    // Simulate payment processing
    const paymentSuccess = true; // In real app, integrate with payment gateway

    if (paymentSuccess) {
      // Update order status
      order.status = 'confirmed';
      order.paymentStatus = 'paid';
      order.transactionId = `txn_${Date.now()}`;
      await order.save();

      // Update product inventory
      for (const item of order.items) {
        await Product.findByIdAndUpdate(
          item.product._id,
          { $inc: { inventory: -item.quantity, purchases: item.quantity } }
        );
      }

      // Calculate and create commissions for each item
      for (const item of order.items) {
        try {
          await paymentsPayoutsService.calculateCommission(order._id);
        } catch (commissionError) {
          console.error('Error calculating commission:', commissionError);
          // Don't fail the order if commission calculation fails
        }
      }

      // Clear user's cart
      await Cart.findOneAndUpdate(
        { user: req.user.id },
        { items: [] }
      );

      res.status(200).json({
        success: true,
        data: order,
        message: 'Payment processed successfully'
      });
    } else {
      order.paymentStatus = 'failed';
      await order.save();

      res.status(400).json({
        success: false,
        message: 'Payment processing failed'
      });
    }
  } catch (error) {
    console.error('Error processing payment:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Get order history
// @route   GET /api/shop/orders
// @access  Private
exports.getOrderHistory = async (req, res) => {
  try {
    const { page = 1, limit = 10, status } = req.query;

    let query = { user: req.user.id };
    if (status) query.status = status;

    const orders = await Order.find(query)
      .populate('items.product', 'name images price')
      .sort({ createdAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit);

    const total = await Order.countDocuments(query);

    res.status(200).json({
      success: true,
      data: orders,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error getting order history:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Get single order
// @route   GET /api/shop/orders/:id
// @access  Private
exports.getOrder = async (req, res) => {
  try {
    const order = await Order.findById(req.params.id)
      .populate('items.product', 'name images price')
      .populate('user', 'name email');

    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Order not found'
      });
    }

    if (order.user._id.toString() !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: 'Not authorized to view this order'
      });
    }

    res.status(200).json({
      success: true,
      data: order
    });
  } catch (error) {
    console.error('Error getting order:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Track order
// @route   GET /api/shop/orders/:id/track
// @access  Private
exports.trackOrder = async (req, res) => {
  try {
    const order = await Order.findById(req.params.id);

    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Order not found'
      });
    }

    if (order.user.toString() !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: 'Not authorized to track this order'
      });
    }

    const trackingInfo = {
      orderNumber: order.orderNumber,
      status: order.status,
      trackingNumber: order.trackingNumber,
      estimatedDelivery: order.estimatedDelivery,
      deliveredAt: order.deliveredAt,
      timeline: [
        { status: 'pending', date: order.createdAt, description: 'Order placed' },
        { status: 'confirmed', date: order.updatedAt, description: 'Order confirmed' }
      ]
    };

    if (order.status === 'shipped') {
      trackingInfo.timeline.push({
        status: 'shipped',
        date: order.updatedAt,
        description: 'Order shipped'
      });
    }

    if (order.status === 'delivered') {
      trackingInfo.timeline.push({
        status: 'delivered',
        date: order.deliveredAt,
        description: 'Order delivered'
      });
    }

    res.status(200).json({
      success: true,
      data: trackingInfo
    });
  } catch (error) {
    console.error('Error tracking order:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// Additional helper functions for the shop
exports.getRecentlyViewed = async (req, res) => {
  try {
    // Implementation for recently viewed products
    res.status(200).json({
      success: true,
      data: [],
      message: 'Recently viewed products retrieved'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

exports.addToRecentlyViewed = async (req, res) => {
  try {
    // Implementation for adding to recently viewed
    res.status(200).json({
      success: true,
      message: 'Added to recently viewed'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

exports.getProductFilters = async (req, res) => {
  try {
    // Implementation for product filters
    res.status(200).json({
      success: true,
      data: {
        categories: [],
        priceRanges: [],
        brands: []
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

exports.bulkAddToCart = async (req, res) => {
  try {
    // Implementation for bulk add to cart
    res.status(200).json({
      success: true,
      message: 'Items added to cart'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

exports.validateCart = async (req, res) => {
  try {
    // Implementation for cart validation
    res.status(200).json({
      success: true,
      data: { valid: true }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

exports.getCartSummary = async (req, res) => {
  try {
    // Implementation for cart summary
    res.status(200).json({
      success: true,
      data: { total: 0, items: 0 }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

exports.applyPromoCode = async (req, res) => {
  try {
    // Implementation for promo code
    res.status(200).json({
      success: true,
      message: 'Promo code applied'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

exports.removePromoCode = async (req, res) => {
  try {
    // Implementation for removing promo code
    res.status(200).json({
      success: true,
      message: 'Promo code removed'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

exports.getSavedAddresses = async (req, res) => {
  try {
    // Implementation for saved addresses
    res.status(200).json({
      success: true,
      data: []
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

exports.addSavedAddress = async (req, res) => {
  try {
    // Implementation for adding saved address
    res.status(200).json({
      success: true,
      message: 'Address saved'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

exports.updateSavedAddress = async (req, res) => {
  try {
    // Implementation for updating saved address
    res.status(200).json({
      success: true,
      message: 'Address updated'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

exports.deleteSavedAddress = async (req, res) => {
  try {
    // Implementation for deleting saved address
    res.status(200).json({
      success: true,
      message: 'Address deleted'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

exports.getSavedPaymentMethods = async (req, res) => {
  try {
    // Implementation for saved payment methods
    res.status(200).json({
      success: true,
      data: []
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

exports.addSavedPaymentMethod = async (req, res) => {
  try {
    // Implementation for adding saved payment method
    res.status(200).json({
      success: true,
      message: 'Payment method saved'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

exports.deleteSavedPaymentMethod = async (req, res) => {
  try {
    // Implementation for deleting saved payment method
    res.status(200).json({
      success: true,
      message: 'Payment method deleted'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

exports.getDeliveryOptions = async (req, res) => {
  try {
    // Implementation for delivery options
    res.status(200).json({
      success: true,
      data: [
        { id: 'standard', name: 'Standard Delivery', price: 9.99, days: '3-5' },
        { id: 'express', name: 'Express Delivery', price: 19.99, days: '1-2' },
        { id: 'overnight', name: 'Overnight Delivery', price: 39.99, days: '1' }
      ]
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

exports.calculateShipping = async (req, res) => {
  try {
    // Implementation for shipping calculation
    res.status(200).json({
      success: true,
      data: { cost: 9.99, method: 'standard' }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

exports.getProductSuggestions = async (req, res) => {
  try {
    // Implementation for product suggestions
    res.status(200).json({
      success: true,
      data: []
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

exports.compareProducts = async (req, res) => {
  try {
    // Implementation for product comparison
    res.status(200).json({
      success: true,
      data: []
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

exports.getProductReviews = async (req, res) => {
  try {
    // Implementation for product reviews
    res.status(200).json({
      success: true,
      data: []
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

exports.createProductReview = async (req, res) => {
  try {
    // Implementation for creating product review
    res.status(200).json({
      success: true,
      message: 'Review created'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

exports.incrementProductViews = async (req, res) => {
  try {
    // Implementation for incrementing product views
    res.status(200).json({
      success: true,
      message: 'View count updated'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

exports.getRelatedProducts = async (req, res) => {
  try {
    // Implementation for related products
    res.status(200).json({
      success: true,
      data: []
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

exports.getProductDeals = async (req, res) => {
  try {
    // Implementation for product deals
    res.status(200).json({
      success: true,
      data: []
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

exports.getFlashDeals = async (req, res) => {
  try {
    // Implementation for flash deals
    res.status(200).json({
      success: true,
      data: []
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};