/**
 * Async handler to wrap async route handlers and middleware
 * This eliminates the need for try/catch blocks in route handlers
 * @param {Function} fn - The async function to wrap
 * @returns {Function} - The wrapped function
 */
const asyncHandler = (fn) => (req, res, next) => {
  Promise.resolve(fn(req, res, next)).catch((err) => {
    console.error('Async<PERSON><PERSON><PERSON> caught an error:', err);
    if (!res.headersSent) {
      next(err);
    } else {
      console.error('Headers already sent, cannot pass error to next middleware');
    }
  });
};

module.exports = asyncHandler;
