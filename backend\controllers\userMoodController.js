const UserMood = require('../models/UserMood');
const MoodComment = require('../models/MoodComment');
const Like = require('../models/Like');
const User = require('../models/User');
const { createError } = require('../utils/error');
const socketEmitter = require('../utils/socketEmitter');
const mongoose = require('mongoose');

/**
 * Get all user moods with pagination and filtering
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.getUserMoods = async (req, res, next) => {
  try {
    const { page = 1, limit = 10, category, user: userId, popular } = req.query;
    const skip = (parseInt(page) - 1) * parseInt(limit);

    // Build query
    let query = {};

    // Add category filter if provided
    if (category && category !== 'all') {
      query.category = category;
    }

    // Add user filter if provided
    if (userId) {
      query.user = userId;
    }

    // Sort options
    let sortOptions = { createdAt: -1 }; // Default sort by newest
    if (popular === 'true') {
      sortOptions = { usageCount: -1 }; // Sort by popularity
    }

    // Find user moods
    const userMoods = await UserMood.find(query)
      .sort(sortOptions)
      .skip(skip)
      .limit(parseInt(limit))
      .populate('user', 'username name profilePicture')
      .populate('relatedEmotions', 'name color icon');

    // Get total count
    const total = await UserMood.countDocuments(query);

    // Check if user has liked each mood
    const moodsWithLikeStatus = await Promise.all(
      userMoods.map(async (mood) => {
        const moodObj = mood.toObject();

        if (req.user) {
          const isLiked = await Like.exists({
            user: req.user.id,
            mood: mood._id,
          });

          moodObj.isLiked = !!isLiked;
        } else {
          moodObj.isLiked = false;
        }

        return moodObj;
      })
    );

    res.status(200).json({
      success: true,
      count: userMoods.length,
      total,
      totalPages: Math.ceil(total / parseInt(limit)),
      currentPage: parseInt(page),
      data: moodsWithLikeStatus,
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Get a single user mood by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.getUserMood = async (req, res, next) => {
  try {
    const { id } = req.params;

    // Find user mood
    const userMood = await UserMood.findById(id)
      .populate('user', 'username name profilePicture')
      .populate('relatedEmotions', 'name color icon');

    if (!userMood) {
      return next(createError(404, 'User mood not found'));
    }

    // Check if user has liked the mood
    let isLiked = false;
    if (req.user) {
      const like = await Like.exists({
        user: req.user.id,
        mood: userMood._id,
      });
      isLiked = !!like;
    }

    // Convert to object and add like status
    const moodObj = userMood.toObject();
    moodObj.isLiked = isLiked;

    res.status(200).json({
      success: true,
      data: moodObj,
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Create a new user mood
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.createUserMood = async (req, res, next) => {
  try {
    const { name, description, category, icon, color, relatedEmotions, isPublic } = req.body;

    // Create user mood
    const userMood = await UserMood.create({
      user: req.user.id,
      name,
      description,
      category,
      icon,
      color,
      relatedEmotions: relatedEmotions || [],
      isPublic: isPublic !== undefined ? isPublic : true,
    });

    // Populate user details
    await userMood.populate('user', 'username name profilePicture');
    await userMood.populate('relatedEmotions', 'name color icon');

    res.status(201).json({
      success: true,
      data: userMood,
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Update a user mood
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.updateUserMood = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { name, description, category, icon, color, relatedEmotions, isPublic } = req.body;

    // Find user mood
    let userMood = await UserMood.findById(id);

    if (!userMood) {
      return next(createError(404, 'User mood not found'));
    }

    // Check if user is the owner
    if (userMood.user.toString() !== req.user.id) {
      return next(createError(403, 'Not authorized to update this mood'));
    }

    // Update user mood
    userMood = await UserMood.findByIdAndUpdate(
      id,
      {
        name,
        description,
        category,
        icon,
        color,
        relatedEmotions: relatedEmotions || userMood.relatedEmotions,
        isPublic: isPublic !== undefined ? isPublic : userMood.isPublic,
      },
      { new: true }
    )
      .populate('user', 'username name profilePicture')
      .populate('relatedEmotions', 'name color icon');

    res.status(200).json({
      success: true,
      data: userMood,
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Delete a user mood
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.deleteUserMood = async (req, res, next) => {
  try {
    const { id } = req.params;

    // Find user mood
    const userMood = await UserMood.findById(id);

    if (!userMood) {
      return next(createError(404, 'User mood not found'));
    }

    // Check if user is the owner
    if (userMood.user.toString() !== req.user.id) {
      return next(createError(403, 'Not authorized to delete this mood'));
    }

    // Delete all comments associated with this mood
    await MoodComment.deleteMany({ mood: id });

    // Delete all likes associated with this mood
    await Like.deleteMany({ mood: id });

    // Delete the mood
    await userMood.deleteOne();

    res.status(200).json({
      success: true,
      data: {},
    });
  } catch (err) {
    next(err);
  }
};
