/**
 * Utility functions for emitting socket events
 */

let io;

/**
 * Initialize the socket emitter with the Socket.IO instance
 * @param {Object} socketIo - Socket.IO server instance
 */
const initialize = (socketIo) => {
  io = socketIo;
};

/**
 * Emit a notification to a specific user
 * @param {string} userId - The ID of the user to notify
 * @param {Object} notification - The notification object
 */
const emitNotification = (userId, notification) => {
  if (!io) {
    console.error('Socket.IO not initialized');
    return;
  }

  io.to(`user:${userId}`).emit('notification', notification);
};

/**
 * Emit a like event to all connected clients
 * @param {string} userId - The ID of the user who liked
 * @param {string} itemType - The type of item liked (post, reel, comment)
 * @param {string} itemId - The ID of the liked item
 * @param {string} ownerId - The ID of the item's owner
 */
const emitLike = (userId, itemType, itemId, ownerId) => {
  if (!io) {
    console.error('Socket.IO not initialized');
    return;
  }

  // Emit to all clients except the liker
  io.emit('post_liked', {
    userId,
    itemType,
    itemId,
    postUserId: ownerId,
    timestamp: new Date(),
  });
};

/**
 * Emit a comment event to all connected clients
 * @param {string} userId - The ID of the user who commented
 * @param {string} itemType - The type of item commented on (post, reel)
 * @param {string} itemId - The ID of the commented item
 * @param {string} commentId - The ID of the comment
 * @param {string} ownerId - The ID of the item's owner
 */
const emitComment = (userId, itemType, itemId, commentId, ownerId) => {
  if (!io) {
    console.error('Socket.IO not initialized');
    return;
  }

  // Emit to all clients except the commenter
  io.emit('post_commented', {
    userId,
    itemType,
    itemId,
    commentId,
    postUserId: ownerId,
    timestamp: new Date(),
  });
};

/**
 * Emit a follow event to all connected clients
 * @param {string} sourceUserId - The ID of the user who followed
 * @param {string} targetUserId - The ID of the user who was followed
 */
const emitFollow = (sourceUserId, targetUserId) => {
  if (!io) {
    console.error('Socket.IO not initialized');
    return;
  }

  // Emit to all clients
  io.emit('user_followed', {
    sourceUserId,
    targetUserId,
    timestamp: new Date(),
  });
};

/**
 * Emit a story view event to the story owner
 * @param {string} viewerId - The ID of the user who viewed the story
 * @param {string} storyId - The ID of the story
 * @param {string} ownerId - The ID of the story's owner
 */
const emitStoryView = (viewerId, storyId, ownerId) => {
  if (!io) {
    console.error('Socket.IO not initialized');
    return;
  }

  io.to(`user:${ownerId}`).emit('story_viewed', {
    viewerId,
    storyId,
    timestamp: new Date(),
  });
};

/**
 * Emit a notification read event to a user's other devices
 * @param {string} userId - The ID of the user
 * @param {string} notificationId - The ID of the notification
 */
const emitNotificationRead = (userId, notificationId) => {
  if (!io) {
    console.error('Socket.IO not initialized');
    return;
  }

  io.to(`user:${userId}`).emit('notification_read', {
    notificationId,
    userId,
  });
};

/**
 * Emit an all notifications read event to a user's other devices
 * @param {string} userId - The ID of the user
 */
const emitAllNotificationsRead = (userId) => {
  if (!io) {
    console.error('Socket.IO not initialized');
    return;
  }

  io.to(`user:${userId}`).emit('notification_read', {
    allRead: true,
    userId,
  });
};

/**
 * Emit a new message to all participants in a conversation
 * @param {Object} message - The message object
 */
const emitNewMessage = (message) => {
  if (!io) {
    console.error('Socket.IO not initialized');
    return;
  }

  // Emit to the conversation room
  io.to(`conversation:${message.conversation.toString()}`).emit('new_message', message);
};

/**
 * Emit a message read status to all participants in a conversation
 * @param {string} conversationId - The ID of the conversation
 * @param {string} userId - The ID of the user who read the messages
 */
const emitMessagesRead = (conversationId, userId) => {
  if (!io) {
    console.error('Socket.IO not initialized');
    return;
  }

  io.to(`conversation:${conversationId}`).emit('messages_read', {
    conversationId,
    userId,
    timestamp: new Date(),
  });
};

/**
 * Emit a message deleted event to all participants in a conversation
 * @param {string} conversationId - The ID of the conversation
 * @param {string} messageId - The ID of the deleted message
 */
const emitMessageDeleted = (conversationId, messageId) => {
  if (!io) {
    console.error('Socket.IO not initialized');
    return;
  }

  io.to(`conversation:${conversationId}`).emit('message_deleted', {
    conversationId,
    messageId,
    timestamp: new Date(),
  });
};

/**
 * Emit a message reaction event to all participants in a conversation
 * @param {string} conversationId - The ID of the conversation
 * @param {string} messageId - The ID of the message
 * @param {string} userId - The ID of the user who reacted
 * @param {string} emoji - The emoji reaction
 */
const emitMessageReaction = (conversationId, messageId, userId, emoji) => {
  if (!io) {
    console.error('Socket.IO not initialized');
    return;
  }

  io.to(`conversation:${conversationId}`).emit('message_reaction', {
    conversationId,
    messageId,
    userId,
    emoji,
    timestamp: new Date(),
  });
};

/**
 * Emit a message reaction removed event to all participants in a conversation
 * @param {string} conversationId - The ID of the conversation
 * @param {string} messageId - The ID of the message
 * @param {string} userId - The ID of the user who removed the reaction
 */
const emitMessageReactionRemoved = (conversationId, messageId, userId) => {
  if (!io) {
    console.error('Socket.IO not initialized');
    return;
  }

  io.to(`conversation:${conversationId}`).emit('message_reaction_removed', {
    conversationId,
    messageId,
    userId,
    timestamp: new Date(),
  });
};

/**
 * Emit a typing indicator event to all participants in a conversation
 * @param {string} conversationId - The ID of the conversation
 * @param {string} userId - The ID of the user who is typing
 * @param {boolean} isTyping - Whether the user is typing or stopped typing
 */
const emitTypingIndicator = (conversationId, userId, isTyping) => {
  if (!io) {
    console.error('Socket.IO not initialized');
    return;
  }

  io.to(`conversation:${conversationId}`).emit('typing', {
    conversationId,
    userId,
    isTyping,
    timestamp: new Date(),
  });
};

/**
 * Emit a message pinned event to all participants in a conversation
 * @param {string} conversationId - The ID of the conversation
 * @param {string} messageId - The ID of the pinned message
 */
const emitMessagePinned = (conversationId, messageId) => {
  if (!io) {
    console.error('Socket.IO not initialized');
    return;
  }

  io.to(`conversation:${conversationId}`).emit('message_pinned', {
    conversationId,
    messageId,
    timestamp: new Date(),
  });
};

/**
 * Emit a message unpinned event to all participants in a conversation
 * @param {string} conversationId - The ID of the conversation
 * @param {string} messageId - The ID of the unpinned message
 */
const emitMessageUnpinned = (conversationId, messageId) => {
  if (!io) {
    console.error('Socket.IO not initialized');
    return;
  }

  io.to(`conversation:${conversationId}`).emit('message_unpinned', {
    conversationId,
    messageId,
    timestamp: new Date(),
  });
};

/**
 * Emit a poll updated event to all participants in a conversation
 * @param {string} conversationId - The ID of the conversation
 * @param {string} messageId - The ID of the poll message
 */
const emitPollUpdated = (conversationId, messageId) => {
  if (!io) {
    console.error('Socket.IO not initialized');
    return;
  }

  io.to(`conversation:${conversationId}`).emit('poll_updated', {
    conversationId,
    messageId,
    timestamp: new Date(),
  });
};

/**
 * Emit a command processed event to all participants in a conversation
 * @param {string} conversationId - The ID of the conversation
 * @param {string} messageId - The ID of the command message
 * @param {Object} result - The result of the command processing
 */
const emitCommandProcessed = (conversationId, messageId, result) => {
  if (!io) {
    console.error('Socket.IO not initialized');
    return;
  }

  io.to(`conversation:${conversationId}`).emit('command_processed', {
    conversationId,
    messageId,
    result,
    timestamp: new Date(),
  });
};

/**
 * Emit a group created event to all participants
 * @param {Object} conversation - The conversation object
 */
const emitGroupCreated = (conversation) => {
  if (!io) {
    console.error('Socket.IO not initialized');
    return;
  }

  // Emit to each participant's personal room
  conversation.participants.forEach(participantId => {
    io.to(`user:${participantId.toString()}`).emit('group_created', conversation);
  });
};

/**
 * Emit a group updated event to all participants
 * @param {Object} conversation - The conversation object
 */
const emitGroupUpdated = (conversation) => {
  if (!io) {
    console.error('Socket.IO not initialized');
    return;
  }

  io.to(`conversation:${conversation._id.toString()}`).emit('group_updated', conversation);
};

/**
 * Emit a participants added event to all participants
 * @param {Object} conversation - The conversation object
 * @param {Array} newParticipants - Array of new participant IDs
 * @param {string} addedBy - ID of the user who added the participants
 */
const emitParticipantsAdded = (conversation, newParticipants, addedBy) => {
  if (!io) {
    console.error('Socket.IO not initialized');
    return;
  }

  // Emit to the conversation room
  io.to(`conversation:${conversation._id.toString()}`).emit('participants_added', {
    conversation,
    newParticipants,
    addedBy,
    timestamp: new Date(),
  });

  // Emit to each new participant's personal room
  newParticipants.forEach(participantId => {
    io.to(`user:${participantId.toString()}`).emit('added_to_group', conversation);
  });
};

/**
 * Emit a participant removed event to all participants
 * @param {Object} conversation - The conversation object
 * @param {string} removedUserId - ID of the removed user
 * @param {string} removedBy - ID of the user who removed the participant
 */
const emitParticipantRemoved = (conversation, removedUserId, removedBy) => {
  if (!io) {
    console.error('Socket.IO not initialized');
    return;
  }

  // Emit to the conversation room
  io.to(`conversation:${conversation._id.toString()}`).emit('participant_removed', {
    conversation,
    removedUserId,
    removedBy,
    timestamp: new Date(),
  });

  // Emit to the removed user's personal room
  io.to(`user:${removedUserId}`).emit('removed_from_group', {
    conversationId: conversation._id.toString(),
    removedBy,
    timestamp: new Date(),
  });
};

/**
 * Emit a participant left event to all participants
 * @param {Object} conversation - The conversation object
 * @param {string} userId - ID of the user who left
 */
const emitParticipantLeft = (conversation, userId) => {
  if (!io) {
    console.error('Socket.IO not initialized');
    return;
  }

  io.to(`conversation:${conversation._id.toString()}`).emit('participant_left', {
    conversation,
    userId,
    timestamp: new Date(),
  });
};

/**
 * Emit a participant role changed event to all participants
 * @param {Object} conversation - The conversation object
 * @param {string} userId - ID of the user whose role changed
 * @param {string} role - The new role
 * @param {string} changedBy - ID of the user who changed the role
 */
const emitParticipantRoleChanged = (conversation, userId, role, changedBy) => {
  if (!io) {
    console.error('Socket.IO not initialized');
    return;
  }

  io.to(`conversation:${conversation._id.toString()}`).emit('participant_role_changed', {
    conversation,
    userId,
    role,
    changedBy,
    timestamp: new Date(),
  });
};

/**
 * Emit a new live stream event to all connected clients
 * @param {Object} stream - The live stream object
 */
const emitNewLiveStream = (stream) => {
  if (!io) {
    console.error('Socket.IO not initialized');
    return;
  }

  // Emit to all clients
  io.emit('new_live_stream', stream);
};

/**
 * Emit a live stream updated event to all connected clients
 * @param {Object} stream - The updated live stream object
 */
const emitLiveStreamUpdated = (stream) => {
  if (!io) {
    console.error('Socket.IO not initialized');
    return;
  }

  // Emit to all clients
  io.emit('live_stream_updated', stream);

  // Also emit to the stream room
  io.to(`stream:${stream._id.toString()}`).emit('stream_updated', stream);
};

/**
 * Emit a live stream deleted event to all connected clients
 * @param {string} streamId - The ID of the deleted live stream
 */
const emitLiveStreamDeleted = (streamId) => {
  if (!io) {
    console.error('Socket.IO not initialized');
    return;
  }

  // Emit to all clients
  io.emit('live_stream_deleted', { streamId });

  // Also emit to the stream room
  io.to(`stream:${streamId.toString()}`).emit('stream_deleted', { streamId });
};

/**
 * Emit a live stream started event to all connected clients
 * @param {Object} stream - The live stream object
 */
const emitLiveStreamStarted = (stream) => {
  if (!io) {
    console.error('Socket.IO not initialized');
    return;
  }

  // Emit to all clients
  io.emit('live_stream_started', stream);

  // Also emit to the stream room
  io.to(`stream:${stream._id.toString()}`).emit('stream_started', stream);
};

/**
 * Emit a live stream ended event to all connected clients
 * @param {Object} stream - The live stream object
 */
const emitLiveStreamEnded = (stream) => {
  if (!io) {
    console.error('Socket.IO not initialized');
    return;
  }

  // Emit to all clients
  io.emit('live_stream_ended', stream);

  // Also emit to the stream room
  io.to(`stream:${stream._id.toString()}`).emit('stream_ended', stream);
};

/**
 * Emit a content moderation event to moderators and content owner
 * @param {Object} moderation - The content moderation object
 */
const emitContentModerated = (moderation) => {
  if (!io) {
    console.error('Socket.IO not initialized');
    return;
  }

  // Emit to content owner
  io.to(`user:${moderation.user.toString()}`).emit('content_moderated', moderation);

  // Emit to admin room
  io.to('admin').emit('content_moderated', moderation);

  // If this is a stream chat message, emit to the stream room
  if (moderation.contentType === 'stream_chat') {
    io.to(`stream:${moderation.contentId.toString()}`).emit('chat_message_moderated', moderation);
  }
};

/**
 * Emit a user blocked event
 * @param {string} userId - The ID of the user who blocked
 * @param {string} targetUserId - The ID of the blocked user
 */
const emitUserBlocked = (userId, targetUserId) => {
  if (!io) {
    console.error('Socket.IO not initialized');
    return;
  }

  // Emit to the user who blocked
  io.to(`user:${userId}`).emit('user_blocked', { targetUserId });
};

/**
 * Emit a user unblocked event
 * @param {string} userId - The ID of the user who unblocked
 * @param {string} targetUserId - The ID of the unblocked user
 */
const emitUserUnblocked = (userId, targetUserId) => {
  if (!io) {
    console.error('Socket.IO not initialized');
    return;
  }

  // Emit to the user who unblocked
  io.to(`user:${userId}`).emit('user_unblocked', { targetUserId });
};

/**
 * Emit a moderation settings updated event
 * @param {string} streamId - The ID of the stream
 * @param {Object} settings - The updated moderation settings
 */
const emitModerationSettingsUpdated = (streamId, settings) => {
  if (!io) {
    console.error('Socket.IO not initialized');
    return;
  }

  // Emit to the stream room
  io.to(`stream:${streamId}`).emit('moderation_settings_updated', settings);
};

/**
 * Emit a moderator added event
 * @param {string} streamId - The ID of the stream
 * @param {string} userId - The ID of the added moderator
 */
const emitModeratorAdded = (streamId, userId) => {
  if (!io) {
    console.error('Socket.IO not initialized');
    return;
  }

  // Emit to the stream room
  io.to(`stream:${streamId}`).emit('moderator_added', { streamId, userId });

  // Emit to the new moderator
  io.to(`user:${userId}`).emit('moderator_role_assigned', { streamId });
};

/**
 * Emit a moderator removed event
 * @param {string} streamId - The ID of the stream
 * @param {string} userId - The ID of the removed moderator
 */
const emitModeratorRemoved = (streamId, userId) => {
  if (!io) {
    console.error('Socket.IO not initialized');
    return;
  }

  // Emit to the stream room
  io.to(`stream:${streamId}`).emit('moderator_removed', { streamId, userId });

  // Emit to the removed moderator
  io.to(`user:${userId}`).emit('moderator_role_removed', { streamId });
};

/**
 * Emit a live stream chat message to all viewers
 * @param {Object} message - The chat message object
 */
const emitLiveStreamChatMessage = (message) => {
  if (!io) {
    console.error('Socket.IO not initialized');
    return;
  }

  // Emit to the stream room
  io.to(`stream:${message.stream.toString()}`).emit('stream_chat_message', message);
};

/**
 * Emit a live stream chat message deleted event to all viewers
 * @param {string} messageId - The ID of the deleted message
 * @param {string} streamId - The ID of the live stream
 */
const emitLiveStreamChatMessageDeleted = (messageId, streamId) => {
  if (!io) {
    console.error('Socket.IO not initialized');
    return;
  }

  // Emit to the stream room
  io.to(`stream:${streamId}`).emit('stream_chat_message_deleted', { messageId, streamId });
};

/**
 * Emit a live stream chat message pinned event to all viewers
 * @param {Object} message - The pinned message object
 * @param {string} streamId - The ID of the live stream
 */
const emitLiveStreamChatMessagePinned = (message, streamId) => {
  if (!io) {
    console.error('Socket.IO not initialized');
    return;
  }

  // Emit to the stream room
  io.to(`stream:${streamId}`).emit('stream_chat_message_pinned', message);
};

/**
 * Emit a live stream viewer joined event to all viewers
 * @param {Object} viewer - The viewer object
 * @param {string} streamId - The ID of the live stream
 * @param {number} activeViewers - The current number of active viewers
 */
const emitLiveStreamViewerJoined = (viewer, streamId, activeViewers) => {
  if (!io) {
    console.error('Socket.IO not initialized');
    return;
  }

  // Emit to the stream room
  io.to(`stream:${streamId}`).emit('stream_viewer_joined', {
    viewer,
    streamId,
    activeViewers,
    timestamp: new Date(),
  });
};

/**
 * Emit a live stream viewer left event to all viewers
 * @param {string} viewerId - The ID of the viewer record
 * @param {string} userId - The ID of the user who left
 * @param {string} streamId - The ID of the live stream
 * @param {number} activeViewers - The current number of active viewers
 */
const emitLiveStreamViewerLeft = (viewerId, userId, streamId, activeViewers) => {
  if (!io) {
    console.error('Socket.IO not initialized');
    return;
  }

  // Emit to the stream room
  io.to(`stream:${streamId}`).emit('stream_viewer_left', {
    viewerId,
    userId,
    streamId,
    activeViewers,
    timestamp: new Date(),
  });
};

/**
 * Emit a live stream reaction event to all viewers
 * @param {Object} reaction - The reaction object
 */
const emitLiveStreamReaction = (reaction) => {
  if (!io) {
    console.error('Socket.IO not initialized');
    return;
  }

  // Emit to the stream room
  io.to(`stream:${reaction.stream.toString()}`).emit('stream_reaction', reaction);
};

/**
 * Emit a live stream reaction deleted event to all viewers
 * @param {string} reactionId - The ID of the deleted reaction
 * @param {string} streamId - The ID of the live stream
 */
const emitLiveStreamReactionDeleted = (reactionId, streamId) => {
  if (!io) {
    console.error('Socket.IO not initialized');
    return;
  }

  // Emit to the stream room
  io.to(`stream:${streamId}`).emit('stream_reaction_deleted', { reactionId, streamId });
};

/**
 * Emit a gift sent event to all viewers
 * @param {Object} giftData - The gift data object
 */
const emitGift = (giftData) => {
  if (!io) {
    console.error('Socket.IO not initialized');
    return;
  }

  // Emit to the stream room
  io.to(`stream:${giftData.streamId}`).emit('stream_gift', giftData);
};

/**
 * Emit a flash deal event to all viewers
 * @param {Object} dealData - The deal data object
 */
const emitFlashDeal = (dealData) => {
  if (!io) {
    console.error('Socket.IO not initialized');
    return;
  }

  // Emit to the stream room
  io.to(`stream:${dealData.streamId}`).emit('stream_flash_deal', dealData);
};

/**
 * Emit a NFT minted event to all viewers
 * @param {Object} nftData - The NFT data object
 */
const emitNFTMinted = (nftData) => {
  if (!io) {
    console.error('Socket.IO not initialized');
    return;
  }

  // Emit to the stream room
  io.to(`stream:${nftData.streamId}`).emit('stream_nft_minted', nftData);
};

/**
 * Emit a sponsor added event to all viewers
 * @param {Object} sponsorData - The sponsor data object
 */
const emitSponsorAdded = (sponsorData) => {
  if (!io) {
    console.error('Socket.IO not initialized');
    return;
  }

  // Emit to the stream room
  io.to(`stream:${sponsorData.streamId}`).emit('stream_sponsor_added', sponsorData);
};

/**
 * Emit a WebRTC offer to a specific user
 * @param {Object} data - The offer data
 */
const emitWebRTCOffer = (data) => {
  if (!io) {
    console.error('Socket.IO not initialized');
    return;
  }

  try {
    // Extract candidate type for logging if available
    let offerType = 'unknown';
    if (data.offer && data.offer.type) {
      offerType = data.offer.type;
    }

    console.log(`Emitting WebRTC offer (${offerType}) from ${data.fromUserId} to ${data.targetUserId} for stream ${data.streamId}`);

    // Emit to the specific user
    io.to(`user:${data.targetUserId}`).emit('webrtc_offer', data);
  } catch (err) {
    console.error('Error emitting WebRTC offer:', err);
  }
};

/**
 * Emit a WebRTC answer to a specific user
 * @param {Object} data - The answer data
 */
const emitWebRTCAnswer = (data) => {
  if (!io) {
    console.error('Socket.IO not initialized');
    return;
  }

  try {
    // Extract answer type for logging if available
    let answerType = 'unknown';
    if (data.answer && data.answer.type) {
      answerType = data.answer.type;
    }

    console.log(`Emitting WebRTC answer (${answerType}) from ${data.fromUserId} to ${data.targetUserId} for stream ${data.streamId}`);

    // Emit to the specific user
    io.to(`user:${data.targetUserId}`).emit('webrtc_answer', data);
  } catch (err) {
    console.error('Error emitting WebRTC answer:', err);
  }
};

/**
 * Emit a WebRTC ICE candidate to a specific user
 * @param {Object} data - The ICE candidate data
 */
const emitWebRTCIceCandidate = (data) => {
  if (!io) {
    console.error('Socket.IO not initialized');
    return;
  }

  try {
    // Extract candidate type for logging if available
    let candidateType = 'unknown';
    if (data.candidate && data.candidate.candidate) {
      const parts = data.candidate.candidate.split(' ');
      if (parts.length >= 7) {
        candidateType = parts[7]; // Extract candidate type
      }
    }

    // Only log for actual candidates (not end-of-candidates)
    if (data.candidate && data.candidate.candidate) {
      console.log(`Emitting WebRTC ICE candidate (${candidateType}) from ${data.fromUserId} to ${data.targetUserId} for stream ${data.streamId}`);
    }

    // Emit to the specific user
    io.to(`user:${data.targetUserId}`).emit('webrtc_ice_candidate', data);
  } catch (err) {
    console.error('Error emitting WebRTC ICE candidate:', err);
  }
};

/**
 * Emit a WebRTC error to a specific user or stream
 * @param {Object} data - The error data
 */
const emitWebRTCError = (data) => {
  if (!io) {
    console.error('Socket.IO not initialized');
    return;
  }

  try {
    console.log(`Emitting WebRTC error to ${data.userId || 'stream ' + data.streamId}: ${data.message}`);

    // Emit to the specific user if userId is provided
    if (data.userId) {
      io.to(`user:${data.userId}`).emit('webrtc_error', data);
    }
    // Otherwise emit to the stream room if streamId is provided
    else if (data.streamId) {
      io.to(`stream:${data.streamId}`).emit('webrtc_error', data);
    }
  } catch (err) {
    console.error('Error emitting WebRTC error:', err);
  }
};

/**
 * Emit a notification when a followed creator starts a stream
 * @param {string} streamId - The ID of the stream
 * @param {Object} streamData - Data about the stream
 * @param {Array} followerIds - Array of follower user IDs
 */
const emitStreamStartNotification = (streamId, streamData, followerIds) => {
  if (!io) {
    console.error('Socket.IO not initialized');
    return;
  }

  // Emit to each follower
  followerIds.forEach(followerId => {
    io.to(`user:${followerId}`).emit('stream_start_notification', {
      type: 'stream_start',
      stream: streamData,
      createdAt: new Date()
    });
  });
};

/**
 * Emit a notification when a stream is scheduled
 * @param {string} streamId - The ID of the stream
 * @param {Object} streamData - Data about the stream
 * @param {Array} followerIds - Array of follower user IDs
 */
const emitStreamScheduledNotification = (streamId, streamData, followerIds) => {
  if (!io) {
    console.error('Socket.IO not initialized');
    return;
  }

  // Emit to each follower
  followerIds.forEach(followerId => {
    io.to(`user:${followerId}`).emit('stream_scheduled_notification', {
      type: 'stream_scheduled',
      stream: streamData,
      createdAt: new Date()
    });
  });
};

/**
 * Emit a reminder notification for an upcoming stream
 * @param {string} userId - The user ID to notify
 * @param {Object} reminderData - Data about the reminder
 */
const emitStreamReminderNotification = (userId, reminderData) => {
  if (!io) {
    console.error('Socket.IO not initialized');
    return;
  }

  io.to(`user:${userId}`).emit('stream_reminder_notification', {
    type: 'stream_reminder',
    reminder: reminderData,
    createdAt: new Date()
  });
};

/**
 * Emit an update when a user RSVPs to a stream
 * @param {string} streamId - The ID of the stream
 * @param {Object} rsvpData - Data about the RSVP
 */
const emitStreamRSVPUpdate = (streamId, rsvpData) => {
  if (!io) {
    console.error('Socket.IO not initialized');
    return;
  }

  // Emit to the stream room
  io.to(`stream:${streamId}`).emit('stream_rsvp_update', {
    type: 'rsvp_update',
    rsvp: rsvpData,
    createdAt: new Date()
  });
};

/**
 * Emit a notification for a trending stream that matches user interests
 * @param {string} userId - The user ID to notify
 * @param {Object} streamData - Data about the stream
 */
const emitTrendingStreamNotification = (userId, streamData) => {
  if (!io) {
    console.error('Socket.IO not initialized');
    return;
  }

  io.to(`user:${userId}`).emit('trending_stream_notification', {
    type: 'trending_stream',
    stream: streamData,
    createdAt: new Date()
  });
};

/**
 * Emit a notification for a local stream
 * @param {string} userId - The user ID to notify
 * @param {Object} streamData - Data about the stream
 */
const emitLocalStreamNotification = (userId, streamData) => {
  if (!io) {
    console.error('Socket.IO not initialized');
    return;
  }

  io.to(`user:${userId}`).emit('local_stream_notification', {
    type: 'local_stream',
    stream: streamData,
    createdAt: new Date()
  });
};

module.exports = {
  initialize,
  emitNotification,
  emitLike,
  emitComment,
  emitFollow,
  emitStoryView,
  emitNotificationRead,
  emitAllNotificationsRead,
  emitNewMessage,
  emitMessagesRead,
  emitMessageDeleted,
  emitMessageReaction,
  emitMessageReactionRemoved,
  emitTypingIndicator,
  emitMessagePinned,
  emitMessageUnpinned,
  emitPollUpdated,
  emitCommandProcessed,
  emitGroupCreated,
  emitGroupUpdated,
  emitParticipantsAdded,
  emitParticipantRemoved,
  emitParticipantLeft,
  emitParticipantRoleChanged,
  // Live streaming events
  emitNewLiveStream,
  emitLiveStreamUpdated,
  emitLiveStreamDeleted,
  emitLiveStreamStarted,
  emitLiveStreamEnded,
  emitLiveStreamChatMessage,
  emitLiveStreamChatMessageDeleted,
  emitLiveStreamChatMessagePinned,
  emitLiveStreamViewerJoined,
  emitLiveStreamViewerLeft,
  emitLiveStreamReaction,
  emitLiveStreamReactionDeleted,
  // Monetization events
  emitGift,
  emitFlashDeal,
  emitNFTMinted,
  emitSponsorAdded,
  // WebRTC events
  emitWebRTCOffer,
  emitWebRTCAnswer,
  emitWebRTCIceCandidate,
  emitWebRTCError,

  // Stream notification events
  emitStreamStartNotification,
  emitStreamScheduledNotification,
  emitStreamReminderNotification,
  emitStreamRSVPUpdate,
  emitTrendingStreamNotification,
  emitLocalStreamNotification,

  // Moderation and safety events
  emitContentModerated,
  emitUserBlocked,
  emitUserUnblocked,
  emitModerationSettingsUpdated,
  emitModeratorAdded,
  emitModeratorRemoved,
};
