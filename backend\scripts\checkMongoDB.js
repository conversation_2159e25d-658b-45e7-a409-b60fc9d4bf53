const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const dotenv = require('dotenv');
const fs = require('fs');
const path = require('path');

// Load environment variables
dotenv.config();

console.log('Checking MongoDB connection and creating test user if needed...');

// Check if MongoDB URI is defined
if (!process.env.MONGO_URI) {
  console.error('MONGO_URI is not defined in environment variables');
  process.exit(1);
}

// Connect to MongoDB
mongoose.connect(process.env.MONGO_URI, {
  useNewUrlParser: true,
  useUnifiedTopology: true,
})
.then(() => {
  console.log('✅ MongoDB connected successfully');
  createTestUser();
})
.catch(err => {
  console.error('❌ MongoDB connection error:', err.message);

  // Check if MongoDB is running
  console.log('\nPossible solutions:');
  console.log('1. Make sure MongoDB is installed and running');
  console.log('2. Check that the MongoDB URI in your .env file is correct');
  console.log('3. If using a local MongoDB, try starting it with: mongod --dbpath=./data');

  process.exit(1);
});

// Create test user
async function createTestUser() {
  try {
    // Check if User model exists
    let User;
    try {
      User = mongoose.model('User');
    } catch (error) {
      // Define User model if it doesn't exist
      const UserSchema = new mongoose.Schema({
        username: {
          type: String,
          required: [true, 'Please provide a username'],
          unique: true,
          trim: true,
          minlength: [3, 'Username must be at least 3 characters'],
          maxlength: [20, 'Username cannot be more than 20 characters'],
        },
        email: {
          type: String,
          required: [true, 'Please provide an email'],
          unique: true,
          match: [
            /^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/,
            'Please provide a valid email',
          ],
        },
        password: {
          type: String,
          required: [true, 'Please provide a password'],
          minlength: [6, 'Password must be at least 6 characters'],
          select: false,
        },
        name: {
          type: String,
          required: [true, 'Please provide your name'],
        },
        bio: {
          type: String,
          maxlength: [150, 'Bio cannot be more than 150 characters'],
          default: '',
        },
        website: {
          type: String,
          default: '',
        },
        profilePicture: {
          type: String,
          default: '/default-profile_jktu3d.png',
        },
      }, {
        timestamps: true,
        toJSON: { virtuals: true },
        toObject: { virtuals: true },
      });

      // Encrypt password using bcrypt
      UserSchema.pre('save', async function (next) {
        if (!this.isModified('password')) {
          next();
        }

        const salt = await bcrypt.genSalt(10);
        this.password = await bcrypt.hash(this.password, salt);
      });

      User = mongoose.model('User', UserSchema);
    }

    // No test users are created - users should register through the application

    console.log('\n✅ MongoDB check completed successfully');
    console.log('\nPlease register a new account at /register to start using the application');

    // Disconnect from MongoDB
    await mongoose.disconnect();
    console.log('\nMongoDB disconnected');
    process.exit(0);
  } catch (error) {
    console.error('❌ Error creating test user:', error.message);
    process.exit(1);
  }
}
