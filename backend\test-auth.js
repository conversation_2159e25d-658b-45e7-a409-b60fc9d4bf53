const axios = require('axios');

// Test authentication endpoints
const testAuth = async () => {
  const baseURL = 'http://localhost:10001';
  
  console.log('🧪 Testing Authentication System...\n');

  try {
    // Test 1: Register a new user
    console.log('1️⃣ Testing Registration...');
    const userData = {
      name: 'Test User',
      fullName: 'Test User Full',
      username: 'testuser_' + Date.now(),
      email: 'test_' + Date.now() + '@example.com',
      password: 'password123'
    };

    const registerResponse = await axios.post(`${baseURL}/api/auth/register`, userData);
    
    if (registerResponse.data.success && registerResponse.data.token) {
      console.log('✅ Registration successful');
      console.log('   Token received:', registerResponse.data.token.substring(0, 20) + '...');
      console.log('   User ID:', registerResponse.data.user.id);
    } else {
      console.log('❌ Registration failed:', registerResponse.data);
      return;
    }

    const token = registerResponse.data.token;

    // Test 2: Login with the same user
    console.log('\n2️⃣ Testing Login...');
    const loginResponse = await axios.post(`${baseURL}/api/auth/login`, {
      email: userData.email,
      password: userData.password
    });

    if (loginResponse.data.success && loginResponse.data.token) {
      console.log('✅ Login successful');
      console.log('   Token received:', loginResponse.data.token.substring(0, 20) + '...');
    } else {
      console.log('❌ Login failed:', loginResponse.data);
      return;
    }

    // Test 3: Get current user info
    console.log('\n3️⃣ Testing Get Me...');
    const meResponse = await axios.get(`${baseURL}/api/auth/me`, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });

    if (meResponse.data.success && meResponse.data.data) {
      console.log('✅ Get Me successful');
      console.log('   Username:', meResponse.data.data.username);
      console.log('   Email:', meResponse.data.data.email);
    } else {
      console.log('❌ Get Me failed:', meResponse.data);
      return;
    }

    // Test 4: Refresh token
    console.log('\n4️⃣ Testing Token Refresh...');
    const refreshResponse = await axios.post(`${baseURL}/api/auth/refresh-token`, {
      token: token
    });

    if (refreshResponse.data.success && refreshResponse.data.token) {
      console.log('✅ Token refresh successful');
      console.log('   New token received:', refreshResponse.data.token.substring(0, 20) + '...');
    } else {
      console.log('❌ Token refresh failed:', refreshResponse.data);
      return;
    }

    // Test 5: Logout
    console.log('\n5️⃣ Testing Logout...');
    const logoutResponse = await axios.get(`${baseURL}/api/auth/logout`, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });

    if (logoutResponse.data.success) {
      console.log('✅ Logout successful');
    } else {
      console.log('❌ Logout failed:', logoutResponse.data);
    }

    console.log('\n🎉 All authentication tests passed!');

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
  }
};

// Run the test
testAuth();
