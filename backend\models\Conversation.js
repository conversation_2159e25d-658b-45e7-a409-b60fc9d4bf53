const mongoose = require('mongoose');

const ConversationSchema = new mongoose.Schema({
  participants: [
    {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
  ],
  isGroup: {
    type: Boolean,
    default: false,
  },
  name: {
    type: String,
    trim: true,
  },
  groupAdmin: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
  },
  groupImage: {
    type: String,
  },
  lastMessage: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Message',
  },
  unreadCount: {
    type: Map,
    of: Number,
    default: {},
  },
  muted: [
    {
      user: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
      },
      until: {
        type: Date,
      },
    },
  ],
  pinnedBy: [
    {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
    },
  ],
  groupMembers: [
    {
      user: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true,
      },
      role: {
        type: String,
        enum: ['admin', 'member'],
        default: 'member',
      },
      addedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
      },
      addedAt: {
        type: Date,
        default: Date.now,
      },
    },
  ],
}, {
  timestamps: true,
});

// Virtual for checking if a user is a participant
ConversationSchema.methods.isParticipant = function(userId) {
  return this.participants.some(participant => participant.toString() === userId.toString());
};

// Virtual for checking if a user is a group admin
ConversationSchema.methods.isGroupAdmin = function(userId) {
  if (!this.isGroup) return false;
  
  if (this.groupAdmin && this.groupAdmin.toString() === userId.toString()) {
    return true;
  }
  
  return this.groupMembers.some(
    member => member.user.toString() === userId.toString() && member.role === 'admin'
  );
};

// Virtual for checking if a user has muted the conversation
ConversationSchema.methods.isMutedBy = function(userId) {
  return this.muted.some(mute => {
    return mute.user.toString() === userId.toString() && 
           (!mute.until || new Date(mute.until) > new Date());
  });
};

// Virtual for checking if a user has pinned the conversation
ConversationSchema.methods.isPinnedBy = function(userId) {
  return this.pinnedBy.some(id => id.toString() === userId.toString());
};

// Get unread count for a specific user
ConversationSchema.methods.getUnreadCountForUser = function(userId) {
  const userIdStr = userId.toString();
  return this.unreadCount.get(userIdStr) || 0;
};

// Reset unread count for a specific user
ConversationSchema.methods.resetUnreadCountForUser = function(userId) {
  const userIdStr = userId.toString();
  this.unreadCount.set(userIdStr, 0);
  return this;
};

// Increment unread count for all participants except the sender
ConversationSchema.methods.incrementUnreadCount = function(senderId) {
  const senderIdStr = senderId.toString();
  
  this.participants.forEach(participantId => {
    const participantIdStr = participantId.toString();
    if (participantIdStr !== senderIdStr) {
      const currentCount = this.unreadCount.get(participantIdStr) || 0;
      this.unreadCount.set(participantIdStr, currentCount + 1);
    }
  });
  
  return this;
};

module.exports = mongoose.model('Conversation', ConversationSchema);
