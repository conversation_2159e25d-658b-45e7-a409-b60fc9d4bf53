const mongoose = require('mongoose');

const TagSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Please provide a tag name'],
    trim: true,
    unique: true,
    lowercase: true,
  },
  count: {
    type: Number,
    default: 0,
  },
  posts: [
    {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Post',
    },
  ],
  reels: [
    {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Reel',
    },
  ],
}, {
  timestamps: true,
});

module.exports = mongoose.model('Tag', TagSchema);
