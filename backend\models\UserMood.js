const mongoose = require('mongoose');

const UserMoodSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  name: {
    type: String,
    required: [true, 'Please provide a mood name'],
    trim: true,
  },
  description: {
    type: String,
    required: [true, 'Please provide a description'],
  },
  category: {
    type: String,
    enum: ['positive', 'negative', 'neutral', 'complex'],
    required: true,
  },
  icon: {
    type: String,
    required: true,
  },
  color: {
    type: String,
    required: true,
  },
  isPublic: {
    type: Boolean,
    default: true,
  },
  relatedEmotions: [
    {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Emotion',
    },
  ],
  usageCount: {
    type: Number,
    default: 0,
  },
  commentsCount: {
    type: Number,
    default: 0,
  },
  likesCount: {
    type: Number,
    default: 0,
  },
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true },
});

// Virtual field for comments
UserMoodSchema.virtual('comments', {
  ref: 'MoodComment',
  localField: '_id',
  foreignField: 'mood',
});

// Virtual field for likes
UserMoodSchema.virtual('likes', {
  ref: 'Like',
  localField: '_id',
  foreignField: 'mood',
});

module.exports = mongoose.model('UserMood', UserMoodSchema);
