const mongoose = require('mongoose');

const MoodSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Please provide a mood name'],
    unique: true,
    trim: true,
  },
  description: {
    type: String,
    required: [true, 'Please provide a description'],
  },
  category: {
    type: String,
    enum: ['positive', 'negative', 'neutral', 'complex'],
    required: true,
  },
  icon: {
    type: String,
    required: true,
  },
  color: {
    type: String,
    required: true,
  },
  relatedEmotions: [
    {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Emotion',
    },
  ],
}, {
  timestamps: true,
});

module.exports = mongoose.model('Mood', MoodSchema);
