const mongoose = require('mongoose');

const AREffectSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Please provide an effect name'],
    trim: true,
  },
  description: {
    type: String,
  },
  type: {
    type: String,
    enum: ['filter', 'object', 'background', 'animation'],
    required: true,
  },
  category: {
    type: String,
    enum: ['fun', 'beauty', 'mood', 'seasonal', 'branded', 'custom'],
    required: true,
  },
  thumbnail: {
    type: String,
    required: true,
  },
  assetUrl: {
    type: String,
    required: true,
  },
  creator: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
  },
  isPublic: {
    type: Boolean,
    default: true,
  },
  usageCount: {
    type: Number,
    default: 0,
  },
  tags: [
    {
      type: String,
    },
  ],
  relatedEmotions: [
    {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Emotion',
    },
  ],
}, {
  timestamps: true,
});

// Index for searching effects
AREffectSchema.index({ name: 'text', description: 'text', tags: 'text' });

module.exports = mongoose.model('AREffect', AREffectSchema);
