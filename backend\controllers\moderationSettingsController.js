const ModerationSettings = require('../models/ModerationSettings');
const LiveStream = require('../models/LiveStream');
const User = require('../models/User');
const asyncHandler = require('../middleware/async');
const ErrorResponse = require('../utils/errorResponse');
const socketEmitter = require('../utils/socketEmitter');

/**
 * @desc    Get moderation settings
 * @route   GET /api/moderation/settings/:ownerType/:ownerId
 * @access  Private
 */
exports.getModerationSettings = asyncHandler(async (req, res, next) => {
  const { ownerType, ownerId } = req.params;

  // Validate owner type
  if (ownerType !== 'User' && ownerType !== 'LiveStream') {
    return next(new ErrorResponse('Invalid owner type. Must be "User" or "LiveStream"', 400));
  }

  // Check permissions
  if (ownerType === 'User' && ownerId !== req.user.id && req.user.role !== 'admin') {
    return next(new ErrorResponse('Not authorized to view these settings', 403));
  }

  if (ownerType === 'LiveStream') {
    // Check if user is stream owner or moderator
    const stream = await LiveStream.findById(ownerId);
    
    if (!stream) {
      return next(new ErrorResponse('Stream not found', 404));
    }

    if (stream.user.toString() !== req.user.id && req.user.role !== 'admin') {
      // Check if user is a moderator
      const settings = await ModerationSettings.findOne({
        ownerType: 'LiveStream',
        owner: ownerId
      });

      if (!settings || !settings.moderators.some(
        mod => mod.user.toString() === req.user.id && mod.permissions.canModifySettings
      )) {
        return next(new ErrorResponse('Not authorized to view these settings', 403));
      }
    }
  }

  // Find settings
  let settings = await ModerationSettings.findOne({
    ownerType,
    owner: ownerId
  }).populate('moderators.user', 'username name profilePicture');

  // If settings don't exist, create default settings
  if (!settings) {
    settings = await ModerationSettings.create({
      ownerType,
      owner: ownerId
    });
  }

  res.status(200).json({
    success: true,
    data: settings
  });
});

/**
 * @desc    Update moderation settings
 * @route   PUT /api/moderation/settings/:ownerType/:ownerId
 * @access  Private
 */
exports.updateModerationSettings = asyncHandler(async (req, res, next) => {
  const { ownerType, ownerId } = req.params;
  const { chat, contentFiltering, userSafety } = req.body;

  // Validate owner type
  if (ownerType !== 'User' && ownerType !== 'LiveStream') {
    return next(new ErrorResponse('Invalid owner type. Must be "User" or "LiveStream"', 400));
  }

  // Check permissions
  if (ownerType === 'User' && ownerId !== req.user.id && req.user.role !== 'admin') {
    return next(new ErrorResponse('Not authorized to update these settings', 403));
  }

  if (ownerType === 'LiveStream') {
    // Check if user is stream owner or admin
    const stream = await LiveStream.findById(ownerId);
    
    if (!stream) {
      return next(new ErrorResponse('Stream not found', 404));
    }

    if (stream.user.toString() !== req.user.id && req.user.role !== 'admin') {
      // Check if user is a moderator with permission to modify settings
      const settings = await ModerationSettings.findOne({
        ownerType: 'LiveStream',
        owner: ownerId
      });

      if (!settings || !settings.moderators.some(
        mod => mod.user.toString() === req.user.id && mod.permissions.canModifySettings
      )) {
        return next(new ErrorResponse('Not authorized to update these settings', 403));
      }
    }
  }

  // Find settings
  let settings = await ModerationSettings.findOne({
    ownerType,
    owner: ownerId
  });

  // If settings don't exist, create them
  if (!settings) {
    settings = await ModerationSettings.create({
      ownerType,
      owner: ownerId,
      ...(chat && { chat }),
      ...(contentFiltering && { contentFiltering }),
      ...(userSafety && { userSafety })
    });
  } else {
    // Update settings
    if (chat) {
      settings.chat = {
        ...settings.chat,
        ...chat
      };
    }

    if (contentFiltering) {
      settings.contentFiltering = {
        ...settings.contentFiltering,
        ...contentFiltering
      };
    }

    if (userSafety) {
      settings.userSafety = {
        ...settings.userSafety,
        ...userSafety
      };
    }

    await settings.save();
  }

  // If this is a live stream, emit settings update event
  if (ownerType === 'LiveStream') {
    socketEmitter.emitModerationSettingsUpdated(ownerId, settings);
  }

  res.status(200).json({
    success: true,
    data: settings
  });
});

/**
 * @desc    Add a moderator
 * @route   POST /api/moderation/settings/:ownerType/:ownerId/moderators
 * @access  Private
 */
exports.addModerator = asyncHandler(async (req, res, next) => {
  const { ownerType, ownerId } = req.params;
  const { userId, permissions } = req.body;

  // Validate input
  if (!userId) {
    return next(new ErrorResponse('User ID is required', 400));
  }

  // Validate owner type
  if (ownerType !== 'User' && ownerType !== 'LiveStream') {
    return next(new ErrorResponse('Invalid owner type. Must be "User" or "LiveStream"', 400));
  }

  // Check if user exists
  const user = await User.findById(userId);
  if (!user) {
    return next(new ErrorResponse('User not found', 404));
  }

  // Check permissions
  if (ownerType === 'User' && ownerId !== req.user.id && req.user.role !== 'admin') {
    return next(new ErrorResponse('Not authorized to add moderators', 403));
  }

  if (ownerType === 'LiveStream') {
    // Check if user is stream owner or admin
    const stream = await LiveStream.findById(ownerId);
    
    if (!stream) {
      return next(new ErrorResponse('Stream not found', 404));
    }

    if (stream.user.toString() !== req.user.id && req.user.role !== 'admin') {
      return next(new ErrorResponse('Not authorized to add moderators', 403));
    }
  }

  // Find settings
  let settings = await ModerationSettings.findOne({
    ownerType,
    owner: ownerId
  });

  // If settings don't exist, create them
  if (!settings) {
    settings = await ModerationSettings.create({
      ownerType,
      owner: ownerId
    });
  }

  // Check if user is already a moderator
  if (settings.moderators.some(mod => mod.user.toString() === userId)) {
    return next(new ErrorResponse('User is already a moderator', 400));
  }

  // Add moderator
  settings.moderators.push({
    user: userId,
    permissions: permissions || {
      canTimeout: true,
      canBan: true,
      canDeleteMessages: true,
      canModifySettings: false
    },
    addedBy: req.user.id
  });

  await settings.save();

  // If this is a live stream, emit moderator added event
  if (ownerType === 'LiveStream') {
    socketEmitter.emitModeratorAdded(ownerId, userId);
  }

  res.status(200).json({
    success: true,
    data: settings
  });
});

/**
 * @desc    Remove a moderator
 * @route   DELETE /api/moderation/settings/:ownerType/:ownerId/moderators/:userId
 * @access  Private
 */
exports.removeModerator = asyncHandler(async (req, res, next) => {
  const { ownerType, ownerId, userId } = req.params;

  // Validate owner type
  if (ownerType !== 'User' && ownerType !== 'LiveStream') {
    return next(new ErrorResponse('Invalid owner type. Must be "User" or "LiveStream"', 400));
  }

  // Check permissions
  if (ownerType === 'User' && ownerId !== req.user.id && req.user.role !== 'admin') {
    return next(new ErrorResponse('Not authorized to remove moderators', 403));
  }

  if (ownerType === 'LiveStream') {
    // Check if user is stream owner or admin
    const stream = await LiveStream.findById(ownerId);
    
    if (!stream) {
      return next(new ErrorResponse('Stream not found', 404));
    }

    if (stream.user.toString() !== req.user.id && req.user.role !== 'admin') {
      return next(new ErrorResponse('Not authorized to remove moderators', 403));
    }
  }

  // Find settings
  const settings = await ModerationSettings.findOne({
    ownerType,
    owner: ownerId
  });

  if (!settings) {
    return next(new ErrorResponse('Moderation settings not found', 404));
  }

  // Check if user is a moderator
  const moderatorIndex = settings.moderators.findIndex(mod => mod.user.toString() === userId);
  
  if (moderatorIndex === -1) {
    return next(new ErrorResponse('User is not a moderator', 400));
  }

  // Remove moderator
  settings.moderators.splice(moderatorIndex, 1);
  await settings.save();

  // If this is a live stream, emit moderator removed event
  if (ownerType === 'LiveStream') {
    socketEmitter.emitModeratorRemoved(ownerId, userId);
  }

  res.status(200).json({
    success: true,
    data: settings
  });
});

/**
 * @desc    Update moderator permissions
 * @route   PUT /api/moderation/settings/:ownerType/:ownerId/moderators/:userId
 * @access  Private
 */
exports.updateModeratorPermissions = asyncHandler(async (req, res, next) => {
  const { ownerType, ownerId, userId } = req.params;
  const { permissions } = req.body;

  // Validate owner type
  if (ownerType !== 'User' && ownerType !== 'LiveStream') {
    return next(new ErrorResponse('Invalid owner type. Must be "User" or "LiveStream"', 400));
  }

  // Check permissions
  if (ownerType === 'User' && ownerId !== req.user.id && req.user.role !== 'admin') {
    return next(new ErrorResponse('Not authorized to update moderator permissions', 403));
  }

  if (ownerType === 'LiveStream') {
    // Check if user is stream owner or admin
    const stream = await LiveStream.findById(ownerId);
    
    if (!stream) {
      return next(new ErrorResponse('Stream not found', 404));
    }

    if (stream.user.toString() !== req.user.id && req.user.role !== 'admin') {
      return next(new ErrorResponse('Not authorized to update moderator permissions', 403));
    }
  }

  // Find settings
  const settings = await ModerationSettings.findOne({
    ownerType,
    owner: ownerId
  });

  if (!settings) {
    return next(new ErrorResponse('Moderation settings not found', 404));
  }

  // Check if user is a moderator
  const moderator = settings.moderators.find(mod => mod.user.toString() === userId);
  
  if (!moderator) {
    return next(new ErrorResponse('User is not a moderator', 400));
  }

  // Update permissions
  moderator.permissions = {
    ...moderator.permissions,
    ...permissions
  };

  await settings.save();

  res.status(200).json({
    success: true,
    data: settings
  });
});
