const mongoose = require('mongoose');

const LiveStreamChatSchema = new mongoose.Schema({
  stream: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'LiveStream',
    required: true,
  },
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  message: {
    type: String,
    required: [true, 'Message cannot be empty'],
    trim: true,
    maxlength: [500, 'Message cannot be more than 500 characters'],
  },
  isPinned: {
    type: Boolean,
    default: false,
  },
  isHighlighted: {
    type: Boolean,
    default: false,
  },
  emotions: [
    {
      emotion: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Emotion',
      },
      intensity: {
        type: Number,
        min: 1,
        max: 10,
        default: 5,
      },
    },
  ],
  reactions: [
    {
      user: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
      },
      emoji: {
        type: String,
      },
      timestamp: {
        type: Date,
        default: Date.now,
      },
    },
  ],
  isDeleted: {
    type: Boolean,
    default: false,
  },
  deletedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
  },
  deletedAt: {
    type: Date,
  },
  replyTo: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'LiveStreamChat',
  },
}, { timestamps: true });

// Create indexes for efficient queries
LiveStreamChatSchema.index({ stream: 1, createdAt: 1 });
LiveStreamChatSchema.index({ user: 1, stream: 1 });

module.exports = mongoose.model('LiveStreamChat', LiveStreamChatSchema);
