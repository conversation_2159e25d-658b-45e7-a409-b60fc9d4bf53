const ContentModeration = require('../models/ContentModeration');
const BlockedWord = require('../models/BlockedWord');
const ModerationSettings = require('../models/ModerationSettings');
const User = require('../models/User');
const Post = require('../models/Post');
const Reel = require('../models/Reel');
const Comment = require('../models/Comment');
const LiveStreamChat = require('../models/LiveStreamChat');
const Message = require('../models/Message');
const asyncHandler = require('../middleware/async');
const ErrorResponse = require('../utils/errorResponse');
const socketEmitter = require('../utils/socketEmitter');
const axios = require('axios');

/**
 * @desc    Analyze content with AI moderation
 * @route   POST /api/moderation/analyze
 * @access  Private
 */
exports.analyzeContent = asyncHandler(async (req, res, next) => {
  const { contentType, contentId, text, imageUrl } = req.body;

  if (!contentType || !contentId) {
    return next(new ErrorResponse('Content type and ID are required', 400));
  }

  // Check if content already has moderation record
  let moderation = await ContentModeration.findOne({
    contentType,
    contentId
  });

  if (moderation) {
    return res.status(200).json({
      success: true,
      data: moderation
    });
  }

  // Get content based on type
  let content;
  let contentUser;

  switch (contentType) {
    case 'post':
      content = await Post.findById(contentId);
      contentUser = content ? content.user : null;
      break;
    case 'reel':
      content = await Reel.findById(contentId);
      contentUser = content ? content.user : null;
      break;
    case 'comment':
      content = await Comment.findById(contentId);
      contentUser = content ? content.user : null;
      break;
    case 'message':
      content = await Message.findById(contentId);
      contentUser = content ? content.sender : null;
      break;
    case 'stream_chat':
      content = await LiveStreamChat.findById(contentId);
      contentUser = content ? content.user : null;
      break;
    case 'user_profile':
      content = await User.findById(contentId);
      contentUser = content ? content._id : null;
      break;
    default:
      return next(new ErrorResponse('Invalid content type', 400));
  }

  if (!content) {
    return next(new ErrorResponse('Content not found', 404));
  }

  // Get content text if not provided
  const contentText = text || getContentText(content, contentType);
  
  // Get content image if not provided
  const contentImage = imageUrl || getContentImage(content, contentType);

  // Check for blocked words
  const blockedWordResults = await checkBlockedWords(contentText, contentType, contentId);

  // Perform AI content moderation
  let aiResults = {
    score: 0,
    categories: {},
    flaggedKeywords: blockedWordResults.flaggedWords,
    suggestedAction: 'none',
    explanation: ''
  };

  // Only call AI moderation API if there's content to analyze
  if (contentText || contentImage) {
    try {
      aiResults = await performAIModeration(contentText, contentImage);
    } catch (error) {
      console.error('AI moderation error:', error);
      // Continue with blocked word results only
    }
  }

  // Combine blocked word and AI results
  const moderationScore = Math.max(
    aiResults.score,
    blockedWordResults.severity === 'high' ? 0.9 : 
    blockedWordResults.severity === 'medium' ? 0.7 : 
    blockedWordResults.severity === 'low' ? 0.5 : 0
  );

  // Determine suggested action based on score
  const suggestedAction = 
    moderationScore >= 0.9 ? 'remove' :
    moderationScore >= 0.7 ? 'hide' :
    moderationScore >= 0.5 ? 'flag' : 'none';

  // Create moderation record
  moderation = await ContentModeration.create({
    contentType,
    contentId,
    user: contentUser,
    aiModeration: {
      score: moderationScore,
      categories: aiResults.categories || {},
      flaggedKeywords: [...new Set([...aiResults.flaggedKeywords, ...blockedWordResults.flaggedWords])],
      suggestedAction,
      explanation: aiResults.explanation || blockedWordResults.explanation
    },
    status: 
      suggestedAction === 'remove' ? 'auto_rejected' :
      suggestedAction === 'none' ? 'auto_approved' : 'pending',
    action: 
      suggestedAction === 'remove' ? 'hidden' : 'none'
  });

  // If content should be auto-moderated, take action
  if (suggestedAction === 'remove' || suggestedAction === 'hide') {
    await takeContentAction(contentType, contentId, suggestedAction === 'remove' ? 'hidden' : 'hidden');
  }

  res.status(201).json({
    success: true,
    data: moderation
  });
});

/**
 * @desc    Get moderation status for content
 * @route   GET /api/moderation/:contentType/:contentId
 * @access  Private
 */
exports.getModerationStatus = asyncHandler(async (req, res, next) => {
  const { contentType, contentId } = req.params;

  const moderation = await ContentModeration.findOne({
    contentType,
    contentId
  });

  if (!moderation) {
    return next(new ErrorResponse('No moderation record found for this content', 404));
  }

  res.status(200).json({
    success: true,
    data: moderation
  });
});

/**
 * @desc    Update moderation status
 * @route   PUT /api/moderation/:id
 * @access  Private (Admin/Moderator)
 */
exports.updateModerationStatus = asyncHandler(async (req, res, next) => {
  const { status, action, notes } = req.body;

  // Check if user is admin or moderator
  if (req.user.role !== 'admin' && req.user.role !== 'moderator') {
    return next(new ErrorResponse('Not authorized to update moderation status', 403));
  }

  let moderation = await ContentModeration.findById(req.params.id);

  if (!moderation) {
    return next(new ErrorResponse('Moderation record not found', 404));
  }

  // Update moderation record
  moderation.status = status || moderation.status;
  moderation.action = action || moderation.action;
  moderation.notes = notes || moderation.notes;
  moderation.moderator = req.user.id;
  moderation.reviewedAt = Date.now();
  moderation.updatedAt = Date.now();

  await moderation.save();

  // Take action on content if needed
  if (action && action !== 'none') {
    await takeContentAction(moderation.contentType, moderation.contentId, action);
  }

  res.status(200).json({
    success: true,
    data: moderation
  });
});

// Helper functions

/**
 * Extract text content based on content type
 */
const getContentText = (content, contentType) => {
  switch (contentType) {
    case 'post':
      return content.caption || '';
    case 'reel':
      return content.caption || '';
    case 'comment':
      return content.text || '';
    case 'message':
      return content.text || '';
    case 'stream_chat':
      return content.message || '';
    case 'user_profile':
      return `${content.name} ${content.bio || ''} ${content.username}`;
    default:
      return '';
  }
};

/**
 * Extract image URL based on content type
 */
const getContentImage = (content, contentType) => {
  switch (contentType) {
    case 'post':
      return content.media && content.media.length > 0 ? content.media[0].url : null;
    case 'reel':
      return content.thumbnail ? content.thumbnail.url : null;
    case 'user_profile':
      return content.profilePicture || null;
    default:
      return null;
  }
};

/**
 * Check content against blocked words
 */
const checkBlockedWords = async (text, contentType, contentId) => {
  if (!text) {
    return { 
      flaggedWords: [], 
      severity: 'none',
      explanation: ''
    };
  }

  // Get global blocked words
  const globalBlockedWords = await BlockedWord.find({
    scope: 'global',
    isActive: true
  });

  // Get content-specific blocked words if applicable
  let scopeBlockedWords = [];
  if (contentType === 'stream_chat') {
    // Get the stream ID from the chat message
    const chatMessage = await LiveStreamChat.findById(contentId);
    if (chatMessage) {
      scopeBlockedWords = await BlockedWord.find({
        scope: 'stream',
        scopeId: chatMessage.stream,
        isActive: true
      });
    }
  }

  // Combine blocked word lists
  const blockedWords = [...globalBlockedWords, ...scopeBlockedWords];
  
  // Check text against blocked words
  const flaggedWords = [];
  let highestSeverity = 'none';
  
  for (const blockedWord of blockedWords) {
    let isMatch = false;
    
    if (blockedWord.isRegex) {
      try {
        const regex = new RegExp(blockedWord.word, 'i');
        isMatch = regex.test(text);
      } catch (error) {
        console.error('Invalid regex pattern:', blockedWord.word);
      }
    } else if (blockedWord.exactMatch) {
      const words = text.toLowerCase().split(/\s+/);
      isMatch = words.includes(blockedWord.word.toLowerCase());
    } else {
      isMatch = text.toLowerCase().includes(blockedWord.word.toLowerCase());
    }
    
    if (isMatch) {
      flaggedWords.push(blockedWord.word);
      
      // Update highest severity
      if (
        (blockedWord.severity === 'high') || 
        (blockedWord.severity === 'medium' && highestSeverity !== 'high') ||
        (blockedWord.severity === 'low' && highestSeverity === 'none')
      ) {
        highestSeverity = blockedWord.severity;
      }
    }
  }
  
  return {
    flaggedWords,
    severity: highestSeverity,
    explanation: flaggedWords.length > 0 
      ? `Content contains blocked ${flaggedWords.length > 1 ? 'words' : 'word'}: ${flaggedWords.join(', ')}`
      : ''
  };
};

/**
 * Perform AI content moderation
 */
const performAIModeration = async (text, imageUrl) => {
  // This would typically call an external AI moderation API
  // For now, we'll implement a simple keyword-based approach
  
  // In a production environment, you would integrate with services like:
  // - OpenAI Moderation API
  // - Amazon Rekognition
  // - Google Cloud Vision API
  // - Microsoft Content Moderator

  // Placeholder implementation
  const sensitiveTerms = {
    sexual: ['porn', 'sex', 'nude', 'naked', 'xxx'],
    violence: ['kill', 'murder', 'attack', 'bomb', 'shoot'],
    hate: ['hate', 'racist', 'nazi', 'bigot'],
    harassment: ['harass', 'stalk', 'bully'],
    selfHarm: ['suicide', 'self-harm', 'cut myself'],
    spam: ['buy now', 'click here', 'free money', 'winner']
  };

  const results = {
    score: 0,
    categories: {
      sexual: 0,
      violence: 0,
      hate: 0,
      harassment: 0,
      selfHarm: 0,
      sexualMinors: 0,
      hateThreatening: 0,
      violenceGraphic: 0,
      selfHarmIntent: 0,
      selfHarmInstructions: 0,
      harassmentThreatening: 0,
      spam: 0
    },
    flaggedKeywords: [],
    suggestedAction: 'none',
    explanation: ''
  };

  if (text) {
    const lowerText = text.toLowerCase();
    
    // Check each category
    for (const [category, terms] of Object.entries(sensitiveTerms)) {
      for (const term of terms) {
        if (lowerText.includes(term)) {
          results.categories[category] = Math.max(results.categories[category], 0.8);
          results.flaggedKeywords.push(term);
        }
      }
    }
  }

  // Calculate overall score (maximum category score)
  results.score = Math.max(...Object.values(results.categories));
  
  // Generate explanation
  if (results.flaggedKeywords.length > 0) {
    results.explanation = `Content may contain sensitive material related to: ${
      Object.entries(results.categories)
        .filter(([_, score]) => score > 0.5)
        .map(([category]) => category)
        .join(', ')
    }`;
  }

  return results;
};

/**
 * Take action on content based on moderation decision
 */
const takeContentAction = async (contentType, contentId, action) => {
  switch (contentType) {
    case 'post':
      if (action === 'hidden' || action === 'removed') {
        await Post.findByIdAndUpdate(contentId, { 
          isHidden: true,
          hiddenReason: 'Content moderation'
        });
      }
      break;
    case 'reel':
      if (action === 'hidden' || action === 'removed') {
        await Reel.findByIdAndUpdate(contentId, { 
          isHidden: true,
          hiddenReason: 'Content moderation'
        });
      }
      break;
    case 'comment':
      if (action === 'hidden' || action === 'removed') {
        await Comment.findByIdAndUpdate(contentId, { 
          isHidden: true,
          hiddenReason: 'Content moderation'
        });
      }
      break;
    case 'message':
      if (action === 'hidden' || action === 'removed') {
        await Message.findByIdAndUpdate(contentId, { 
          isHidden: true,
          hiddenReason: 'Content moderation'
        });
      }
      break;
    case 'stream_chat':
      if (action === 'hidden' || action === 'removed') {
        await LiveStreamChat.findByIdAndUpdate(contentId, { 
          isDeleted: true,
          deletedReason: 'Content moderation',
          deletedAt: Date.now()
        });
      }
      break;
    default:
      break;
  }
};
