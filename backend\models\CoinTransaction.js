const mongoose = require('mongoose');

const CoinTransactionSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  type: {
    type: String,
    enum: ['purchase', 'spend', 'gift', 'refund', 'reward'],
    required: true
  },
  amount: {
    type: Number,
    required: true
  },
  paymentMethod: {
    type: String,
    enum: ['credit_card', 'paypal', 'crypto', 'bank_transfer', 'app_store', 'google_play', null],
    default: null
  },
  paymentDetails: {
    type: Object,
    default: null
  },
  reason: {
    type: String,
    default: null
  },
  itemId: {
    type: mongoose.Schema.Types.ObjectId,
    default: null
  },
  itemType: {
    type: String,
    enum: ['gift', 'reaction', 'subscription', 'product', 'nft', null],
    default: null
  },
  status: {
    type: String,
    enum: ['pending', 'completed', 'failed', 'refunded'],
    default: 'pending'
  },
  createdAt: {
    type: Date,
    default: Date.now
  }
});

module.exports = mongoose.model('CoinTransaction', CoinTransactionSchema);
