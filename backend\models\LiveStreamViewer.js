const mongoose = require('mongoose');

const LiveStreamViewerSchema = new mongoose.Schema({
  stream: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'LiveStream',
    required: true,
  },
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  joinedAt: {
    type: Date,
    default: Date.now,
  },
  leftAt: {
    type: Date,
  },
  duration: {
    type: Number, // in seconds
  },
  isActive: {
    type: Boolean,
    default: true,
  },
  device: {
    type: String,
    enum: ['mobile', 'tablet', 'desktop', 'other'],
    default: 'other',
  },
  interactions: {
    chatMessages: {
      type: Number,
      default: 0,
    },
    reactions: {
      type: Number,
      default: 0,
    },
    gifts: {
      type: Number,
      default: 0,
    },
  },
}, { timestamps: true });

// Create indexes for efficient queries
LiveStreamViewerSchema.index({ stream: 1, user: 1 });
LiveStreamViewerSchema.index({ stream: 1, isActive: 1 });
LiveStreamViewerSchema.index({ user: 1, isActive: 1 });

module.exports = mongoose.model('LiveStreamViewer', LiveStreamViewerSchema);
