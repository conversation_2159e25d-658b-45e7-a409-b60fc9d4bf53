const User = require('../models/User');
const Follow = require('../models/Follow');
const Post = require('../models/Post');
const { createError } = require('../utils/error');
const { cloudinary, uploadToCloudinary } = require('../config/cloudinary');

/**
 * Get all users
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.getUsers = async (req, res, next) => {
  try {
    const users = await User.find().select('-password');

    res.status(200).json({
      success: true,
      count: users.length,
      data: users,
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Get user by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.getUser = async (req, res, next) => {
  try {
    const user = await User.findById(req.params.id).select('-password');

    if (!user) {
      return next(createError(404, 'User not found'));
    }

    res.status(200).json({
      success: true,
      data: user,
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Get user by username
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.getUserByUsername = async (req, res, next) => {
  try {
    const user = await User.findOne({ username: req.params.username }).select('-password');

    if (!user) {
      return next(createError(404, 'User not found'));
    }

    // Get follower and following counts
    const [followerCount, followingCount, postCount] = await Promise.all([
      Follow.countDocuments({ following: user._id }),
      Follow.countDocuments({ follower: user._id }),
      Post.countDocuments({ user: user._id }),
    ]);

    // Check if current user is following this user
    let isFollowing = false;
    // Only check following status if user is authenticated
    if (req.user && req.user.id) {
      const followRecord = await Follow.findOne({
        follower: req.user.id,
        following: user._id,
      });
      isFollowing = !!followRecord;
    }

    const userData = {
      ...user.toObject(),
      followerCount,
      followingCount,
      postCount,
      isFollowing,
    };

    res.status(200).json({
      success: true,
      data: userData,
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Get current user profile
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.getCurrentUserProfile = async (req, res, next) => {
  try {
    const user = await User.findById(req.user.id).select('-password');

    if (!user) {
      return next(createError(404, 'User not found'));
    }

    // Get follower and following counts
    const [followerCount, followingCount, postCount] = await Promise.all([
      Follow.countDocuments({ following: user._id }),
      Follow.countDocuments({ follower: user._id }),
      Post.countDocuments({ user: user._id }),
    ]);

    const userData = {
      ...user.toObject(),
      followerCount,
      followingCount,
      postCount,
    };

    res.status(200).json({
      success: true,
      data: userData,
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Update user profile
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.updateProfile = async (req, res, next) => {
  try {
    const { name, bio, website, location, isPrivate } = req.body;

    // Build update object
    const updateData = {};
    if (name) updateData.name = name;
    if (bio !== undefined) updateData.bio = bio;
    if (website !== undefined) updateData.website = website;

    // Handle location as GeoJSON object
    if (location !== undefined) {
      // Check if location is a GeoJSON object
      if (location.type && location.coordinates) {
        // Validate coordinates
        if (Array.isArray(location.coordinates) && location.coordinates.length === 2) {
          // Valid GeoJSON Point
          updateData.location = {
            type: 'Point',
            coordinates: location.coordinates,
            name: location.name || ''
          };
        } else {
          return next(createError(400, 'Invalid location coordinates format'));
        }
      } else if (typeof location === 'string') {
        // If location is just a string, keep the existing coordinates if any
        const user = await User.findById(req.user.id);
        updateData.location = {
          type: 'Point',
          coordinates: user.location?.coordinates || [0, 0],
          name: location
        };
      } else {
        return next(createError(400, 'Invalid location format'));
      }
    }

    if (isPrivate !== undefined) updateData.isPrivate = isPrivate;

    const user = await User.findByIdAndUpdate(
      req.user.id,
      { $set: updateData },
      { new: true, runValidators: true }
    ).select('-password');

    if (!user) {
      return next(createError(404, 'User not found'));
    }

    res.status(200).json({
      success: true,
      data: user,
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Update profile picture
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.updateProfilePicture = async (req, res, next) => {
  try {
    console.log('Updating profile picture...');
    console.log('Request user:', req.user ? req.user.id : 'No user');
    console.log('Request file:', req.file ? 'File exists' : 'No file');

    // Ensure user exists and has proper permissions
    if (!req.user || !req.user.id) {
      return next(createError(401, 'User not authenticated'));
    }

    // Check if file was uploaded via Multer
    if (req.file && req.file.cloudinaryUrl) {
      // File already uploaded to Cloudinary by middleware
      const profilePictureUrl = req.file.cloudinaryUrl;
      console.log('Profile picture URL from Cloudinary:', profilePictureUrl);

      try {
        // Update user profile
        const user = await User.findByIdAndUpdate(
          req.user.id,
          { $set: { profilePicture: profilePictureUrl } },
          { new: true, runValidators: true }
        ).select('-password');

        if (!user) {
          return next(createError(404, 'User not found'));
        }

        return res.status(200).json({
          success: true,
          data: user,
        });
      } catch (updateError) {
        console.error('Error updating user profile:', updateError);
        return next(createError(500, 'Failed to update user profile'));
      }
    }
    // Check if file was uploaded via express-fileupload
    else if (req.files && (req.files.image || req.files.profilePicture)) {
      const file = req.files.image || req.files.profilePicture;
      console.log('Using express-fileupload with field:', req.files.image ? 'image' : 'profilePicture');

      // Check file size
      if (file.size > 5 * 1024 * 1024) {
        return next(createError(400, 'Image size should be less than 5MB'));
      }

      // Check file type
      if (!file.mimetype.startsWith('image')) {
        return next(createError(400, 'Please upload an image file'));
      }

      try {
        // Upload to Cloudinary with minimal options
        const result = await uploadToCloudinary(file.tempFilePath, 'letstalk/profiles', {
          resource_type: 'auto'
        });

        console.log('Uploaded to Cloudinary:', result.secure_url);

        // Update user profile
        const user = await User.findByIdAndUpdate(
          req.user.id,
          { $set: { profilePicture: result.secure_url } },
          { new: true, runValidators: true }
        ).select('-password');

        if (!user) {
          return next(createError(404, 'User not found'));
        }

        return res.status(200).json({
          success: true,
          data: user,
        });
      } catch (uploadError) {
        console.error('Error uploading to Cloudinary:', uploadError);
        return next(createError(500, 'Failed to upload image to cloud storage'));
      }
    } else {
      console.error('No file found in the request');
      return next(createError(400, 'Please upload an image'));
    }
  } catch (err) {
    console.error('Error updating profile picture:', err);
    return next(createError(500, 'Server error while processing profile picture update'));
  }
};

/**
 * Search users
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.searchUsers = async (req, res, next) => {
  try {
    const { query } = req.query;

    if (!query) {
      return next(createError(400, 'Please provide a search query'));
    }

    const users = await User.find({
      $or: [
        { username: { $regex: query, $options: 'i' } },
        { name: { $regex: query, $options: 'i' } },
      ],
    }).select('username name profilePicture isVerified');

    res.status(200).json({
      success: true,
      count: users.length,
      data: users,
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Get suggested users
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.getSuggestedUsers = async (req, res, next) => {
  try {
    // Get users that the current user is following
    const following = await Follow.find({ follower: req.user.id });
    const followingIds = following.map(follow => follow.following);

    // Add current user ID to exclude from suggestions
    followingIds.push(req.user.id);

    // Find users that the current user is not following
    const users = await User.find({ _id: { $nin: followingIds } })
      .select('username name profilePicture isVerified')
      .limit(5);

    res.status(200).json({
      success: true,
      count: users.length,
      data: users,
    });
  } catch (err) {
    next(err);
  }
};
