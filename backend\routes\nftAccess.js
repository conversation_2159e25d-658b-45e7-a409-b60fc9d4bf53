const express = require('express');
const router = express.Router({ mergeParams: true });
const { protect, optionalAuth } = require('../middleware/auth');
const {
  getNFTAccessTokens,
  getNFTAccessToken,
  createNFTAccessToken,
  updateNFTAccessToken,
  checkAccess,
} = require('../controllers/nftAccessController');

// Public routes
router.get('/', optionalAuth, getNFTAccessTokens);
router.get('/:id', optionalAuth, getNFTAccessToken);

// Protected routes
router.use(protect);

router.post('/', createNFTAccessToken);
router.put('/:id', updateNFTAccessToken);
router.get('/check', checkAccess);

module.exports = router;
