<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover" />
    <meta name="theme-color" content="#5271FF" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="default" />
    <title>Let's Talk</title>
  </head>
  <body>
    <div id="root"></div>
    <!-- Load dynamic configuration -->
    <script id="dynamic-config" type="application/json">
      {"apiBaseUrl":"http://localhost:10001","socketUrl":"http://localhost:10001"}
    </script>
    <script>
      // Load dynamic configuration from the script tag or fetch it
      (function loadDynamicConfig() {
        // Default configuration as fallback
        const defaultConfig = {
          apiBaseUrl: 'http://localhost:10001',
          socketUrl: 'http://localhost:10001'
        };

        // Try to load from script tag first
        try {
          const configElement = document.getElementById('dynamic-config');
          if (configElement && configElement.textContent.trim()) {
            const configText = configElement.textContent.trim();
            try {
              window.dynamicConfig = JSON.parse(configText);
              console.log('Loaded dynamic config from script tag:', window.dynamicConfig);
              return; // Successfully loaded
            } catch (parseError) {
              console.error('Error parsing JSON from script tag:', parseError);
              // Continue to next method
            }
          }
        } catch (scriptError) {
          console.error('Error accessing script tag:', scriptError);
          // Continue to next method
        }

        // If we're here, try to fetch the configuration file
        fetch('/dynamic-config.json')
          .then(response => {
            if (!response.ok) {
              throw new Error(`HTTP error! Status: ${response.status}`);
            }
            return response.json();
          })
          .then(config => {
            window.dynamicConfig = config;
            console.log('Loaded dynamic config from file:', window.dynamicConfig);
          })
          .catch(error => {
            console.error('Error loading dynamic config from file:', error);
            // Use default values
            window.dynamicConfig = defaultConfig;
            console.warn('Using fallback configuration:', window.dynamicConfig);
          });
      })();
    </script>
    <script type="module" src="/src/main.jsx"></script>
  </body>
</html>
