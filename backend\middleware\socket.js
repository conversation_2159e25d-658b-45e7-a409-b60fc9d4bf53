const jwt = require('jsonwebtoken');
const User = require('../models/User');
const Conversation = require('../models/Conversation');
const LiveStream = require('../models/LiveStream');
const LiveStreamViewer = require('../models/LiveStreamViewer');
const { createError } = require('../utils/error');
const socketEmitter = require('../utils/socketEmitter');

/**
 * Socket.IO middleware for authentication and connection handling
 * @param {Object} io - Socket.IO server instance
 */
const socketMiddleware = (io) => {
  // Authentication middleware
  io.use(async (socket, next) => {
    try {
      const token = socket.handshake.auth.token;

      if (!token) {
        return next(createError(401, 'Authentication token is required'));
      }

      // Verify token
      const decoded = jwt.verify(token, process.env.JWT_SECRET);

      // Get user from database
      const user = await User.findById(decoded.id).select('-password');

      if (!user) {
        return next(createError(404, 'User not found'));
      }

      // Attach user to socket
      socket.user = user;
      next();
    } catch (err) {
      if (err.name === 'JsonWebTokenError') {
        return next(createError(401, 'Invalid token'));
      } else if (err.name === 'TokenExpiredError') {
        return next(createError(401, 'Token expired'));
      }

      return next(createError(500, 'Server error'));
    }
  });

  // Connection handler
  io.on('connection', (socket) => {
    console.log(`User connected: ${socket.user.username} (${socket.id})`);

    // Join user's personal room for private notifications
    socket.on('join', ({ userId }) => {
      if (socket.user._id.toString() === userId) {
        socket.join(`user:${userId}`);
        console.log(`User ${socket.user.username} joined room: user:${userId}`);
      }
    });

    // Join conversation rooms
    socket.on('join_conversations', async () => {
      try {
        // Find all conversations where user is a participant
        const conversations = await Conversation.find({
          participants: socket.user._id,
        }).select('_id');

        // Join each conversation room
        conversations.forEach(conversation => {
          const roomId = `conversation:${conversation._id.toString()}`;
          socket.join(roomId);
          console.log(`User ${socket.user.username} joined room: ${roomId}`);
        });

        socket.emit('conversations_joined', {
          count: conversations.length,
        });
      } catch (err) {
        console.error('Socket join conversations error:', err);
      }
    });

    // Join a specific conversation room
    socket.on('join_conversation', async ({ conversationId }) => {
      try {
        // Check if user is a participant
        const conversation = await Conversation.findById(conversationId);

        if (!conversation) {
          socket.emit('error', {
            message: 'Conversation not found',
          });
          return;
        }

        if (!conversation.participants.includes(socket.user._id)) {
          socket.emit('error', {
            message: 'You are not a participant in this conversation',
          });
          return;
        }

        // Join the conversation room
        const roomId = `conversation:${conversationId}`;
        socket.join(roomId);
        console.log(`User ${socket.user.username} joined room: ${roomId}`);

        socket.emit('conversation_joined', {
          conversationId,
        });
      } catch (err) {
        console.error('Socket join conversation error:', err);
        socket.emit('error', {
          message: 'Failed to join conversation',
        });
      }
    });

    // Handle typing indicator
    socket.on('typing', ({ conversationId, isTyping }) => {
      socketEmitter.emitTypingIndicator(conversationId, socket.user._id.toString(), isTyping);
    });

    // Handle follow events
    socket.on('follow', async ({ userId }) => {
      try {
        // Emit to the target user's room
        io.to(`user:${userId}`).emit('follow', {
          sourceUserId: socket.user._id.toString(),
          targetUserId: userId,
          timestamp: new Date(),
        });

        // Broadcast to all connected clients for real-time updates
        socket.broadcast.emit('user_followed', {
          sourceUserId: socket.user._id.toString(),
          targetUserId: userId,
          timestamp: new Date(),
        });
      } catch (err) {
        console.error('Socket follow error:', err);
      }
    });

    // Handle unfollow events
    socket.on('unfollow', async ({ userId }) => {
      try {
        // Emit to the target user's room
        io.to(`user:${userId}`).emit('unfollow', {
          sourceUserId: socket.user._id.toString(),
          targetUserId: userId,
          timestamp: new Date(),
        });
      } catch (err) {
        console.error('Socket unfollow error:', err);
      }
    });

    // Handle like events
    socket.on('like', async ({ itemType, itemId }) => {
      try {
        // Get the owner of the liked item
        let ownerId;

        if (itemType === 'post') {
          const Post = require('../models/Post');
          const post = await Post.findById(itemId);
          if (post) {
            ownerId = post.user.toString();
          }
        } else if (itemType === 'reel') {
          const Reel = require('../models/Reel');
          const reel = await Reel.findById(itemId);
          if (reel) {
            ownerId = reel.user.toString();
          }
        } else if (itemType === 'comment') {
          const Comment = require('../models/Comment');
          const comment = await Comment.findById(itemId);
          if (comment) {
            ownerId = comment.user.toString();
          }
        }

        if (ownerId && ownerId !== socket.user._id.toString()) {
          // Emit to the owner's room
          io.to(`user:${ownerId}`).emit('like', {
            userId: socket.user._id.toString(),
            itemType,
            itemId,
            timestamp: new Date(),
          });
        }

        // Broadcast to all connected clients for real-time updates
        socket.broadcast.emit('post_liked', {
          userId: socket.user._id.toString(),
          itemType,
          itemId,
          timestamp: new Date(),
        });
      } catch (err) {
        console.error('Socket like error:', err);
      }
    });

    // Handle unlike events
    socket.on('unlike', async ({ itemType, itemId }) => {
      try {
        // Broadcast to all connected clients for real-time updates
        socket.broadcast.emit('post_unliked', {
          userId: socket.user._id.toString(),
          itemType,
          itemId,
          timestamp: new Date(),
        });
      } catch (err) {
        console.error('Socket unlike error:', err);
      }
    });

    // Handle comment events
    socket.on('comment', async ({ itemType, itemId, commentId }) => {
      try {
        // Get the owner of the commented item
        let ownerId;

        if (itemType === 'post') {
          const Post = require('../models/Post');
          const post = await Post.findById(itemId);
          if (post) {
            ownerId = post.user.toString();
          }
        } else if (itemType === 'reel') {
          const Reel = require('../models/Reel');
          const reel = await Reel.findById(itemId);
          if (reel) {
            ownerId = reel.user.toString();
          }
        }

        if (ownerId && ownerId !== socket.user._id.toString()) {
          // Emit to the owner's room
          io.to(`user:${ownerId}`).emit('comment', {
            userId: socket.user._id.toString(),
            itemType,
            itemId,
            commentId,
            timestamp: new Date(),
          });
        }

        // Broadcast to all connected clients for real-time updates
        socket.broadcast.emit('post_commented', {
          userId: socket.user._id.toString(),
          itemType,
          itemId,
          commentId,
          timestamp: new Date(),
        });
      } catch (err) {
        console.error('Socket comment error:', err);
      }
    });

    // Handle notification read events
    socket.on('mark_notification_read', ({ notificationId }) => {
      // Broadcast to user's other devices
      socket.to(`user:${socket.user._id.toString()}`).emit('notification_read', {
        notificationId,
        userId: socket.user._id.toString(),
      });
    });

    // Handle mark all notifications read
    socket.on('mark_all_notifications_read', () => {
      // Broadcast to user's other devices
      socket.to(`user:${socket.user._id.toString()}`).emit('notification_read', {
        allRead: true,
        userId: socket.user._id.toString(),
      });
    });

    // Handle notification deletion
    socket.on('delete_notification', ({ notificationId }) => {
      // Broadcast to user's other devices
      socket.to(`user:${socket.user._id.toString()}`).emit('notification_deleted', {
        notificationId,
        userId: socket.user._id.toString(),
      });
    });

    // Handle story view events
    socket.on('view_story', ({ storyId, userId }) => {
      // Emit to the story owner's room
      io.to(`user:${userId}`).emit('story_viewed', {
        storyId,
        viewerId: socket.user._id.toString(),
        timestamp: new Date(),
      });
    });

    // Handle message read events
    socket.on('mark_messages_read', ({ conversationId }) => {
      socketEmitter.emitMessagesRead(conversationId, socket.user._id.toString());
    });

    // Handle message reaction events
    socket.on('message_reaction', ({ messageId, conversationId, emoji }) => {
      socketEmitter.emitMessageReaction(conversationId, messageId, socket.user._id.toString(), emoji);
    });

    // Handle message reaction removal events
    socket.on('message_reaction_remove', ({ messageId, conversationId }) => {
      socketEmitter.emitMessageReactionRemoved(conversationId, messageId, socket.user._id.toString());
    });

    // Live streaming event handlers

    // Join a live stream room
    socket.on('join_stream', async ({ streamId }) => {
      try {
        // Check if stream exists
        const stream = await LiveStream.findById(streamId);

        if (!stream) {
          socket.emit('error', {
            message: 'Live stream not found',
          });
          return;
        }

        // Check if stream is private and user is allowed to view
        if (stream.isPrivate) {
          const isAllowed = stream.user.toString() === socket.user._id.toString() ||
                          stream.allowedViewers.includes(socket.user._id);

          if (!isAllowed) {
            socket.emit('error', {
              message: 'Not authorized to view this stream',
            });
            return;
          }
        }

        // Join the stream room
        const roomId = `stream:${streamId}`;
        socket.join(roomId);
        console.log(`User ${socket.user.username} joined stream room: ${roomId}`);

        socket.emit('stream_joined', {
          streamId,
        });

        // Update viewer count if stream is live
        if (stream.status === 'live') {
          // Check if user is already an active viewer
          let viewer = await LiveStreamViewer.findOne({
            stream: streamId,
            user: socket.user._id,
            isActive: true,
          });

          if (!viewer) {
            // Check if user was a previous viewer
            viewer = await LiveStreamViewer.findOne({
              stream: streamId,
              user: socket.user._id,
            });

            if (viewer) {
              // Update existing viewer record
              viewer.isActive = true;
              viewer.joinedAt = Date.now();
              viewer.leftAt = null;
              viewer.duration = null;
              viewer.device = socket.handshake.headers['user-agent'] ?
                (socket.handshake.headers['user-agent'].includes('Mobile') ? 'mobile' : 'desktop') :
                'other';
              await viewer.save();
            } else {
              // Create new viewer record
              viewer = await LiveStreamViewer.create({
                stream: streamId,
                user: socket.user._id,
                device: socket.handshake.headers['user-agent'] ?
                  (socket.handshake.headers['user-agent'].includes('Mobile') ? 'mobile' : 'desktop') :
                  'other',
              });
            }

            // Update stream view count
            await LiveStream.findByIdAndUpdate(streamId, {
              $inc: { viewCount: 1, totalViews: 1 },
            });

            // Get current viewer count
            const activeViewers = await LiveStreamViewer.countDocuments({
              stream: streamId,
              isActive: true,
            });

            // Update peak viewers if needed
            if (activeViewers > stream.peakViewers) {
              await LiveStream.findByIdAndUpdate(streamId, {
                peakViewers: activeViewers,
              });
            }

            // Populate user
            await viewer.populate('user', 'username name profilePicture isVerified');

            // Emit socket event for viewer joined
            socketEmitter.emitLiveStreamViewerJoined(viewer, streamId, activeViewers);
          }
        }
      } catch (err) {
        console.error('Socket join stream error:', err);
        socket.emit('error', {
          message: 'Failed to join stream',
        });
      }
    });

    // Leave a live stream room
    socket.on('leave_stream', async ({ streamId }) => {
      try {
        // Leave the stream room
        const roomId = `stream:${streamId}`;
        socket.leave(roomId);
        console.log(`User ${socket.user.username} left stream room: ${roomId}`);

        // Update viewer record if stream is live
        const stream = await LiveStream.findById(streamId);

        if (stream && stream.status === 'live') {
          // Find viewer record
          const viewer = await LiveStreamViewer.findOne({
            stream: streamId,
            user: socket.user._id,
            isActive: true,
          });

          if (viewer) {
            // Update viewer record
            viewer.isActive = false;
            viewer.leftAt = Date.now();
            viewer.duration = (Date.now() - viewer.joinedAt) / 1000; // Duration in seconds
            await viewer.save();

            // Update stream view count
            await LiveStream.findByIdAndUpdate(streamId, {
              $inc: { viewCount: -1 },
            });

            // Get current viewer count
            const activeViewers = await LiveStreamViewer.countDocuments({
              stream: streamId,
              isActive: true,
            });

            // Emit socket event for viewer left
            socketEmitter.emitLiveStreamViewerLeft(viewer._id, socket.user._id.toString(), streamId, activeViewers);
          }
        }

        socket.emit('stream_left', {
          streamId,
        });
      } catch (err) {
        console.error('Socket leave stream error:', err);
      }
    });

    // Send a chat message in a live stream
    socket.on('stream_chat_message', async ({ streamId, message, replyTo, emotions }) => {
      try {
        // Check if stream exists and is live
        const stream = await LiveStream.findById(streamId);

        if (!stream) {
          socket.emit('error', {
            message: 'Live stream not found',
          });
          return;
        }

        if (stream.status !== 'live') {
          socket.emit('error', {
            message: 'Cannot chat in a stream that is not live',
          });
          return;
        }

        // Check if chat is enabled
        if (!stream.settings.chat.enabled) {
          socket.emit('error', {
            message: 'Chat is disabled for this stream',
          });
          return;
        }

        // Create chat message
        const LiveStreamChat = require('../models/LiveStreamChat');
        const chatMessage = await LiveStreamChat.create({
          stream: streamId,
          user: socket.user._id,
          message,
          replyTo,
          emotions,
        });

        // Populate user and emotions
        await chatMessage.populate('user', 'username name profilePicture isVerified');

        if (emotions && emotions.length > 0) {
          await chatMessage.populate('emotions.emotion', 'name color icon category');
        }

        if (replyTo) {
          await chatMessage.populate({
            path: 'replyTo',
            select: 'message user',
            populate: {
              path: 'user',
              select: 'username name profilePicture',
            },
          });
        }

        // Update viewer interaction count
        await LiveStreamViewer.findOneAndUpdate(
          {
            stream: streamId,
            user: socket.user._id,
            isActive: true,
          },
          {
            $inc: { 'interactions.chatMessages': 1 },
          }
        );

        // Emit socket event for new chat message
        socketEmitter.emitLiveStreamChatMessage(chatMessage);
      } catch (err) {
        console.error('Socket stream chat message error:', err);
        socket.emit('error', {
          message: 'Failed to send chat message',
        });
      }
    });

    // Send a reaction in a live stream
    socket.on('stream_reaction', async ({ streamId, type, emoji, giftType, giftValue, giftAnimation, message, emotions }) => {
      try {
        // Check if stream exists and is live
        const stream = await LiveStream.findById(streamId);

        if (!stream) {
          socket.emit('error', {
            message: 'Live stream not found',
          });
          return;
        }

        if (stream.status !== 'live') {
          socket.emit('error', {
            message: 'Cannot react to a stream that is not live',
          });
          return;
        }

        // Check if reactions are enabled
        if (!stream.settings.reactions.enabled) {
          socket.emit('error', {
            message: 'Reactions are disabled for this stream',
          });
          return;
        }

        // Create reaction
        const LiveStreamReaction = require('../models/LiveStreamReaction');
        const reaction = await LiveStreamReaction.create({
          stream: streamId,
          user: socket.user._id,
          type,
          emoji,
          giftType,
          giftValue,
          giftAnimation,
          message,
          emotions,
        });

        // Populate user and emotions
        await reaction.populate('user', 'username name profilePicture isVerified');

        if (emotions && emotions.length > 0) {
          await reaction.populate('emotions.emotion', 'name color icon category');
        }

        // Update viewer interaction count
        await LiveStreamViewer.findOneAndUpdate(
          {
            stream: streamId,
            user: socket.user._id,
            isActive: true,
          },
          {
            $inc: { 'interactions.reactions': 1 },
          }
        );

        // Update stream likes count if reaction is a like
        if (type === 'like') {
          await LiveStream.findByIdAndUpdate(streamId, {
            $inc: { likesCount: 1 },
          });
        }

        // Emit socket event for new reaction
        socketEmitter.emitLiveStreamReaction(reaction);
      } catch (err) {
        console.error('Socket stream reaction error:', err);
        socket.emit('error', {
          message: 'Failed to send reaction',
        });
      }
    });

    // WebRTC signaling events

    // Handle WebRTC offer
    socket.on('webrtc_offer', (data) => {
      try {
        // Validate data
        if (!data.streamId || !data.targetUserId || !data.offer) {
          socket.emit('error', {
            message: 'Invalid WebRTC offer data',
          });
          return;
        }

        // Add fromUserId to data
        data.fromUserId = socket.user._id.toString();

        // Emit to target user
        socketEmitter.emitWebRTCOffer(data);

        console.log(`WebRTC offer sent from ${socket.user.username} to ${data.targetUserId}`);
      } catch (err) {
        console.error('Socket WebRTC offer error:', err);
        socket.emit('error', {
          message: 'Failed to send WebRTC offer',
        });
      }
    });

    // Handle WebRTC answer
    socket.on('webrtc_answer', (data) => {
      try {
        // Validate data
        if (!data.streamId || !data.targetUserId || !data.answer) {
          socket.emit('error', {
            message: 'Invalid WebRTC answer data',
          });
          return;
        }

        // Add fromUserId to data
        data.fromUserId = socket.user._id.toString();

        // Emit to target user
        socketEmitter.emitWebRTCAnswer(data);

        console.log(`WebRTC answer sent from ${socket.user.username} to ${data.targetUserId}`);
      } catch (err) {
        console.error('Socket WebRTC answer error:', err);
        socket.emit('error', {
          message: 'Failed to send WebRTC answer',
        });
      }
    });

    // Handle WebRTC ICE candidate
    socket.on('webrtc_ice_candidate', (data) => {
      try {
        // Validate data
        if (!data.streamId || !data.targetUserId || !data.candidate) {
          socket.emit('error', {
            message: 'Invalid WebRTC ICE candidate data',
          });
          return;
        }

        // Add fromUserId to data
        data.fromUserId = socket.user._id.toString();

        // Emit to target user
        socketEmitter.emitWebRTCIceCandidate(data);
      } catch (err) {
        console.error('Socket WebRTC ICE candidate error:', err);
        socket.emit('error', {
          message: 'Failed to send WebRTC ICE candidate',
        });
      }
    });

    // Handle host joining a stream
    socket.on('join_host', async ({ streamId }) => {
      try {
        // Check if stream exists
        const stream = await LiveStream.findById(streamId);

        if (!stream) {
          socket.emit('error', {
            message: 'Live stream not found',
          });
          return;
        }

        // Check if user is allowed to be a host
        const isMainHost = stream.user.toString() === socket.user._id.toString();
        const isCoHost = stream.coHosts?.some(coHost => coHost.toString() === socket.user._id.toString());

        if (!isMainHost && !isCoHost) {
          socket.emit('error', {
            message: 'Not authorized to be a host in this stream',
          });
          return;
        }

        // Join the host room
        const roomId = `stream:${streamId}:hosts`;
        socket.join(roomId);
        console.log(`Host ${socket.user.username} joined host room: ${roomId}`);

        // Emit host joined event
        io.to(`stream:${streamId}`).emit('stream_host_joined', {
          streamId,
          host: {
            _id: socket.user._id.toString(),
            name: socket.user.name,
            username: socket.user.username,
            profilePicture: socket.user.profilePicture,
            isMainHost: isMainHost
          }
        });

        socket.emit('host_joined', {
          streamId,
        });
      } catch (err) {
        console.error('Socket join host error:', err);
        socket.emit('error', {
          message: 'Failed to join as host',
        });
      }
    });

    // Handle host leaving a stream
    socket.on('host_left', ({ streamId }) => {
      try {
        // Leave the host room
        const roomId = `stream:${streamId}:hosts`;
        socket.leave(roomId);
        console.log(`Host ${socket.user.username} left host room: ${roomId}`);

        // Emit host left event
        io.to(`stream:${streamId}`).emit('stream_host_left', {
          streamId,
          hostId: socket.user._id.toString()
        });

        socket.emit('host_left', {
          streamId,
        });
      } catch (err) {
        console.error('Socket host left error:', err);
      }
    });

    // Handle host mute change
    socket.on('host_mute_change', ({ streamId, isMuted }) => {
      try {
        // Emit host mute change event
        io.to(`stream:${streamId}`).emit('stream_host_mute_change', {
          streamId,
          hostId: socket.user._id.toString(),
          isMuted
        });
      } catch (err) {
        console.error('Socket host mute change error:', err);
      }
    });

    // Handle host video change
    socket.on('host_video_change', ({ streamId, isVideoOff }) => {
      try {
        // Emit host video change event
        io.to(`stream:${streamId}`).emit('stream_host_video_change', {
          streamId,
          hostId: socket.user._id.toString(),
          isVideoOff
        });
      } catch (err) {
        console.error('Socket host video change error:', err);
      }
    });

    // Handle host screen sharing change
    socket.on('host_screen_sharing_change', ({ streamId, isScreenSharing }) => {
      try {
        if (!streamId) {
          socket.emit('error', {
            message: 'Invalid stream ID',
          });
          return;
        }

        console.log(`Host ${socket.user.username} ${isScreenSharing ? 'started' : 'stopped'} screen sharing in stream ${streamId}`);

        // Emit host screen sharing change event
        io.to(`stream:${streamId}`).emit('stream_host_screen_sharing_change', {
          streamId,
          hostId: socket.user._id.toString(),
          isScreenSharing
        });
      } catch (err) {
        console.error('Socket host screen sharing change error:', err);
        socket.emit('error', {
          message: 'Failed to update screen sharing status',
        });
      }
    });

    // Handle host ready event
    socket.on('host_ready', ({ streamId }) => {
      try {
        if (!streamId) {
          socket.emit('error', {
            message: 'Invalid stream ID',
          });
          return;
        }

        console.log(`Host ${socket.user.username} is ready in stream ${streamId}`);

        // Emit host ready event to all hosts in the stream
        io.to(`stream:${streamId}:hosts`).emit('host_ready', {
          streamId,
          userId: socket.user._id.toString()
        });
      } catch (err) {
        console.error('Socket host ready error:', err);
        socket.emit('error', {
          message: 'Failed to signal host ready',
        });
      }
    });

    // Handle disconnection
    socket.on('disconnect', async () => {
      console.log(`User disconnected: ${socket.user.username} (${socket.id})`);

      try {
        // Update viewer records for any active streams the user was viewing
        const activeViewers = await LiveStreamViewer.find({
          user: socket.user._id,
          isActive: true,
        });

        for (const viewer of activeViewers) {
          // Update viewer record
          viewer.isActive = false;
          viewer.leftAt = Date.now();
          viewer.duration = (Date.now() - viewer.joinedAt) / 1000; // Duration in seconds
          await viewer.save();

          // Update stream view count
          await LiveStream.findByIdAndUpdate(viewer.stream, {
            $inc: { viewCount: -1 },
          });

          // Get current viewer count
          const currentActiveViewers = await LiveStreamViewer.countDocuments({
            stream: viewer.stream,
            isActive: true,
          });

          // Emit socket event for viewer left
          socketEmitter.emitLiveStreamViewerLeft(
            viewer._id,
            socket.user._id.toString(),
            viewer.stream.toString(),
            currentActiveViewers
          );
        }
      } catch (err) {
        console.error('Socket disconnect error:', err);
      }
    });
  });
};

module.exports = socketMiddleware;
