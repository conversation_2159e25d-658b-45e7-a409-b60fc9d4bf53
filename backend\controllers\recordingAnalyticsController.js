/**
 * Recording Analytics Controller
 * Handles analytics tracking for stream recordings
 */

const StreamRecording = require('../models/StreamRecording');
const { createError } = require('../utils/error');

/**
 * Track recording view
 * @route POST /api/live-streams/recordings/:id/analytics/view
 * @access Public
 */
exports.trackView = async (req, res, next) => {
  try {
    const { id } = req.params;

    // Get the recording
    const recording = await StreamRecording.findById(id);
    if (!recording) {
      return next(createError(404, 'Recording not found'));
    }

    // Get basic information from headers
    const userAgent = req.headers['user-agent'] || 'unknown';
    const referrer = req.headers.referer || 'direct';

    // Simplified browser detection
    const browser = userAgent.includes('Firefox') ? 'Firefox' :
                   userAgent.includes('Chrome') ? 'Chrome' :
                   userAgent.includes('Safari') ? 'Safari' :
                   userAgent.includes('Edge') ? 'Edge' :
                   userAgent.includes('MSIE') || userAgent.includes('Trident') ? 'Internet Explorer' :
                   'Other';

    // Simplified device detection
    const device = userAgent.includes('Mobile') ? 'mobile' :
                  userAgent.includes('Tablet') ? 'tablet' :
                  'desktop';

    // Default country (we can't detect without geoip-lite)
    const country = 'unknown';

    // Update views count
    recording.views += 1;

    // Add to views history
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const viewHistoryEntry = recording.analytics.viewsHistory.find(
      entry => new Date(entry.date).setHours(0, 0, 0, 0) === today.getTime()
    );

    if (viewHistoryEntry) {
      viewHistoryEntry.count += 1;
    } else {
      recording.analytics.viewsHistory.push({
        date: today,
        count: 1
      });
    }

    // Update demographics
    // Countries
    const countryEntry = recording.analytics.demographics.countries.find(
      entry => entry.country === country
    );

    if (countryEntry) {
      countryEntry.count += 1;
    } else {
      recording.analytics.demographics.countries.push({
        country,
        count: 1
      });
    }

    // Devices
    const deviceEntry = recording.analytics.demographics.devices.find(
      entry => entry.device === device
    );

    if (deviceEntry) {
      deviceEntry.count += 1;
    } else {
      recording.analytics.demographics.devices.push({
        device,
        count: 1
      });
    }

    // Browsers
    const browserEntry = recording.analytics.demographics.browsers.find(
      entry => entry.browser === browser
    );

    if (browserEntry) {
      browserEntry.count += 1;
    } else {
      recording.analytics.demographics.browsers.push({
        browser,
        count: 1
      });
    }

    // Referrers
    const referrerEntry = recording.analytics.referrers.find(
      entry => entry.source === referrer
    );

    if (referrerEntry) {
      referrerEntry.count += 1;
    } else {
      recording.analytics.referrers.push({
        source: referrer,
        count: 1
      });
    }

    // Save the recording
    await recording.save();

    res.status(200).json({
      success: true,
      message: 'View tracked successfully',
      data: {
        views: recording.views
      }
    });
  } catch (err) {
    console.error('Error tracking view:', err);
    next(err);
  }
};

/**
 * Track recording engagement
 * @route POST /api/live-streams/recordings/:id/analytics/engagement
 * @access Public
 */
exports.trackEngagement = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { action, watchTime } = req.body;

    if (!action) {
      return next(createError(400, 'Action is required'));
    }

    // Get the recording
    const recording = await StreamRecording.findById(id);
    if (!recording) {
      return next(createError(404, 'Recording not found'));
    }

    // Update engagement metrics based on action
    switch (action) {
      case 'like':
        recording.analytics.engagement.likes += 1;
        break;
      case 'unlike':
        recording.analytics.engagement.likes = Math.max(0, recording.analytics.engagement.likes - 1);
        break;
      case 'comment':
        recording.analytics.engagement.comments += 1;
        break;
      case 'share':
        recording.analytics.engagement.shares += 1;
        break;
      case 'completed':
        // Update completion rate
        const completions = recording.analytics.completionRate * recording.views;
        recording.analytics.completionRate = (completions + 1) / (recording.views || 1);
        break;
      default:
        // No action needed
        break;
    }

    // Update watch time if provided
    if (watchTime && watchTime > 0) {
      recording.analytics.totalWatchTime += watchTime;
      recording.analytics.averageWatchTime = recording.analytics.totalWatchTime / (recording.views || 1);
    }

    // Save the recording
    await recording.save();

    res.status(200).json({
      success: true,
      message: 'Engagement tracked successfully',
      data: {
        engagement: recording.analytics.engagement,
        totalWatchTime: recording.analytics.totalWatchTime,
        averageWatchTime: recording.analytics.averageWatchTime,
        completionRate: recording.analytics.completionRate
      }
    });
  } catch (err) {
    console.error('Error tracking engagement:', err);
    next(err);
  }
};

/**
 * Get recording analytics
 * @route GET /api/live-streams/recordings/:id/analytics
 * @access Private (owner only)
 */
exports.getAnalytics = async (req, res, next) => {
  try {
    const { id } = req.params;

    // Get the recording
    const recording = await StreamRecording.findById(id);
    if (!recording) {
      return next(createError(404, 'Recording not found'));
    }

    // Check if user is authorized to view analytics
    if (recording.user.toString() !== req.user.id) {
      return next(createError(403, 'You are not authorized to view analytics for this recording'));
    }

    res.status(200).json({
      success: true,
      data: {
        views: recording.views,
        likes: recording.likes,
        analytics: recording.analytics
      }
    });
  } catch (err) {
    console.error('Error getting analytics:', err);
    next(err);
  }
};
