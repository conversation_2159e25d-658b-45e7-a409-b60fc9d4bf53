const mongoose = require('mongoose');

const StreamViewSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  stream: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'LiveStream',
    required: true
  },
  duration: {
    type: Number,
    default: 0
  },
  completionPercentage: {
    type: Number,
    default: 0
  },
  viewedAt: {
    type: Date,
    default: Date.now
  },
  device: {
    type: String,
    enum: ['mobile', 'tablet', 'desktop', 'tv', 'other'],
    default: 'other'
  },
  location: {
    type: {
      type: String,
      enum: ['Point'],
      default: 'Point'
    },
    coordinates: {
      type: [Number],
      default: [0, 0]
    }
  },
  reactions: [
    {
      type: {
        type: String,
        enum: ['like', 'love', 'wow', 'haha', 'sad', 'angry', 'custom'],
        required: true
      },
      timestamp: {
        type: Date,
        default: Date.now
      }
    }
  ],
  comments: [
    {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Comment'
    }
  ],
  emotions: [
    {
      emotion: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Emotion'
      },
      intensity: {
        type: Number,
        min: 1,
        max: 10,
        default: 5
      },
      timestamp: {
        type: Date,
        default: Date.now
      }
    }
  ]
});

// Create compound index for user and stream
StreamViewSchema.index({ user: 1, stream: 1 }, { unique: true });

// Create index for location
StreamViewSchema.index({ location: '2dsphere' });

module.exports = mongoose.model('StreamView', StreamViewSchema);
