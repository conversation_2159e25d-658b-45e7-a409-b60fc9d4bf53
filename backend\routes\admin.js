const express = require('express');
const router = express.Router();
const { protect, authorize } = require('../middleware/auth');
const adminController = require('../controllers/adminController');

// Apply authentication and admin authorization to all routes
router.use(protect);
router.use(authorize('admin'));

// Dashboard routes
router.get('/dashboard', adminController.getDashboardStats);

// Vendor management routes
router.get('/vendors', adminController.getVendors);
router.put('/vendors/:id/approve', adminController.approveVendor);
router.put('/vendors/:id/ban', adminController.banVendor);

// Product management routes
router.get('/products', adminController.getProducts);
router.get('/products/:id', adminController.getProduct);
router.put('/products/:id/status', adminController.updateProductStatus);
router.delete('/products/:id', adminController.deleteProduct);

// Category management routes
router.get('/categories', adminController.getCategories);
router.post('/categories', adminController.createCategory);
router.put('/categories/:id', adminController.updateCategory);
router.delete('/categories/:id', adminController.deleteCategory);

// Order management routes
router.get('/orders', adminController.getOrders);
router.put('/orders/:id/status', adminController.updateOrderStatus);

// Analytics routes
router.get('/analytics/revenue', adminController.getRevenueAnalytics);
router.get('/analytics/commissions', adminController.getCommissionAnalytics);

// Promotion management routes
router.get('/promotions', adminController.getPromotions);
router.post('/promotions', adminController.createPromotion);
router.put('/promotions/:id', adminController.updatePromotion);
router.delete('/promotions/:id', adminController.deletePromotion);

// Advanced reporting routes
router.get('/reports/advanced', adminController.getAdvancedReports);

module.exports = router;
