const Analytics = require('../models/Analytics');
const { LoyaltyProgram, UserLoyalty } = require('../models/LoyaltyProgram');
const Product = require('../models/Product');
const Order = require('../models/Order');
const Cart = require('../models/Cart');
const User = require('../models/User');
const Post = require('../models/Post');
const Reel = require('../models/Reel');
const Comment = require('../models/Comment');
const mongoose = require('mongoose');

/**
 * @desc    Get account overview analytics
 * @route   GET /api/analytics/account-overview
 * @access  Private
 */

exports.getAccountOverview = async (req, res) => {
  try {
    const userId = req.user._id;

    // Get user data
    const user = await User.findById(userId);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Get follower count history (simulated for now)
    // In a real implementation, this would come from a time-series database or analytics collection
    const today = new Date();
    const followerHistory = [];

    // Generate 30 days of follower history data
    for (let i = 29; i >= 0; i--) {
      const date = new Date(today);
      date.setDate(date.getDate() - i);

      // Simulate some growth pattern
      const baseCount = user.followers.length;
      const randomFactor = Math.random() * 0.1; // Up to 10% variation
      const growthFactor = 1 - (i / 30); // Linear growth factor

      const count = Math.floor(baseCount * growthFactor * (1 - randomFactor));

      followerHistory.push({
        date: date.toISOString().split('T')[0],
        count
      });
    }

    // Get post count
    const postCount = await Post.countDocuments({ user: userId, isArchived: false });

    // Get reel count
    const reelCount = await Reel.countDocuments({ user: userId, isArchived: false });

    // Get total likes received
    const posts = await Post.find({ user: userId, isArchived: false });
    const reels = await Reel.find({ user: userId, isArchived: false });

    const totalLikes = posts.reduce((sum, post) => sum + post.likes.length, 0) +
                       reels.reduce((sum, reel) => sum + reel.likes.length, 0);

    // Get total comments received
    const totalComments = posts.reduce((sum, post) => sum + post.comments.length, 0) +
                         reels.reduce((sum, reel) => sum + reel.comments.length, 0);

    // Get engagement rate
    const totalContent = postCount + reelCount;
    const engagementRate = totalContent > 0 ?
      ((totalLikes + totalComments) / (totalContent * user.followers.length)) * 100 : 0;

    res.status(200).json({
      success: true,
      data: {
        followerCount: user.followers.length,
        followingCount: user.following.length,
        postCount,
        reelCount,
        totalLikes,
        totalComments,
        engagementRate: parseFloat(engagementRate.toFixed(2)),
        followerHistory
      }
    });
  } catch (error) {
    console.error('Get account overview error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

/**
 * @desc    Get content performance analytics
 * @route   GET /api/analytics/content-performance
 * @access  Private
 */
exports.getContentPerformance = async (req, res) => {
  try {
    const userId = req.user._id;
    const { period = '30days', contentType } = req.query;

    // Calculate date range based on period
    const endDate = new Date();
    let startDate = new Date();

    switch (period) {
      case '7days':
        startDate.setDate(endDate.getDate() - 7);
        break;
      case '30days':
        startDate.setDate(endDate.getDate() - 30);
        break;
      case '90days':
        startDate.setDate(endDate.getDate() - 90);
        break;
      case '1year':
        startDate.setFullYear(endDate.getFullYear() - 1);
        break;
      default:
        startDate.setDate(endDate.getDate() - 30);
    }

    // Build query
    const query = {
      user: userId,
      isArchived: false,
      createdAt: { $gte: startDate, $lte: endDate }
    };

    // Get content based on type
    let posts = [];
    let reels = [];

    if (!contentType || contentType === 'post') {
      posts = await Post.find(query)
        .select('image caption likes comments views createdAt')
        .sort({ createdAt: -1 });
    }

    if (!contentType || contentType === 'reel') {
      reels = await Reel.find(query)
        .select('video thumbnail caption likes comments views createdAt')
        .sort({ createdAt: -1 });
    }

    // Process post data
    const postData = posts.map(post => ({
      id: post._id,
      type: 'post',
      thumbnail: post.image,
      caption: post.caption,
      likes: post.likes.length,
      comments: post.comments.length,
      views: post.views || 0,
      engagementRate: ((post.likes.length + post.comments.length) / Math.max(post.views || 1, 1)) * 100,
      createdAt: post.createdAt
    }));

    // Process reel data
    const reelData = reels.map(reel => ({
      id: reel._id,
      type: 'reel',
      thumbnail: reel.thumbnail,
      caption: reel.caption,
      likes: reel.likes.length,
      comments: reel.comments.length,
      views: reel.views || 0,
      engagementRate: ((reel.likes.length + reel.comments.length) / Math.max(reel.views || 1, 1)) * 100,
      createdAt: reel.createdAt
    }));

    // Combine and sort by engagement rate
    const allContent = [...postData, ...reelData].sort((a, b) => b.engagementRate - a.engagementRate);

    // Calculate summary statistics
    const totalLikes = allContent.reduce((sum, item) => sum + item.likes, 0);
    const totalComments = allContent.reduce((sum, item) => sum + item.comments, 0);
    const totalViews = allContent.reduce((sum, item) => sum + item.views, 0);
    const avgEngagementRate = allContent.length > 0 ?
      allContent.reduce((sum, item) => sum + item.engagementRate, 0) / allContent.length : 0;

    res.status(200).json({
      success: true,
      data: {
        content: allContent,
        summary: {
          totalContent: allContent.length,
          totalLikes,
          totalComments,
          totalViews,
          avgEngagementRate: parseFloat(avgEngagementRate.toFixed(2))
        },
        topPerforming: allContent.slice(0, 5)
      }
    });
  } catch (error) {
    console.error('Get content performance error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

/**
 * @desc    Get audience insights
 * @route   GET /api/analytics/audience-insights
 * @access  Private
 */
exports.getAudienceInsights = async (req, res) => {
  try {
    const userId = req.user._id;

    // Get user's followers
    const user = await User.findById(userId).populate('followers', 'gender lastActive createdAt');

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Process follower data
    const followers = user.followers;

    // Gender distribution
    const genderDistribution = {
      male: 0,
      female: 0,
      other: 0,
      unspecified: 0
    };

    followers.forEach(follower => {
      if (follower.gender === 'male') genderDistribution.male++;
      else if (follower.gender === 'female') genderDistribution.female++;
      else if (follower.gender === 'other') genderDistribution.other++;
      else genderDistribution.unspecified++;
    });

    // Convert to percentages
    const totalFollowers = followers.length;
    if (totalFollowers > 0) {
      genderDistribution.male = parseFloat(((genderDistribution.male / totalFollowers) * 100).toFixed(1));
      genderDistribution.female = parseFloat(((genderDistribution.female / totalFollowers) * 100).toFixed(1));
      genderDistribution.other = parseFloat(((genderDistribution.other / totalFollowers) * 100).toFixed(1));
      genderDistribution.unspecified = parseFloat(((genderDistribution.unspecified / totalFollowers) * 100).toFixed(1));
    }

    // Activity times (based on lastActive)
    const activityHours = Array(24).fill(0);

    followers.forEach(follower => {
      if (follower.lastActive) {
        const hour = new Date(follower.lastActive).getHours();
        activityHours[hour]++;
      }
    });

    // Follower growth over time
    const followerGrowth = [];
    const now = new Date();
    const monthsAgo = new Date(now.getFullYear(), now.getMonth() - 5, 1); // 6 months of data

    // Group followers by month they started following
    const followersByMonth = {};

    // Initialize months
    for (let i = 0; i <= 5; i++) {
      const date = new Date(now.getFullYear(), now.getMonth() - i, 1);
      const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
      followersByMonth[monthKey] = 0;
    }

    // Count followers by month (simulated data for now)
    // In a real implementation, you would track when users followed each other
    followers.forEach(follower => {
      const followDate = follower.createdAt; // Using creation date as a proxy
      if (followDate && followDate >= monthsAgo) {
        const monthKey = `${followDate.getFullYear()}-${String(followDate.getMonth() + 1).padStart(2, '0')}`;
        if (followersByMonth[monthKey] !== undefined) {
          followersByMonth[monthKey]++;
        }
      }
    });

    // Convert to array format
    for (const [month, count] of Object.entries(followersByMonth)) {
      followerGrowth.push({
        month,
        count
      });
    }

    // Sort follower growth by month (ascending)
    followerGrowth.sort((a, b) => a.month.localeCompare(b.month));

    res.status(200).json({
      success: true,
      data: {
        genderDistribution,
        activityHours,
        followerGrowth,
        totalFollowers
      }
    });
  } catch (error) {
    console.error('Get audience insights error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

/**
 * @desc    Get engagement metrics
 * @route   GET /api/analytics/engagement
 * @access  Private
 */
exports.getEngagementMetrics = async (req, res) => {
  try {
    const userId = req.user._id;
    const { period = '30days' } = req.query;

    // Calculate date range based on period
    const endDate = new Date();
    let startDate = new Date();

    switch (period) {
      case '7days':
        startDate.setDate(endDate.getDate() - 7);
        break;
      case '30days':
        startDate.setDate(endDate.getDate() - 30);
        break;
      case '90days':
        startDate.setDate(endDate.getDate() - 90);
        break;
      case '1year':
        startDate.setFullYear(endDate.getFullYear() - 1);
        break;
      default:
        startDate.setDate(endDate.getDate() - 30);
    }

    // Get user data
    const user = await User.findById(userId);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Get posts and reels within date range
    const posts = await Post.find({
      user: userId,
      isArchived: false,
      createdAt: { $gte: startDate, $lte: endDate }
    }).select('likes comments views createdAt');

    const reels = await Reel.find({
      user: userId,
      isArchived: false,
      createdAt: { $gte: startDate, $lte: endDate }
    }).select('likes comments views createdAt');

    // Calculate daily engagement metrics
    const dailyMetrics = {};
    const dateFormat = date => date.toISOString().split('T')[0];

    // Initialize daily metrics for each day in the range
    for (let d = new Date(startDate); d <= endDate; d.setDate(d.getDate() + 1)) {
      const dateKey = dateFormat(d);
      dailyMetrics[dateKey] = {
        likes: 0,
        comments: 0,
        views: 0,
        engagementRate: 0
      };
    }

    // Process posts
    posts.forEach(post => {
      const dateKey = dateFormat(post.createdAt);
      if (dailyMetrics[dateKey]) {
        dailyMetrics[dateKey].likes += post.likes.length;
        dailyMetrics[dateKey].comments += post.comments.length;
        dailyMetrics[dateKey].views += post.views || 0;
      }
    });

    // Process reels
    reels.forEach(reel => {
      const dateKey = dateFormat(reel.createdAt);
      if (dailyMetrics[dateKey]) {
        dailyMetrics[dateKey].likes += reel.likes.length;
        dailyMetrics[dateKey].comments += reel.comments.length;
        dailyMetrics[dateKey].views += reel.views || 0;
      }
    });

    // Calculate engagement rates
    const followerCount = user.followers.length || 1; // Avoid division by zero

    for (const dateKey in dailyMetrics) {
      const metrics = dailyMetrics[dateKey];
      const interactions = metrics.likes + metrics.comments;
      const viewsOrFollowers = Math.max(metrics.views, followerCount);
      metrics.engagementRate = parseFloat(((interactions / viewsOrFollowers) * 100).toFixed(2));
    }

    // Convert to array format
    const engagementData = Object.entries(dailyMetrics).map(([date, metrics]) => ({
      date,
      ...metrics
    }));

    // Sort by date
    engagementData.sort((a, b) => a.date.localeCompare(b.date));

    // Calculate overall metrics
    const totalLikes = engagementData.reduce((sum, day) => sum + day.likes, 0);
    const totalComments = engagementData.reduce((sum, day) => sum + day.comments, 0);
    const totalViews = engagementData.reduce((sum, day) => sum + day.views, 0);
    const totalInteractions = totalLikes + totalComments;
    const overallEngagementRate = parseFloat(((totalInteractions / Math.max(totalViews, followerCount)) * 100).toFixed(2));

    res.status(200).json({
      success: true,
      data: {
        daily: engagementData,
        summary: {
          totalLikes,
          totalComments,
          totalViews,
          totalInteractions,
          overallEngagementRate
        }
      }
    });
  } catch (error) {
    console.error('Get engagement metrics error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

/**
 * @desc    Get reach and impressions
 * @route   GET /api/analytics/reach-impressions
 * @access  Private
 */
exports.getReachAndImpressions = async (req, res) => {
  try {
    const userId = req.user._id;
    const { period = '30days' } = req.query;

    // Calculate date range based on period
    const endDate = new Date();
    let startDate = new Date();

    switch (period) {
      case '7days':
        startDate.setDate(endDate.getDate() - 7);
        break;
      case '30days':
        startDate.setDate(endDate.getDate() - 30);
        break;
      case '90days':
        startDate.setDate(endDate.getDate() - 90);
        break;
      case '1year':
        startDate.setFullYear(endDate.getFullYear() - 1);
        break;
      default:
        startDate.setDate(endDate.getDate() - 30);
    }

    // Get posts and reels within date range
    const posts = await Post.find({
      user: userId,
      isArchived: false,
      createdAt: { $gte: startDate, $lte: endDate }
    }).select('views impressions createdAt');

    const reels = await Reel.find({
      user: userId,
      isArchived: false,
      createdAt: { $gte: startDate, $lte: endDate }
    }).select('views impressions createdAt');

    // Calculate daily reach and impressions
    const dailyMetrics = {};
    const dateFormat = date => date.toISOString().split('T')[0];

    // Initialize daily metrics for each day in the range
    for (let d = new Date(startDate); d <= endDate; d.setDate(d.getDate() + 1)) {
      const dateKey = dateFormat(d);
      dailyMetrics[dateKey] = {
        reach: 0,
        impressions: 0
      };
    }

    // Process posts (using views as reach and impressions as impressions)
    posts.forEach(post => {
      const dateKey = dateFormat(post.createdAt);
      if (dailyMetrics[dateKey]) {
        dailyMetrics[dateKey].reach += post.views || 0;
        dailyMetrics[dateKey].impressions += post.impressions || post.views || 0;
      }
    });

    // Process reels
    reels.forEach(reel => {
      const dateKey = dateFormat(reel.createdAt);
      if (dailyMetrics[dateKey]) {
        dailyMetrics[dateKey].reach += reel.views || 0;
        dailyMetrics[dateKey].impressions += reel.impressions || reel.views || 0;
      }
    });

    // Convert to array format
    const reachData = Object.entries(dailyMetrics).map(([date, metrics]) => ({
      date,
      ...metrics
    }));

    // Sort by date
    reachData.sort((a, b) => a.date.localeCompare(b.date));

    // Calculate overall metrics
    const totalReach = reachData.reduce((sum, day) => sum + day.reach, 0);
    const totalImpressions = reachData.reduce((sum, day) => sum + day.impressions, 0);

    res.status(200).json({
      success: true,
      data: {
        daily: reachData,
        summary: {
          totalReach,
          totalImpressions,
          avgImpressions: totalReach > 0 ? parseFloat((totalImpressions / totalReach).toFixed(2)) : 0
        }
      }
    });
  } catch (error) {
    console.error('Get reach and impressions error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Get sales analytics
// @route   GET /api/analytics/sales
// @access  Private
exports.getSalesAnalytics = async (req, res) => {
  try {
    const {
      startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 days ago
      endDate = new Date(),
      groupBy = 'day'
    } = req.query;

    const salesData = await Analytics.getSalesAnalytics(startDate, endDate, groupBy);

    // Get summary statistics
    const totalRevenue = salesData.reduce((sum, item) => sum + item.revenue, 0);
    const totalOrders = salesData.reduce((sum, item) => sum + item.orders, 0);
    const avgOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0;

    // Get comparison data (previous period)
    const periodLength = new Date(endDate) - new Date(startDate);
    const prevStartDate = new Date(new Date(startDate) - periodLength);
    const prevEndDate = new Date(startDate);

    const prevSalesData = await Analytics.getSalesAnalytics(prevStartDate, prevEndDate, groupBy);
    const prevTotalRevenue = prevSalesData.reduce((sum, item) => sum + item.revenue, 0);
    const prevTotalOrders = prevSalesData.reduce((sum, item) => sum + item.orders, 0);

    const revenueGrowth = prevTotalRevenue > 0 ?
      ((totalRevenue - prevTotalRevenue) / prevTotalRevenue) * 100 : 0;
    const orderGrowth = prevTotalOrders > 0 ?
      ((totalOrders - prevTotalOrders) / prevTotalOrders) * 100 : 0;

    res.status(200).json({
      success: true,
      data: {
        timeline: salesData,
        summary: {
          totalRevenue,
          totalOrders,
          avgOrderValue,
          revenueGrowth,
          orderGrowth
        },
        period: {
          startDate,
          endDate,
          groupBy
        }
      }
    });
  } catch (error) {
    console.error('Error getting sales analytics:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Track visitor activity
// @route   POST /api/analytics/track
// @access  Public
exports.trackVisitor = async (req, res) => {
  try {
    const {
      type,
      sessionId,
      page,
      event,
      productId,
      campaignId,
      metadata
    } = req.body;

    const trackingData = {
      type: type || 'visitor_tracking',
      date: new Date(),
      ...metadata
    };

    // Add user if authenticated
    if (req.user) {
      trackingData.user = req.user.id;
    }

    // Add specific data based on type
    if (type === 'visitor_tracking') {
      trackingData.visitorData = {
        sessionId,
        ipAddress: req.ip,
        userAgent: req.get('User-Agent'),
        referrer: req.get('Referer'),
        landingPage: page,
        ...metadata
      };
    } else if (type === 'product_performance' && productId) {
      trackingData.product = productId;
      trackingData.productData = {
        [event]: 1,
        ...metadata
      };
    } else if (type === 'campaign_performance' && campaignId) {
      trackingData.campaign = campaignId;
      trackingData.campaignData = {
        [event]: 1,
        ...metadata
      };
    }

    const analytics = new Analytics(trackingData);
    await analytics.save();

    res.status(200).json({
      success: true,
      message: 'Event tracked successfully'
    });
  } catch (error) {
    console.error('Error tracking visitor:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Get product performance analytics
// @route   GET /api/analytics/products
// @access  Private
exports.getProductAnalytics = async (req, res) => {
  try {
    const {
      startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
      endDate = new Date(),
      limit = 10,
      sortBy = 'revenue'
    } = req.query;

    // Get top performing products
    const topProducts = await Analytics.aggregate([
      {
        $match: {
          type: 'product_performance',
          date: {
            $gte: new Date(startDate),
            $lte: new Date(endDate)
          }
        }
      },
      {
        $group: {
          _id: '$product',
          totalViews: { $sum: '$productData.views' },
          totalClicks: { $sum: '$productData.clicks' },
          totalAddToCart: { $sum: '$productData.addToCart' },
          totalPurchases: { $sum: '$productData.purchases' },
          totalRevenue: { $sum: '$productData.revenue' },
          avgConversionRate: { $avg: '$productData.conversionRate' }
        }
      },
      {
        $lookup: {
          from: 'products',
          localField: '_id',
          foreignField: '_id',
          as: 'productInfo'
        }
      },
      {
        $unwind: '$productInfo'
      },
      {
        $project: {
          product: {
            _id: '$productInfo._id',
            name: '$productInfo.name',
            images: '$productInfo.images',
            price: '$productInfo.price'
          },
          metrics: {
            views: '$totalViews',
            clicks: '$totalClicks',
            addToCart: '$totalAddToCart',
            purchases: '$totalPurchases',
            revenue: '$totalRevenue',
            conversionRate: '$avgConversionRate',
            ctr: { $divide: ['$totalClicks', '$totalViews'] },
            cartConversion: { $divide: ['$totalPurchases', '$totalAddToCart'] }
          }
        }
      },
      {
        $sort: { [`metrics.${sortBy}`]: -1 }
      },
      {
        $limit: parseInt(limit)
      }
    ]);

    res.status(200).json({
      success: true,
      data: topProducts
    });
  } catch (error) {
    console.error('Error getting product analytics:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Get visitor analytics
// @route   GET /api/analytics/visitors
// @access  Private
exports.getVisitorAnalytics = async (req, res) => {
  try {
    const {
      startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
      endDate = new Date()
    } = req.query;

    const visitorData = await Analytics.getVisitorAnalytics(startDate, endDate);

    // Get real-time visitor count (last 30 minutes)
    const realTimeVisitors = await Analytics.countDocuments({
      type: 'visitor_tracking',
      date: {
        $gte: new Date(Date.now() - 30 * 60 * 1000) // 30 minutes ago
      }
    });

    // Get top pages
    const topPages = await Analytics.aggregate([
      {
        $match: {
          type: 'visitor_tracking',
          date: {
            $gte: new Date(startDate),
            $lte: new Date(endDate)
          }
        }
      },
      {
        $group: {
          _id: '$visitorData.landingPage',
          visits: { $sum: 1 },
          uniqueVisitors: { $addToSet: '$visitorData.sessionId' },
          avgSessionDuration: { $avg: '$visitorData.sessionDuration' },
          bounceRate: { $avg: '$visitorData.bounceRate' }
        }
      },
      {
        $project: {
          page: '$_id',
          visits: 1,
          uniqueVisitors: { $size: '$uniqueVisitors' },
          avgSessionDuration: 1,
          bounceRate: 1
        }
      },
      {
        $sort: { visits: -1 }
      },
      {
        $limit: 10
      }
    ]);

    res.status(200).json({
      success: true,
      data: {
        summary: visitorData[0] || {
          totalVisitors: 0,
          totalPageViews: 0,
          avgSessionDuration: 0,
          avgBounceRate: 0
        },
        realTimeVisitors,
        topPages
      }
    });
  } catch (error) {
    console.error('Error getting visitor analytics:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Get cart abandonment analytics
// @route   GET /api/analytics/cart-abandonment
// @access  Private
exports.getCartAbandonmentAnalytics = async (req, res) => {
  try {
    const {
      startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
      endDate = new Date()
    } = req.query;

    const abandonmentData = await Analytics.getCartAbandonmentData(startDate, endDate);

    // Get abandonment timeline
    const timeline = await Analytics.aggregate([
      {
        $match: {
          type: 'cart_abandonment',
          date: {
            $gte: new Date(startDate),
            $lte: new Date(endDate)
          }
        }
      },
      {
        $group: {
          _id: {
            year: { $year: '$date' },
            month: { $month: '$date' },
            day: { $dayOfMonth: '$date' }
          },
          abandoned: { $sum: 1 },
          totalValue: { $sum: '$cartData.cartValue' },
          recovered: { $sum: { $cond: ['$cartData.recovered', 1, 0] } },
          recoveredValue: { $sum: '$cartData.recoveryRevenue' }
        }
      },
      {
        $sort: { '_id': 1 }
      }
    ]);

    // Get current abandoned carts (not recovered)
    const currentAbandoned = await Analytics.find({
      type: 'cart_abandonment',
      'cartData.recovered': false,
      date: {
        $gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) // Last 7 days
      }
    })
    .populate('user', 'name email')
    .sort({ date: -1 })
    .limit(20);

    res.status(200).json({
      success: true,
      data: {
        summary: abandonmentData[0] || {
          totalAbandoned: 0,
          totalAbandonedValue: 0,
          recovered: 0,
          recoveredRevenue: 0,
          recoveryRate: 0
        },
        timeline,
        currentAbandoned
      }
    });
  } catch (error) {
    console.error('Error getting cart abandonment analytics:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Get heatmap data
// @route   GET /api/analytics/heatmap
// @access  Private
exports.getHeatmapData = async (req, res) => {
  try {
    const {
      page,
      startDate = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
      endDate = new Date()
    } = req.query;

    if (!page) {
      return res.status(400).json({
        success: false,
        message: 'Page parameter is required'
      });
    }

    const heatmapData = await Analytics.find({
      type: 'visitor_tracking',
      'heatmapData.page': page,
      date: {
        $gte: new Date(startDate),
        $lte: new Date(endDate)
      }
    })
    .select('heatmapData date')
    .lean();

    // Process heatmap data for visualization
    const processedData = heatmapData.map(item => ({
      x: item.heatmapData.clickX,
      y: item.heatmapData.clickY,
      element: item.heatmapData.elementId,
      timestamp: item.date
    }));

    res.status(200).json({
      success: true,
      data: {
        page,
        clicks: processedData,
        totalClicks: processedData.length
      }
    });
  } catch (error) {
    console.error('Error getting heatmap data:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

module.exports = {
  getAccountOverview: exports.getAccountOverview,
  getContentPerformance: exports.getContentPerformance,
  getAudienceInsights: exports.getAudienceInsights,
  getEngagementMetrics: exports.getEngagementMetrics,
  getReachAndImpressions: exports.getReachAndImpressions,
  getSalesAnalytics: exports.getSalesAnalytics,
  trackVisitor: exports.trackVisitor,
  getProductAnalytics: exports.getProductAnalytics,
  getVisitorAnalytics: exports.getVisitorAnalytics,
  getCartAbandonmentAnalytics: exports.getCartAbandonmentAnalytics,
  getHeatmapData: exports.getHeatmapData
};