const express = require('express');
const router = express.Router();
const { protect, optionalAuth } = require('../middleware/auth');
const {
  createVendorReview,
  getVendorReviews,
  getUserReviews,
  updateVendorReview,
  deleteVendorReview,
  markReviewHelpful,
  reportReview
} = require('../controllers/vendorReviewController');

// Public routes
router.get('/vendor/:vendorId', getVendorReviews);

// Protected routes
router.use(protect);

// Review CRUD operations
router.post('/', createVendorReview);
router.get('/my-reviews', getUserReviews);
router.put('/:id', updateVendorReview);
router.delete('/:id', deleteVendorReview);

// Review interactions
router.post('/:id/helpful', markReviewHelpful);
router.post('/:id/report', reportReview);

module.exports = router;
