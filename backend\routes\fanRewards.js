const express = require('express');
const router = express.Router({ mergeParams: true });
const { protect, optionalAuth } = require('../middleware/auth');
const {
  getUserRewards,
  getLeaderboard,
  awardPoints,
  awardBadge,
  redeemReward,
} = require('../controllers/fanRewardController');

// Public routes
router.get('/leaderboard', optionalAuth, getLeaderboard);

// Protected routes
router.use(protect);

router.get('/', getUserRewards);
router.post('/points', awardPoints);
router.post('/badges', awardBadge);
router.post('/redeem', redeemReward);

module.exports = router;
