const Notification = require('../models/Notification');
const User = require('../models/User');
const { createError } = require('../utils/error');
const socketEmitter = require('../utils/socketEmitter');

/**
 * Create a new notification
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.createNotification = async (req, res, next) => {
  try {
    const { recipient, type, sender, post, reel, comment, text } = req.body;

    // Don't create notification if sender is the recipient
    if (sender && sender.toString() === recipient.toString()) {
      return res.status(200).json({
        success: true,
        message: 'Notification not created (self-action)',
      });
    }

    // Create notification
    const notification = await Notification.create({
      recipient,
      type,
      sender: sender || req.user.id,
      post,
      reel,
      comment,
      text,
    });

    // Populate sender details
    await notification.populate('sender', 'username name profilePicture');

    // Emit notification via Socket.IO
    socketEmitter.emitNotification(recipient, notification);

    res.status(201).json({
      success: true,
      data: notification,
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Get all notifications for the authenticated user
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.getNotifications = async (req, res, next) => {
  try {
    const notifications = await Notification.find({ recipient: req.user.id })
      .sort({ createdAt: -1 })
      .populate('sender', 'username name profilePicture')
      .populate('post', 'media')
      .populate('reel', 'video thumbnail')
      .limit(50);

    res.status(200).json({
      success: true,
      count: notifications.length,
      data: notifications,
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Get unread notifications count for the authenticated user
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.getUnreadCount = async (req, res, next) => {
  try {
    const count = await Notification.countDocuments({
      recipient: req.user.id,
      read: false,
    });

    res.status(200).json({
      success: true,
      count,
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Mark a notification as read
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.markAsRead = async (req, res, next) => {
  try {
    const notification = await Notification.findById(req.params.id);

    if (!notification) {
      return next(createError(404, 'Notification not found'));
    }

    // Check if the notification belongs to the authenticated user
    if (notification.recipient.toString() !== req.user.id) {
      return next(createError(403, 'Not authorized to access this notification'));
    }

    // Update notification
    notification.read = true;
    await notification.save();

    // Emit notification read event
    socketEmitter.emitNotificationRead(req.user.id, notification._id);

    res.status(200).json({
      success: true,
      data: notification,
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Mark all notifications as read
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.markAllAsRead = async (req, res, next) => {
  try {
    await Notification.updateMany(
      { recipient: req.user.id, read: false },
      { read: true }
    );

    // Emit all notifications read event
    socketEmitter.emitAllNotificationsRead(req.user.id);

    res.status(200).json({
      success: true,
      message: 'All notifications marked as read',
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Delete a notification
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.deleteNotification = async (req, res, next) => {
  try {
    const notification = await Notification.findById(req.params.id);

    if (!notification) {
      return next(createError(404, 'Notification not found'));
    }

    // Check if the notification belongs to the authenticated user
    if (notification.recipient.toString() !== req.user.id) {
      return next(createError(403, 'Not authorized to delete this notification'));
    }

    await notification.remove();

    res.status(200).json({
      success: true,
      message: 'Notification deleted',
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Delete all notifications for the authenticated user
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.deleteAllNotifications = async (req, res, next) => {
  try {
    await Notification.deleteMany({ recipient: req.user.id });

    res.status(200).json({
      success: true,
      message: 'All notifications deleted',
    });
  } catch (err) {
    next(err);
  }
};
