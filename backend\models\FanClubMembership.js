const mongoose = require('mongoose');

const FanClubMembershipSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.ObjectId,
    ref: 'User',
    required: true
  },
  fanClub: {
    type: mongoose.Schema.ObjectId,
    ref: 'FanClub',
    required: true
  },
  tier: {
    type: mongoose.Schema.Types.ObjectId,
    required: true
  },
  status: {
    type: String,
    enum: ['active', 'cancelled', 'expired'],
    default: 'active'
  },
  joinedAt: {
    type: Date,
    default: Date.now
  },
  renewalDate: {
    type: Date,
    required: true
  },
  cancelledAt: {
    type: Date
  }
});

// Create compound index for user and fanClub to ensure a user can only have one membership per fan club
FanClubMembershipSchema.index({ user: 1, fanClub: 1 }, { unique: true });
FanClubMembershipSchema.index({ fanClub: 1, status: 1 });
FanClubMembershipSchema.index({ renewalDate: 1 });

module.exports = mongoose.model('FanClubMembership', FanClubMembershipSchema);
