const mongoose = require('mongoose');

const OrderItemSchema = new mongoose.Schema({
  product: {
    type: mongoose.Schema.ObjectId,
    ref: 'Product',
    required: true
  },
  quantity: {
    type: Number,
    required: true,
    min: [1, 'Quantity must be at least 1']
  },
  price: {
    type: Number,
    required: true
  },
  total: {
    type: Number,
    required: true
  }
});

const OrderSchema = new mongoose.Schema({
  orderNumber: {
    type: String,
    required: true,
    unique: true
  },
  user: {
    type: mongoose.Schema.ObjectId,
    ref: 'User',
    required: true
  },
  items: [OrderItemSchema],
  shippingAddress: {
    fullName: String,
    addressLine1: String,
    addressLine2: String,
    city: String,
    state: String,
    postalCode: String,
    country: String,
    phone: String
  },
  billingAddress: {
    fullName: String,
    addressLine1: String,
    addressLine2: String,
    city: String,
    state: String,
    postalCode: String,
    country: String
  },
  paymentMethod: {
    type: {
      type: String,
      enum: ['credit_card', 'debit_card', 'paypal', 'apple_pay', 'google_pay', 'bank_transfer'],
      required: true
    },
    last4: String,
    brand: String,
    expiryMonth: Number,
    expiryYear: Number
  },
  subtotal: {
    type: Number,
    required: true
  },
  discount: {
    type: Number,
    default: 0
  },
  tax: {
    type: Number,
    required: true
  },
  shipping: {
    type: Number,
    required: true
  },
  total: {
    type: Number,
    required: true
  },
  promoCode: {
    code: String,
    discount: Number,
    type: {
      type: String,
      enum: ['percentage', 'fixed']
    }
  },
  status: {
    type: String,
    enum: ['pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled', 'refunded'],
    default: 'pending'
  },
  paymentStatus: {
    type: String,
    enum: ['pending', 'paid', 'failed', 'refunded'],
    default: 'pending'
  },
  shippingMethod: {
    type: String,
    enum: ['standard', 'express', 'overnight', 'pickup'],
    default: 'standard'
  },
  trackingNumber: String,
  estimatedDelivery: Date,
  deliveredAt: Date,
  notes: String,
  vendorNotes: String,
  vendorId: {
    type: mongoose.Schema.ObjectId,
    ref: 'Vendor'
  },
  vendor: {
    type: mongoose.Schema.ObjectId,
    ref: 'User'
  },
  transactionId: String,
  paymentIntentId: String,
  refundId: String,
  buyerProtection: {
    isActive: {
      type: Boolean,
      default: false
    },
    protectionId: {
      type: mongoose.Schema.ObjectId,
      ref: 'BuyerProtection'
    }
  },
  disputes: [{
    type: mongoose.Schema.ObjectId,
    ref: 'Dispute'
  }],
  reviews: [{
    type: mongoose.Schema.ObjectId,
    ref: 'ProductReview'
  }],
  compliance: {
    gdprProcessed: {
      type: Boolean,
      default: false
    },
    kycVerified: {
      type: Boolean,
      default: false
    },
    amlChecked: {
      type: Boolean,
      default: false
    }
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Generate order number before saving
OrderSchema.pre('save', function(next) {
  if (this.isNew) {
    const timestamp = Date.now().toString();
    const random = Math.random().toString(36).substring(2, 8).toUpperCase();
    this.orderNumber = `ORD-${timestamp.slice(-6)}-${random}`;
  }
  this.updatedAt = Date.now();
  next();
});

// Calculate totals before saving
OrderSchema.pre('save', function(next) {
  if (this.items && this.items.length > 0) {
    let subtotal = 0;
    this.items.forEach(item => {
      item.total = item.price * item.quantity;
      subtotal += item.total;
    });
    this.subtotal = subtotal;

    // Apply discount
    let discount = 0;
    if (this.promoCode) {
      if (this.promoCode.type === 'percentage') {
        discount = (subtotal * this.promoCode.discount) / 100;
      } else {
        discount = this.promoCode.discount;
      }
    }
    this.discount = discount;

    // Calculate total
    this.total = subtotal - discount + this.tax + this.shipping;
  }
  next();
});

// Instance method to update status
OrderSchema.methods.updateStatus = function(newStatus) {
  this.status = newStatus;
  if (newStatus === 'delivered') {
    this.deliveredAt = new Date();
  }
  return this.save();
};

// Instance method to add tracking number
OrderSchema.methods.addTracking = function(trackingNumber, estimatedDelivery) {
  this.trackingNumber = trackingNumber;
  this.estimatedDelivery = estimatedDelivery;
  this.status = 'shipped';
  return this.save();
};

// Instance method to get order summary
OrderSchema.methods.getSummary = function() {
  return {
    orderNumber: this.orderNumber,
    itemCount: this.items.reduce((total, item) => total + item.quantity, 0),
    subtotal: this.subtotal,
    discount: this.discount,
    tax: this.tax,
    shipping: this.shipping,
    total: this.total,
    status: this.status,
    paymentStatus: this.paymentStatus
  };
};

// Static method to find orders by user
OrderSchema.statics.findByUser = function(userId, options = {}) {
  const { page = 1, limit = 10, status } = options;

  let query = { user: userId };
  if (status) {
    query.status = status;
  }

  return this.find(query)
    .populate('items.product', 'name images price')
    .sort({ createdAt: -1 })
    .limit(limit * 1)
    .skip((page - 1) * limit);
};

// Create indexes for better performance
OrderSchema.index({ user: 1, createdAt: -1 });
OrderSchema.index({ orderNumber: 1 });
OrderSchema.index({ status: 1 });
OrderSchema.index({ paymentStatus: 1 });
OrderSchema.index({ trackingNumber: 1 });
OrderSchema.index({ vendorId: 1, createdAt: -1 });
OrderSchema.index({ 'items.product': 1 });

module.exports = mongoose.model('Order', OrderSchema);
