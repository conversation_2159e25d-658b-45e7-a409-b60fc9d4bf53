const fs = require('fs');
const path = require('path');

console.log('🔍 Validating Frontend Authentication Structure...\n');

// Check if required files exist
const requiredFiles = [
  'src/context/AuthContext.jsx',
  'src/pages/AuthPage.jsx',
  'src/pages/Register.jsx',
  'src/utils/fixedAxios.js',
  'public/dynamic-config.json'
];

console.log('📁 Checking required frontend files...');
let allFilesExist = true;

requiredFiles.forEach(file => {
  const filePath = path.join(__dirname, file);
  if (fs.existsSync(filePath)) {
    console.log(`✅ ${file} - exists`);
  } else {
    console.log(`❌ ${file} - missing`);
    allFilesExist = false;
  }
});

console.log('\n🔧 Checking dynamic configuration...');

try {
  const configPath = path.join(__dirname, 'public/dynamic-config.json');
  const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
  
  if (config.apiBaseUrl && config.socketUrl) {
    console.log(`✅ API Base URL: ${config.apiBaseUrl}`);
    console.log(`✅ Socket URL: ${config.socketUrl}`);
    
    // Check if URLs are valid
    try {
      new URL(config.apiBaseUrl);
      new URL(config.socketUrl);
      console.log('✅ URLs are valid');
    } catch (urlError) {
      console.log('❌ Invalid URLs in configuration');
      allFilesExist = false;
    }
  } else {
    console.log('❌ Missing required configuration fields');
    allFilesExist = false;
  }
} catch (error) {
  console.log(`❌ Error reading dynamic config: ${error.message}`);
  allFilesExist = false;
}

console.log('\n📋 Checking AuthContext structure...');

try {
  const authContextPath = path.join(__dirname, 'src/context/AuthContext.jsx');
  const authContextContent = fs.readFileSync(authContextPath, 'utf8');
  
  const requiredFunctions = [
    'register',
    'login',
    'logout',
    'updateUser',
    'clearError'
  ];
  
  requiredFunctions.forEach(func => {
    if (authContextContent.includes(`const ${func} =`) || authContextContent.includes(`${func}:`)) {
      console.log(`✅ ${func} - function found`);
    } else {
      console.log(`❌ ${func} - function missing`);
      allFilesExist = false;
    }
  });
  
  // Check for proper error handling
  if (authContextContent.includes('err.response?.data?.message') || authContextContent.includes('err.response.data?.message')) {
    console.log('✅ Error handling - properly implemented');
  } else {
    console.log('⚠️  Error handling - may need improvement');
  }
  
} catch (error) {
  console.log(`❌ Error reading AuthContext: ${error.message}`);
  allFilesExist = false;
}

console.log('\n🌐 Checking fixedAxios configuration...');

try {
  const axiosPath = path.join(__dirname, 'src/utils/fixedAxios.js');
  const axiosContent = fs.readFileSync(axiosPath, 'utf8');
  
  const requiredFeatures = [
    'getDynamicConfig',
    'interceptors.request',
    'interceptors.response',
    'Authorization',
    'Bearer'
  ];
  
  requiredFeatures.forEach(feature => {
    if (axiosContent.includes(feature)) {
      console.log(`✅ ${feature} - implemented`);
    } else {
      console.log(`❌ ${feature} - missing`);
      allFilesExist = false;
    }
  });
  
  // Check for URL construction fixes
  if (axiosContent.includes('baseURL.endsWith') && axiosContent.includes('url.startsWith')) {
    console.log('✅ URL construction fixes - implemented');
  } else {
    console.log('⚠️  URL construction fixes - may need improvement');
  }
  
} catch (error) {
  console.log(`❌ Error reading fixedAxios: ${error.message}`);
  allFilesExist = false;
}

console.log('\n📝 Checking AuthPage form handling...');

try {
  const authPagePath = path.join(__dirname, 'src/pages/AuthPage.jsx');
  const authPageContent = fs.readFileSync(authPagePath, 'utf8');
  
  // Check for proper field mapping
  if (authPageContent.includes('fullName:') && authPageContent.includes('name:')) {
    console.log('✅ Field mapping - both name and fullName supported');
  } else {
    console.log('⚠️  Field mapping - may need improvement');
  }
  
  // Check for form validation
  if (authPageContent.includes('handleLogin') && authPageContent.includes('handleRegister')) {
    console.log('✅ Form handlers - implemented');
  } else {
    console.log('❌ Form handlers - missing');
    allFilesExist = false;
  }
  
} catch (error) {
  console.log(`❌ Error reading AuthPage: ${error.message}`);
  allFilesExist = false;
}

console.log('\n' + '='.repeat(50));

if (allFilesExist) {
  console.log('🎉 Frontend authentication structure validation PASSED!');
  console.log('✅ All required files and configurations are present.');
  console.log('\n📝 Frontend is ready for authentication testing!');
} else {
  console.log('❌ Frontend authentication structure validation FAILED!');
  console.log('⚠️  Please fix the issues above before proceeding.');
}

console.log('='.repeat(50));
