const express = require('express');
const router = express.Router();

const {
  blockUser,
  unblockUser,
  getBlockedUsers,
  reportUser,
  reportContent,
  getReports,
  updateReport
} = require('../controllers/userSafetyController');

const { protect } = require('../middleware/auth');

// Block/unblock routes
router.route('/block').post(protect, blockUser);
router.route('/block/:targetUserId').delete(protect, unblockUser);
router.route('/blocks').get(protect, getBlockedUsers);

// Report routes
router.route('/report/user').post(protect, reportUser);
router.route('/report/content').post(protect, reportContent);
router.route('/reports').get(protect, getReports);
router.route('/reports/:id').put(protect, updateReport);

module.exports = router;
