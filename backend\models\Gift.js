const mongoose = require('mongoose');

const GiftSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Please add a name'],
    trim: true,
    maxlength: [50, 'Name cannot be more than 50 characters']
  },
  description: {
    type: String,
    maxlength: [500, 'Description cannot be more than 500 characters']
  },
  price: {
    type: Number,
    required: [true, 'Please add a price'],
    min: [1, 'Price must be at least 1 coin']
  },
  imageUrl: {
    type: String,
    required: [true, 'Please add an image URL']
  },
  animationUrl: {
    type: String
  },
  category: {
    type: String,
    enum: ['standard', 'premium', 'exclusive', 'seasonal', 'limited'],
    default: 'standard'
  },
  isActive: {
    type: Boolean,
    default: true
  },
  createdAt: {
    type: Date,
    default: Date.now
  }
});

module.exports = mongoose.model('Gift', GiftSchema);
