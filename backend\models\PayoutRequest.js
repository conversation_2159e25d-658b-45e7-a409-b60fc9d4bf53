const mongoose = require('mongoose');

const PayoutRequestSchema = new mongoose.Schema({
  requestId: {
    type: String,
    unique: true,
    required: true
  },
  vendor: {
    type: mongoose.Schema.ObjectId,
    ref: 'Vendor',
    required: true
  },
  user: {
    type: mongoose.Schema.ObjectId,
    ref: 'User',
    required: true
  },
  type: {
    type: String,
    enum: ['manual', 'automatic', 'scheduled'],
    default: 'manual'
  },
  status: {
    type: String,
    enum: ['pending', 'approved', 'processing', 'completed', 'failed', 'cancelled', 'rejected'],
    default: 'pending'
  },
  amounts: {
    requestedAmount: {
      type: Number,
      required: true,
      min: 0
    },
    availableBalance: {
      type: Number,
      required: true,
      min: 0
    },
    processingFee: {
      type: Number,
      default: 0,
      min: 0
    },
    taxes: {
      type: Number,
      default: 0,
      min: 0
    },
    netAmount: {
      type: Number,
      required: true,
      min: 0
    }
  },
  currency: {
    type: String,
    required: true,
    default: 'USD',
    uppercase: true
  },
  payoutMethod: {
    type: {
      type: String,
      enum: ['bank_transfer', 'paypal', 'mobile_money', 'crypto', 'check', 'wire_transfer'],
      required: true
    },
    details: {
      // Bank Transfer
      bankName: String,
      accountNumber: String,
      routingNumber: String,
      swiftCode: String,
      iban: String,
      
      // PayPal
      paypalEmail: String,
      
      // Mobile Money
      mobileProvider: String,
      mobileNumber: String,
      
      // Crypto
      walletAddress: String,
      cryptoCurrency: String,
      network: String,
      
      // Check
      mailingAddress: {
        street: String,
        city: String,
        state: String,
        postalCode: String,
        country: String
      },
      
      // Common fields
      accountHolderName: String,
      country: String,
      verified: {
        type: Boolean,
        default: false
      }
    }
  },
  commissions: [{
    type: mongoose.Schema.ObjectId,
    ref: 'Commission'
  }],
  schedule: {
    frequency: {
      type: String,
      enum: ['daily', 'weekly', 'bi_weekly', 'monthly', 'quarterly'],
      default: 'weekly'
    },
    dayOfWeek: Number, // 0-6 for weekly
    dayOfMonth: Number, // 1-31 for monthly
    nextPayoutDate: Date,
    isActive: {
      type: Boolean,
      default: false
    }
  },
  minimumPayout: {
    amount: {
      type: Number,
      default: 50
    },
    currency: {
      type: String,
      default: 'USD'
    }
  },
  processing: {
    processedAt: Date,
    processedBy: {
      type: mongoose.Schema.ObjectId,
      ref: 'User'
    },
    externalTransactionId: String,
    providerResponse: mongoose.Schema.Types.Mixed,
    estimatedArrival: Date,
    actualArrival: Date
  },
  verification: {
    kycStatus: {
      type: String,
      enum: ['not_required', 'pending', 'verified', 'rejected'],
      default: 'not_required'
    },
    taxFormStatus: {
      type: String,
      enum: ['not_required', 'pending', 'submitted', 'approved'],
      default: 'not_required'
    },
    identityVerified: {
      type: Boolean,
      default: false
    },
    bankAccountVerified: {
      type: Boolean,
      default: false
    }
  },
  compliance: {
    amlChecked: {
      type: Boolean,
      default: false
    },
    sanctionsChecked: {
      type: Boolean,
      default: false
    },
    fraudChecked: {
      type: Boolean,
      default: false
    },
    riskScore: {
      type: Number,
      min: 0,
      max: 100
    },
    complianceNotes: String
  },
  timeline: [{
    status: String,
    timestamp: {
      type: Date,
      default: Date.now
    },
    note: String,
    performedBy: {
      type: mongoose.Schema.ObjectId,
      ref: 'User'
    },
    metadata: mongoose.Schema.Types.Mixed
  }],
  notifications: {
    emailSent: {
      type: Boolean,
      default: false
    },
    smsSent: {
      type: Boolean,
      default: false
    },
    pushNotificationSent: {
      type: Boolean,
      default: false
    }
  },
  retryAttempts: {
    count: {
      type: Number,
      default: 0
    },
    maxAttempts: {
      type: Number,
      default: 3
    },
    lastAttempt: Date,
    nextRetry: Date
  },
  failure: {
    reason: String,
    code: String,
    details: String,
    isRetryable: {
      type: Boolean,
      default: true
    }
  },
  metadata: {
    ipAddress: String,
    userAgent: String,
    requestSource: String,
    campaignId: String,
    notes: String,
    customFields: mongoose.Schema.Types.Mixed
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Generate unique request ID
PayoutRequestSchema.pre('save', function(next) {
  if (this.isNew && !this.requestId) {
    this.requestId = `PAYOUT-${Date.now()}-${Math.random().toString(36).substr(2, 8).toUpperCase()}`;
  }
  this.updatedAt = Date.now();
  next();
});

// Calculate net amount after fees and taxes
PayoutRequestSchema.methods.calculateNetAmount = function() {
  const netAmount = this.amounts.requestedAmount - this.amounts.processingFee - this.amounts.taxes;
  this.amounts.netAmount = Math.max(0, netAmount);
  return this.amounts.netAmount;
};

// Add timeline entry
PayoutRequestSchema.methods.addTimelineEntry = function(status, note, performedBy, metadata = {}) {
  this.timeline.push({
    status,
    note,
    performedBy,
    metadata,
    timestamp: new Date()
  });
  return this.save();
};

// Update status with timeline
PayoutRequestSchema.methods.updateStatus = function(status, note, performedBy, metadata = {}) {
  this.status = status;
  return this.addTimelineEntry(status, note, performedBy, metadata);
};

// Mark as processing
PayoutRequestSchema.methods.startProcessing = function(processedBy, externalTransactionId) {
  this.status = 'processing';
  this.processing.processedAt = new Date();
  this.processing.processedBy = processedBy;
  this.processing.externalTransactionId = externalTransactionId;
  
  return this.addTimelineEntry(
    'processing_started',
    'Payout processing initiated',
    processedBy,
    { externalTransactionId }
  );
};

// Mark as completed
PayoutRequestSchema.methods.markCompleted = function(actualArrival, providerResponse) {
  this.status = 'completed';
  this.processing.actualArrival = actualArrival || new Date();
  this.processing.providerResponse = providerResponse;
  
  return this.addTimelineEntry(
    'completed',
    'Payout completed successfully',
    null,
    { actualArrival: this.processing.actualArrival }
  );
};

// Mark as failed
PayoutRequestSchema.methods.markFailed = function(reason, code, details, isRetryable = true) {
  this.status = 'failed';
  this.failure = {
    reason,
    code,
    details,
    isRetryable
  };
  
  if (isRetryable && this.retryAttempts.count < this.retryAttempts.maxAttempts) {
    this.retryAttempts.count += 1;
    this.retryAttempts.lastAttempt = new Date();
    this.retryAttempts.nextRetry = new Date(Date.now() + (this.retryAttempts.count * 60 * 60 * 1000)); // Exponential backoff
  }
  
  return this.addTimelineEntry(
    'failed',
    `Payout failed: ${reason}`,
    null,
    { reason, code, details, isRetryable }
  );
};

// Check if eligible for payout
PayoutRequestSchema.methods.isEligibleForPayout = function() {
  const checks = {
    minimumAmount: this.amounts.requestedAmount >= this.minimumPayout.amount,
    kycVerified: this.verification.kycStatus === 'verified' || this.verification.kycStatus === 'not_required',
    identityVerified: this.verification.identityVerified,
    paymentMethodVerified: this.payoutMethod.details.verified,
    complianceCleared: this.compliance.amlChecked && this.compliance.sanctionsChecked,
    notBlocked: !['rejected', 'cancelled', 'failed'].includes(this.status)
  };
  
  return Object.values(checks).every(check => check === true);
};

// Schedule next automatic payout
PayoutRequestSchema.methods.scheduleNextPayout = function() {
  if (!this.schedule.isActive) return null;
  
  const now = new Date();
  let nextDate = new Date(now);
  
  switch (this.schedule.frequency) {
    case 'daily':
      nextDate.setDate(now.getDate() + 1);
      break;
    case 'weekly':
      nextDate.setDate(now.getDate() + (7 - now.getDay() + this.schedule.dayOfWeek) % 7);
      if (nextDate <= now) nextDate.setDate(nextDate.getDate() + 7);
      break;
    case 'bi_weekly':
      nextDate.setDate(now.getDate() + 14);
      break;
    case 'monthly':
      nextDate.setMonth(now.getMonth() + 1);
      nextDate.setDate(this.schedule.dayOfMonth || 1);
      break;
    case 'quarterly':
      nextDate.setMonth(now.getMonth() + 3);
      nextDate.setDate(this.schedule.dayOfMonth || 1);
      break;
  }
  
  this.schedule.nextPayoutDate = nextDate;
  return nextDate;
};

// Create indexes for better performance
PayoutRequestSchema.index({ requestId: 1 });
PayoutRequestSchema.index({ vendor: 1, status: 1 });
PayoutRequestSchema.index({ user: 1, createdAt: -1 });
PayoutRequestSchema.index({ status: 1, createdAt: -1 });
PayoutRequestSchema.index({ 'schedule.nextPayoutDate': 1, 'schedule.isActive': 1 });
PayoutRequestSchema.index({ 'amounts.requestedAmount': -1 });
PayoutRequestSchema.index({ currency: 1 });
PayoutRequestSchema.index({ 'payoutMethod.type': 1 });
PayoutRequestSchema.index({ createdAt: -1 });

module.exports = mongoose.model('PayoutRequest', PayoutRequestSchema);
