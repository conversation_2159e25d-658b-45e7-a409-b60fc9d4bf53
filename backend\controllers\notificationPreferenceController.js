const asyncHandler = require('../middleware/async');
const ErrorResponse = require('../utils/errorResponse');
const NotificationPreference = require('../models/NotificationPreference');

/**
 * @desc    Get notification preferences for the current user
 * @route   GET /api/users/me/notification-preferences
 * @access  Private
 */
exports.getNotificationPreferences = asyncHandler(async (req, res, next) => {
  let preferences = await NotificationPreference.findOne({ user: req.user.id });

  // If no preferences exist, create default preferences
  if (!preferences) {
    preferences = await NotificationPreference.create({
      user: req.user.id
    });
  }

  res.status(200).json({
    success: true,
    data: preferences
  });
});

/**
 * @desc    Update notification preferences
 * @route   PUT /api/users/me/notification-preferences
 * @access  Private
 */
exports.updateNotificationPreferences = asyncHandler(async (req, res, next) => {
  let preferences = await NotificationPreference.findOne({ user: req.user.id });

  // If no preferences exist, create new preferences
  if (!preferences) {
    preferences = await NotificationPreference.create({
      user: req.user.id,
      ...req.body
    });
  } else {
    // Update existing preferences
    preferences = await NotificationPreference.findOneAndUpdate(
      { user: req.user.id },
      req.body,
      {
        new: true,
        runValidators: true
      }
    );
  }

  res.status(200).json({
    success: true,
    data: preferences
  });
});

/**
 * @desc    Update a specific notification preference category
 * @route   PATCH /api/users/me/notification-preferences/:category
 * @access  Private
 */
exports.updatePreferenceCategory = asyncHandler(async (req, res, next) => {
  const validCategories = ['liveStream', 'content', 'social', 'channels', 'quietHours', 'locationBasedAlerts'];
  
  if (!validCategories.includes(req.params.category)) {
    return next(new ErrorResponse(`Invalid preference category: ${req.params.category}`, 400));
  }

  let preferences = await NotificationPreference.findOne({ user: req.user.id });

  // If no preferences exist, create new preferences
  if (!preferences) {
    const newPrefs = { user: req.user.id };
    newPrefs[req.params.category] = req.body;
    
    preferences = await NotificationPreference.create(newPrefs);
  } else {
    // Create update object with the category as the key
    const updateObj = {};
    updateObj[req.params.category] = req.body;

    // Update existing preferences
    preferences = await NotificationPreference.findOneAndUpdate(
      { user: req.user.id },
      { $set: updateObj },
      {
        new: true,
        runValidators: true
      }
    );
  }

  res.status(200).json({
    success: true,
    data: preferences
  });
});

/**
 * @desc    Toggle a specific notification preference
 * @route   PATCH /api/users/me/notification-preferences/toggle/:path
 * @access  Private
 */
exports.togglePreference = asyncHandler(async (req, res, next) => {
  const path = req.params.path;
  
  // Validate path format (e.g., "liveStream.followedCreatorStart")
  if (!path || !path.includes('.')) {
    return next(new ErrorResponse(`Invalid preference path: ${path}`, 400));
  }

  let preferences = await NotificationPreference.findOne({ user: req.user.id });

  // If no preferences exist, create default preferences
  if (!preferences) {
    preferences = await NotificationPreference.create({
      user: req.user.id
    });
  }

  // Get current value of the preference
  const [category, setting] = path.split('.');
  
  if (!preferences[category] || typeof preferences[category][setting] === 'undefined') {
    return next(new ErrorResponse(`Invalid preference path: ${path}`, 400));
  }

  const currentValue = preferences[category][setting];
  
  // Create update object to toggle the value
  const updateObj = {};
  updateObj[`${category}.${setting}`] = !currentValue;

  // Update the preference
  preferences = await NotificationPreference.findOneAndUpdate(
    { user: req.user.id },
    { $set: updateObj },
    {
      new: true,
      runValidators: true
    }
  );

  res.status(200).json({
    success: true,
    data: preferences
  });
});
