const express = require('express');
const router = express.Router();
const multer = require('multer');
const { protect, authorize } = require('../middleware/auth');
const {
  createBuyerProtection,
  activateClaim,
  createDispute,
  addDisputeMessage,
  createAbuseReport,
  createVerifiedReview,
  getComplianceStatus,
  giveGDPRConsent,
  getUserDisputes,
  getBuyerProtections,
  markReviewHelpful,
  reportReview,
  requestDataExport,
  requestDataDeletion,
  resolveDispute,
  escalateDispute,
  getAbuseReports,
  processAbuseReport,
  updateKYCStatus
} = require('../controllers/trustSecurityController');

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, 'uploads/');
  },
  filename: function (req, file, cb) {
    cb(null, `${Date.now()}-${file.originalname}`);
  }
});

const upload = multer({
  storage,
  limits: {
    fileSize: 10 * 1024 * 1024 // 10MB limit
  },
  fileFilter: function (req, file, cb) {
    // Allow images and documents
    if (file.mimetype.startsWith('image/') ||
        file.mimetype === 'application/pdf' ||
        file.mimetype.startsWith('application/')) {
      cb(null, true);
    } else {
      cb(new Error('Invalid file type'), false);
    }
  }
});

// All routes require authentication
router.use(protect);

// Buyer Protection routes
router.post('/buyer-protection', createBuyerProtection);
router.get('/buyer-protection', getBuyerProtections);
router.post('/buyer-protection/:id/claim', upload.array('evidence', 5), activateClaim);

// Dispute Resolution routes
router.get('/disputes', getUserDisputes);
router.post('/disputes', createDispute);
router.post('/disputes/:id/messages', upload.array('attachments', 3), addDisputeMessage);

// Abuse Reporting routes
router.post('/abuse-reports', upload.array('evidence', 5), createAbuseReport);

// Verified Reviews routes
router.post('/reviews', upload.array('images', 5), createVerifiedReview);
router.post('/reviews/:id/helpful', markReviewHelpful);
router.post('/reviews/:id/report', reportReview);

// Compliance routes
router.get('/compliance', getComplianceStatus);
router.post('/compliance/gdpr-consent', giveGDPRConsent);
router.post('/compliance/data-export', requestDataExport);
router.post('/compliance/data-deletion', requestDataDeletion);
router.put('/compliance/kyc', updateKYCStatus);

// Dispute management routes
router.put('/disputes/:id/resolve', authorize('admin'), resolveDispute);
router.put('/disputes/:id/escalate', escalateDispute);

// Abuse report management routes (Admin only)
router.get('/abuse-reports', authorize('admin'), getAbuseReports);
router.put('/abuse-reports/:id/process', authorize('admin'), processAbuseReport);

module.exports = router;
