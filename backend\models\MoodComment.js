const mongoose = require('mongoose');

const MoodCommentSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  mood: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'UserMood',
    required: true,
  },
  text: {
    type: String,
    required: [true, 'Please provide a comment text'],
    maxlength: [500, 'Comment cannot be more than 500 characters'],
  },
  emotions: [
    {
      emotion: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Emotion',
      },
      intensity: {
        type: Number,
        min: 1,
        max: 10,
        default: 5,
      },
    },
  ],
  parentComment: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'MoodComment',
  },
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true },
});

// Virtual field for replies count
MoodCommentSchema.virtual('repliesCount', {
  ref: 'MoodComment',
  localField: '_id',
  foreignField: 'parentComment',
  count: true,
});

// Virtual field for likes count
MoodCommentSchema.virtual('likesCount', {
  ref: 'Like',
  localField: '_id',
  foreignField: 'moodComment',
  count: true,
});

module.exports = mongoose.model('MoodComment', MoodCommentSchema);
