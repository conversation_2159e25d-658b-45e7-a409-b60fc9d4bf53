const asyncHandler = require('../middleware/async');
const ErrorResponse = require('../utils/errorResponse');
const Gift = require('../models/Gift');
const GiftTransaction = require('../models/GiftTransaction');
const User = require('../models/User');
const socketEmitter = require('../utils/socketEmitter');

// @desc    Get all gifts
// @route   GET /api/monetization/gifts
// @access  Public
exports.getGifts = asyncHandler(async (req, res, next) => {
  const gifts = await Gift.find().sort({ price: 1 });
  
  res.status(200).json({
    success: true,
    count: gifts.length,
    data: gifts
  });
});

// @desc    Get single gift
// @route   GET /api/monetization/gifts/:id
// @access  Public
exports.getGift = asyncHandler(async (req, res, next) => {
  const gift = await Gift.findById(req.params.id);
  
  if (!gift) {
    return next(new ErrorResponse(`Gift not found with id of ${req.params.id}`, 404));
  }
  
  res.status(200).json({
    success: true,
    data: gift
  });
});

// @desc    Create new gift
// @route   POST /api/monetization/gifts
// @access  Private/Admin
exports.createGift = asyncHandler(async (req, res, next) => {
  // Add user to req.body
  req.body.user = req.user.id;
  
  const gift = await Gift.create(req.body);
  
  res.status(201).json({
    success: true,
    data: gift
  });
});

// @desc    Update gift
// @route   PUT /api/monetization/gifts/:id
// @access  Private/Admin
exports.updateGift = asyncHandler(async (req, res, next) => {
  let gift = await Gift.findById(req.params.id);
  
  if (!gift) {
    return next(new ErrorResponse(`Gift not found with id of ${req.params.id}`, 404));
  }
  
  gift = await Gift.findByIdAndUpdate(req.params.id, req.body, {
    new: true,
    runValidators: true
  });
  
  res.status(200).json({
    success: true,
    data: gift
  });
});

// @desc    Delete gift
// @route   DELETE /api/monetization/gifts/:id
// @access  Private/Admin
exports.deleteGift = asyncHandler(async (req, res, next) => {
  const gift = await Gift.findById(req.params.id);
  
  if (!gift) {
    return next(new ErrorResponse(`Gift not found with id of ${req.params.id}`, 404));
  }
  
  await gift.remove();
  
  res.status(200).json({
    success: true,
    data: {}
  });
});

// @desc    Send gift to a stream
// @route   POST /api/monetization/live-streams/:streamId/gifts
// @access  Private
exports.sendGift = asyncHandler(async (req, res, next) => {
  const { giftId, message } = req.body;
  
  // Check if gift exists
  const gift = await Gift.findById(giftId);
  if (!gift) {
    return next(new ErrorResponse(`Gift not found with id of ${giftId}`, 404));
  }
  
  // Check if user has enough coins
  const user = await User.findById(req.user.id);
  if (user.coins < gift.price) {
    return next(new ErrorResponse('Not enough coins to send this gift', 400));
  }
  
  // Deduct coins from user
  user.coins -= gift.price;
  await user.save();
  
  // Create gift transaction
  const giftTransaction = await GiftTransaction.create({
    user: req.user.id,
    stream: req.params.streamId,
    gift: giftId,
    message: message || '',
    amount: gift.price
  });
  
  // Populate user and gift data
  await giftTransaction.populate([
    { path: 'user', select: 'name username profilePicture' },
    { path: 'gift' }
  ]);
  
  // Emit socket event
  socketEmitter.emitToRoom(`stream:${req.params.streamId}`, 'stream_gift', {
    streamId: req.params.streamId,
    user: {
      _id: user._id,
      name: user.name,
      username: user.username,
      profilePicture: user.profilePicture
    },
    gift: {
      _id: gift._id,
      name: gift.name,
      price: gift.price,
      imageUrl: gift.imageUrl
    },
    message: message || '',
    createdAt: new Date()
  });
  
  res.status(200).json({
    success: true,
    data: giftTransaction
  });
});

// @desc    Get top gifters for a stream
// @route   GET /api/monetization/live-streams/:streamId/gifts/top
// @access  Public
exports.getTopGifters = asyncHandler(async (req, res, next) => {
  // Aggregate to get top gifters
  const topGifters = await GiftTransaction.aggregate([
    { $match: { stream: req.params.streamId } },
    { $group: { _id: '$user', totalAmount: { $sum: '$amount' } } },
    { $sort: { totalAmount: -1 } },
    { $limit: 10 }
  ]);
  
  // Populate user data
  const populatedGifters = await User.populate(topGifters, {
    path: '_id',
    select: 'name username profilePicture'
  });
  
  // Format response
  const formattedGifters = populatedGifters.map(gifter => ({
    user: gifter._id,
    totalAmount: gifter.totalAmount
  }));
  
  res.status(200).json({
    success: true,
    count: formattedGifters.length,
    data: formattedGifters
  });
});

// @desc    Get gift history for a stream
// @route   GET /api/monetization/live-streams/:streamId/gifts/history
// @access  Public
exports.getGiftHistory = asyncHandler(async (req, res, next) => {
  const giftHistory = await GiftTransaction.find({ stream: req.params.streamId })
    .populate('user', 'name username profilePicture')
    .populate('gift')
    .sort('-createdAt')
    .limit(50);
  
  res.status(200).json({
    success: true,
    count: giftHistory.length,
    data: giftHistory
  });
});
