const express = require('express');
const router = express.Router();
const { protect } = require('../middleware/auth');
const {
  getNotificationPreferences,
  updateNotificationPreferences,
  updatePreferenceCategory,
  togglePreference
} = require('../controllers/notificationPreferenceController');

// All routes are protected
router.use(protect);

// Notification preference routes
router.route('/')
  .get(getNotificationPreferences)
  .put(updateNotificationPreferences);

router.route('/:category')
  .patch(updatePreferenceCategory);

router.route('/toggle/:path')
  .patch(togglePreference);

module.exports = router;
