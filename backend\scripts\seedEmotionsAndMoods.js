const mongoose = require('mongoose');
const dotenv = require('dotenv');
const Emotion = require('../models/Emotion');
const Mood = require('../models/Mood');

// Load env vars
dotenv.config({ path: '../.env' });

// Connect to DB
mongoose.connect(process.env.MONGO_URI, {
  useNewUrlParser: true,
  useUnifiedTopology: true,
});

// Emotions data
const emotions = [
  {
    name: 'Joy',
    description: 'A feeling of great pleasure and happiness',
    category: 'positive',
    icon: 'emoji_emotions',
    color: '#FFD700',
    defaultAudioCue: 'https://example.com/audio/joy.mp3',
  },
  {
    name: 'Sadness',
    description: 'Feeling or showing sorrow; unhappy',
    category: 'negative',
    icon: 'sentiment_very_dissatisfied',
    color: '#4169E1',
    defaultAudioCue: 'https://example.com/audio/sadness.mp3',
  },
  {
    name: 'Anger',
    description: 'A strong feeling of annoyance, displeasure, or hostility',
    category: 'negative',
    icon: 'mood_bad',
    color: '#FF0000',
    defaultAudioCue: 'https://example.com/audio/anger.mp3',
  },
  {
    name: 'Fear',
    description: 'An unpleasant emotion caused by the belief that someone or something is dangerous',
    category: 'negative',
    icon: 'sentiment_very_dissatisfied',
    color: '#800080',
    defaultAudioCue: 'https://example.com/audio/fear.mp3',
  },
  {
    name: 'Surprise',
    description: 'A feeling of mild astonishment or shock caused by something unexpected',
    category: 'neutral',
    icon: 'sentiment_satisfied_alt',
    color: '#FFA500',
    defaultAudioCue: 'https://example.com/audio/surprise.mp3',
  },
  {
    name: 'Disgust',
    description: 'A feeling of revulsion or strong disapproval aroused by something unpleasant or offensive',
    category: 'negative',
    icon: 'sick',
    color: '#008000',
    defaultAudioCue: 'https://example.com/audio/disgust.mp3',
  },
  {
    name: 'Trust',
    description: 'Firm belief in the reliability, truth, ability, or strength of someone or something',
    category: 'positive',
    icon: 'favorite',
    color: '#00BFFF',
    defaultAudioCue: 'https://example.com/audio/trust.mp3',
  },
  {
    name: 'Anticipation',
    description: 'The action of anticipating something; expectation or prediction',
    category: 'neutral',
    icon: 'hourglass_empty',
    color: '#FF69B4',
    defaultAudioCue: 'https://example.com/audio/anticipation.mp3',
  },
  {
    name: 'Love',
    description: 'An intense feeling of deep affection',
    category: 'positive',
    icon: 'favorite',
    color: '#FF1493',
    defaultAudioCue: 'https://example.com/audio/love.mp3',
  },
  {
    name: 'Gratitude',
    description: 'The quality of being thankful; readiness to show appreciation',
    category: 'positive',
    icon: 'volunteer_activism',
    color: '#9370DB',
    defaultAudioCue: 'https://example.com/audio/gratitude.mp3',
  },
];

// Moods data
const moods = [
  {
    name: 'Happy',
    description: 'Feeling or showing pleasure or contentment',
    category: 'positive',
    icon: 'sentiment_very_satisfied',
    color: '#FFD700',
  },
  {
    name: 'Sad',
    description: 'Feeling or showing sorrow; unhappy',
    category: 'negative',
    icon: 'sentiment_very_dissatisfied',
    color: '#4169E1',
  },
  {
    name: 'Angry',
    description: 'Feeling or showing strong annoyance, displeasure, or hostility',
    category: 'negative',
    icon: 'mood_bad',
    color: '#FF0000',
  },
  {
    name: 'Anxious',
    description: 'Experiencing worry, unease, or nervousness',
    category: 'negative',
    icon: 'psychology',
    color: '#800080',
  },
  {
    name: 'Calm',
    description: 'Not showing or feeling nervousness, anger, or other strong emotions',
    category: 'positive',
    icon: 'spa',
    color: '#00BFFF',
  },
  {
    name: 'Excited',
    description: 'Very enthusiastic and eager',
    category: 'positive',
    icon: 'celebration',
    color: '#FFA500',
  },
  {
    name: 'Tired',
    description: 'In need of sleep or rest; weary',
    category: 'neutral',
    icon: 'bedtime',
    color: '#808080',
  },
  {
    name: 'Bored',
    description: 'Feeling weary because one is unoccupied or lacks interest',
    category: 'negative',
    icon: 'sentiment_neutral',
    color: '#A9A9A9',
  },
  {
    name: 'Inspired',
    description: 'Filled with the urge or ability to do or feel something creative',
    category: 'positive',
    icon: 'lightbulb',
    color: '#FFFF00',
  },
  {
    name: 'Nostalgic',
    description: 'Experiencing or exhibiting nostalgia, a sentimental longing for the past',
    category: 'complex',
    icon: 'history',
    color: '#DDA0DD',
  },
];

// Import into DB
const importData = async () => {
  try {
    await Emotion.deleteMany();
    await Mood.deleteMany();

    const createdEmotions = await Emotion.insertMany(emotions);
    
    // Map emotion IDs to their respective moods
    const moodsWithRelatedEmotions = moods.map(mood => {
      const relatedEmotions = [];
      
      // Simple mapping based on categories and names
      createdEmotions.forEach(emotion => {
        if (
          (mood.name === 'Happy' && ['Joy', 'Love', 'Gratitude'].includes(emotion.name)) ||
          (mood.name === 'Sad' && ['Sadness'].includes(emotion.name)) ||
          (mood.name === 'Angry' && ['Anger', 'Disgust'].includes(emotion.name)) ||
          (mood.name === 'Anxious' && ['Fear'].includes(emotion.name)) ||
          (mood.name === 'Calm' && ['Trust'].includes(emotion.name)) ||
          (mood.name === 'Excited' && ['Joy', 'Surprise', 'Anticipation'].includes(emotion.name)) ||
          (mood.name === 'Inspired' && ['Joy', 'Anticipation'].includes(emotion.name)) ||
          (mood.name === 'Nostalgic' && ['Joy', 'Sadness'].includes(emotion.name))
        ) {
          relatedEmotions.push(emotion._id);
        }
      });
      
      return {
        ...mood,
        relatedEmotions,
      };
    });
    
    await Mood.insertMany(moodsWithRelatedEmotions);

    console.log('Data Imported!');
    process.exit();
  } catch (err) {
    console.error(err);
    process.exit(1);
  }
};

importData();
