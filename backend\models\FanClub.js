const mongoose = require('mongoose');

const FanClubTierSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Please add a tier name'],
    trim: true,
    maxlength: [50, 'Name cannot be more than 50 characters']
  },
  description: {
    type: String,
    required: [true, 'Please add a tier description'],
    maxlength: [500, 'Description cannot be more than 500 characters']
  },
  price: {
    type: Number,
    required: [true, 'Please add a monthly price'],
    min: [1, 'Price must be at least $1']
  },
  level: {
    type: Number,
    required: [true, 'Please add a tier level'],
    min: [1, 'Level must be at least 1']
  },
  benefits: {
    type: [String],
    required: [true, 'Please add at least one benefit'],
    validate: {
      validator: function(v) {
        return v.length > 0;
      },
      message: 'Please add at least one benefit'
    }
  },
  color: {
    type: String,
    default: '#1976d2'
  },
  icon: {
    type: String
  }
});

const FanClubEventSchema = new mongoose.Schema({
  title: {
    type: String,
    required: [true, 'Please add an event title'],
    trim: true,
    maxlength: [100, 'Title cannot be more than 100 characters']
  },
  description: {
    type: String,
    maxlength: [1000, 'Description cannot be more than 1000 characters']
  },
  scheduledFor: {
    type: Date,
    required: [true, 'Please add a scheduled date and time']
  },
  duration: {
    type: Number,
    required: [true, 'Please add a duration in minutes'],
    min: [5, 'Duration must be at least 5 minutes']
  },
  tierLevel: {
    type: Number,
    required: [true, 'Please add a minimum tier level required'],
    min: [1, 'Level must be at least 1']
  },
  eventType: {
    type: String,
    enum: ['stream', 'meetup', 'qa', 'other'],
    default: 'stream'
  },
  thumbnail: {
    type: String
  }
});

const FanClubSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Please add a fan club name'],
    trim: true,
    maxlength: [50, 'Name cannot be more than 50 characters']
  },
  description: {
    type: String,
    required: [true, 'Please add a description'],
    maxlength: [1000, 'Description cannot be more than 1000 characters']
  },
  user: {
    type: mongoose.Schema.ObjectId,
    ref: 'User',
    required: true,
    unique: true
  },
  banner: {
    type: String
  },
  tiers: [FanClubTierSchema],
  upcomingEvents: [FanClubEventSchema],
  memberCount: {
    type: Number,
    default: 0
  },
  enabled: {
    type: Boolean,
    default: true
  },
  createdAt: {
    type: Date,
    default: Date.now
  }
});

// Create index for faster queries
FanClubSchema.index({ user: 1 });
FanClubSchema.index({ enabled: 1 });

module.exports = mongoose.model('FanClub', FanClubSchema);
