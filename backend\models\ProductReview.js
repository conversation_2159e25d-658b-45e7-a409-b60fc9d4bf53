const mongoose = require('mongoose');

const ProductReviewSchema = new mongoose.Schema({
  product: {
    type: mongoose.Schema.ObjectId,
    ref: 'Product',
    required: true
  },
  user: {
    type: mongoose.Schema.ObjectId,
    ref: 'User',
    required: true
  },
  order: {
    type: mongoose.Schema.ObjectId,
    ref: 'Order'
  },
  rating: {
    type: Number,
    min: 1,
    max: 5,
    required: [true, 'Please add a rating between 1 and 5']
  },
  title: {
    type: String,
    trim: true,
    maxlength: [100, 'Title cannot be more than 100 characters']
  },
  comment: {
    type: String,
    required: [true, 'Please add a comment'],
    maxlength: [1000, 'Comment cannot be more than 1000 characters']
  },
  pros: [String],
  cons: [String],
  images: [String],
  videos: [String],
  isVerifiedPurchase: {
    type: Boolean,
    default: false
  },
  verificationDetails: {
    orderNumber: String,
    purchaseDate: Date,
    verifiedAt: Date,
    verificationMethod: {
      type: String,
      enum: ['order_match', 'receipt_upload', 'payment_verification', 'manual_verification']
    },
    verifiedBy: {
      type: mongoose.Schema.ObjectId,
      ref: 'User'
    }
  },
  buyerTags: [{
    type: String,
    enum: ['verified_buyer', 'frequent_buyer', 'early_adopter', 'expert_reviewer', 'helpful_reviewer', 'top_contributor']
  }],
  aspects: {
    quality: {
      type: Number,
      min: 1,
      max: 5
    },
    value: {
      type: Number,
      min: 1,
      max: 5
    },
    shipping: {
      type: Number,
      min: 1,
      max: 5
    },
    packaging: {
      type: Number,
      min: 1,
      max: 5
    },
    accuracy: {
      type: Number,
      min: 1,
      max: 5
    }
  },
  helpfulVotes: {
    type: Number,
    default: 0
  },
  unhelpfulVotes: {
    type: Number,
    default: 0
  },
  votedBy: [{
    user: {
      type: mongoose.Schema.ObjectId,
      ref: 'User'
    },
    vote: {
      type: String,
      enum: ['helpful', 'unhelpful']
    },
    votedAt: {
      type: Date,
      default: Date.now
    }
  }],
  reportedCount: {
    type: Number,
    default: 0
  },
  reportedBy: [{
    user: {
      type: mongoose.Schema.ObjectId,
      ref: 'User'
    },
    reason: String,
    reportedAt: {
      type: Date,
      default: Date.now
    }
  }],
  status: {
    type: String,
    enum: ['active', 'hidden', 'reported', 'removed', 'pending_moderation'],
    default: 'active'
  },
  moderationNotes: String,
  vendorResponse: {
    message: String,
    respondedAt: Date,
    respondedBy: {
      type: mongoose.Schema.ObjectId,
      ref: 'User'
    }
  },
  incentivized: {
    type: Boolean,
    default: false
  },
  incentiveDetails: {
    type: String,
    description: String,
    value: Number
  },
  editHistory: [{
    editedAt: Date,
    previousRating: Number,
    previousComment: String,
    reason: String
  }],
  compliance: {
    gdprProcessed: {
      type: Boolean,
      default: false
    },
    dataRetentionUntil: Date,
    anonymized: {
      type: Boolean,
      default: false
    }
  },
  analytics: {
    views: {
      type: Number,
      default: 0
    },
    clicks: {
      type: Number,
      default: 0
    },
    shares: {
      type: Number,
      default: 0
    }
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Prevent user from submitting more than one review per product
ProductReviewSchema.index({ user: 1, product: 1 }, { unique: true });

// Create indexes for better performance
ProductReviewSchema.index({ product: 1, createdAt: -1 });
ProductReviewSchema.index({ product: 1, rating: -1 });
ProductReviewSchema.index({ user: 1 });
ProductReviewSchema.index({ status: 1 });
ProductReviewSchema.index({ isVerifiedPurchase: 1 });
ProductReviewSchema.index({ helpfulVotes: -1 });
ProductReviewSchema.index({ 'buyerTags': 1 });

// Update the updatedAt field before saving
ProductReviewSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

// Static method to get average rating and calculate product analytics
ProductReviewSchema.statics.getAverageRating = async function(productId) {
  const obj = await this.aggregate([
    {
      $match: { product: productId, status: 'active' }
    },
    {
      $group: {
        _id: '$product',
        averageRating: { $avg: '$rating' },
        totalReviews: { $sum: 1 },
        ratingDistribution: {
          $push: '$rating'
        },
        verifiedReviews: {
          $sum: { $cond: ['$isVerifiedPurchase', 1, 0] }
        },
        aspectRatings: {
          $avg: {
            quality: '$aspects.quality',
            value: '$aspects.value',
            shipping: '$aspects.shipping',
            packaging: '$aspects.packaging',
            accuracy: '$aspects.accuracy'
          }
        }
      }
    }
  ]);

  if (obj.length > 0) {
    // Calculate rating distribution
    const distribution = { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 };
    obj[0].ratingDistribution.forEach(rating => {
      distribution[rating] = (distribution[rating] || 0) + 1;
    });

    try {
      await this.model('Product').findByIdAndUpdate(productId, {
        averageRating: Math.round(obj[0].averageRating * 10) / 10,
        reviewCount: obj[0].totalReviews,
        verifiedReviewCount: obj[0].verifiedReviews,
        ratingDistribution: distribution,
        aspectRatings: obj[0].aspectRatings
      });
    } catch (err) {
      console.error('Error updating product rating:', err);
    }
  }
};

// Call getAverageRating after save
ProductReviewSchema.post('save', function() {
  this.constructor.getAverageRating(this.product);
});

// Call getAverageRating before remove
ProductReviewSchema.pre('remove', function() {
  this.constructor.getAverageRating(this.product);
});

// Method to verify purchase
ProductReviewSchema.methods.verifyPurchase = async function(order, method = 'order_match', verifiedBy = null) {
  this.isVerifiedPurchase = true;
  this.verificationDetails = {
    orderNumber: order.orderNumber,
    purchaseDate: order.createdAt,
    verifiedAt: new Date(),
    verificationMethod: method,
    verifiedBy
  };
  
  // Add verified buyer tag
  if (!this.buyerTags.includes('verified_buyer')) {
    this.buyerTags.push('verified_buyer');
  }
  
  return this.save();
};

// Method to add buyer tags
ProductReviewSchema.methods.addBuyerTag = function(tag) {
  if (!this.buyerTags.includes(tag)) {
    this.buyerTags.push(tag);
    return this.save();
  }
  return this;
};

// Method to mark review as helpful
ProductReviewSchema.methods.markHelpful = function(userId) {
  // Check if user already voted
  const existingVote = this.votedBy.find(vote => vote.user.toString() === userId.toString());
  
  if (existingVote) {
    if (existingVote.vote === 'unhelpful') {
      this.unhelpfulVotes -= 1;
      this.helpfulVotes += 1;
      existingVote.vote = 'helpful';
      existingVote.votedAt = new Date();
    }
  } else {
    this.helpfulVotes += 1;
    this.votedBy.push({
      user: userId,
      vote: 'helpful'
    });
  }
  
  return this.save();
};

// Method to mark review as unhelpful
ProductReviewSchema.methods.markUnhelpful = function(userId) {
  // Check if user already voted
  const existingVote = this.votedBy.find(vote => vote.user.toString() === userId.toString());
  
  if (existingVote) {
    if (existingVote.vote === 'helpful') {
      this.helpfulVotes -= 1;
      this.unhelpfulVotes += 1;
      existingVote.vote = 'unhelpful';
      existingVote.votedAt = new Date();
    }
  } else {
    this.unhelpfulVotes += 1;
    this.votedBy.push({
      user: userId,
      vote: 'unhelpful'
    });
  }
  
  return this.save();
};

// Method to report review
ProductReviewSchema.methods.reportReview = function(userId, reason) {
  this.reportedCount += 1;
  this.reportedBy.push({
    user: userId,
    reason
  });
  
  if (this.reportedCount >= 5) {
    this.status = 'reported';
  }
  
  return this.save();
};

// Method to add vendor response
ProductReviewSchema.methods.addVendorResponse = function(message, respondedBy) {
  this.vendorResponse = {
    message,
    respondedAt: new Date(),
    respondedBy
  };
  return this.save();
};

// Method to calculate helpfulness score
ProductReviewSchema.methods.getHelpfulnessScore = function() {
  const totalVotes = this.helpfulVotes + this.unhelpfulVotes;
  if (totalVotes === 0) return 0;
  return (this.helpfulVotes / totalVotes) * 100;
};

module.exports = mongoose.model('ProductReview', ProductReviewSchema);
