const mongoose = require('mongoose');

const GiftTransactionSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.ObjectId,
    ref: 'User',
    required: true
  },
  stream: {
    type: mongoose.Schema.ObjectId,
    ref: 'LiveStream',
    required: true
  },
  gift: {
    type: mongoose.Schema.ObjectId,
    ref: 'Gift',
    required: true
  },
  message: {
    type: String,
    maxlength: [200, 'Message cannot be more than 200 characters']
  },
  amount: {
    type: Number,
    required: true
  },
  createdAt: {
    type: Date,
    default: Date.now
  }
});

// Create index for faster queries
GiftTransactionSchema.index({ stream: 1, createdAt: -1 });
GiftTransactionSchema.index({ user: 1, createdAt: -1 });

module.exports = mongoose.model('GiftTransaction', GiftTransactionSchema);
