const mongoose = require('mongoose');

const ReactionSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Please add a name'],
    trim: true,
    maxlength: [50, 'Name cannot be more than 50 characters'],
  },
  icon: {
    type: String,
    required: [true, 'Please add an icon URL'],
  },
  animation: {
    type: String,
    default: null,
  },
  category: {
    type: String,
    enum: ['basic', 'premium', 'special', 'seasonal', 'custom'],
    default: 'basic',
  },
  cost: {
    type: Number,
    default: 0,
  },
  popularity: {
    type: Number,
    default: 0,
  },
  isActive: {
    type: Boolean,
    default: true,
  },
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
});

module.exports = mongoose.model('Reaction', ReactionSchema);
