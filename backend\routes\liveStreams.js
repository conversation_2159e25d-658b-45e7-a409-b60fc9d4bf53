const express = require('express');
const router = express.Router();
const { protect, optionalAuth } = require('../middleware/auth');
const {
  getLiveStreams,
  getLiveStream,
  createLiveStream,
  updateLiveStream,
  deleteLiveStream,
  startLiveStream,
  endLiveStream,
  getLiveStreamHosts,
  addCoHost,
  removeCoHost,
  inviteCoHost,
  getCoHostInvitations,
  respondToInvitation,
  cancelInvitation,
  saveViewingPreferences,
  getViewingPreferences
} = require('../controllers/liveStreamController');

const {
  getStreamRSVP,
  setStreamRSVP
} = require('../controllers/reminderController');

const {
  getChatMessages,
  addChatMessage,
  deleteChatMessage,
  pinChatMessage,
} = require('../controllers/liveStreamChatController');

const {
  getViewers,
  joinStream,
  leaveStream,
  getViewerStats,
} = require('../controllers/liveStreamViewerController');

const {
  getReactions,
  addReaction,
  deleteReaction,
  getReactionStats,
} = require('../controllers/liveStreamReactionController');

// Public routes
router.get('/', optionalAuth, getLiveStreams);
router.get('/:id', optionalAuth, getLiveStream);
router.get('/:streamId/chat', optionalAuth, getChatMessages);
router.get('/:streamId/viewers', optionalAuth, getViewers);
router.get('/:streamId/reactions', optionalAuth, getReactions);

// Protected routes
router.use(protect);

// Live stream routes
router.post('/', createLiveStream);
router.put('/:id', updateLiveStream);
router.delete('/:id', deleteLiveStream);
router.put('/:id/start', startLiveStream);
router.put('/:id/end', endLiveStream);

// Chat routes
router.post('/:streamId/chat', addChatMessage);
router.delete('/:streamId/chat/:id', deleteChatMessage);
router.put('/:streamId/chat/:id/pin', pinChatMessage);

// Viewer routes
router.post('/:streamId/viewers', joinStream);
router.delete('/:streamId/viewers', leaveStream);
router.get('/:streamId/viewers/stats', getViewerStats);

// Reaction routes
router.post('/:streamId/reactions', addReaction);
router.delete('/:streamId/reactions/:id', deleteReaction);
router.get('/:streamId/reactions/stats', getReactionStats);

// Host routes
router.get('/:id/hosts', getLiveStreamHosts);
router.post('/:id/hosts', addCoHost);
router.delete('/:id/hosts/:userId', removeCoHost);
router.post('/:id/invite', inviteCoHost);

// Invitation routes
router.get('/invitations', getCoHostInvitations);
router.put('/invitations/:id', respondToInvitation);
router.delete('/invitations/:id', cancelInvitation);

// RSVP routes
router.get('/:streamId/rsvp', getStreamRSVP);
router.post('/:streamId/rsvp', setStreamRSVP);

// Viewing preferences routes
router.get('/viewing-preferences', getViewingPreferences);
router.put('/viewing-preferences', saveViewingPreferences);

// Stream clips routes
const streamClipsRouter = require('./streamClips');
router.use('/:streamId/clips', streamClipsRouter);

// NFT access routes
const nftAccessRouter = require('./nftAccess');
router.use('/:streamId/nft-access', nftAccessRouter);

// Voice command routes
router.post('/:streamId/voice-commands/execute', require('./voiceCommands'));

// Fan rewards routes
const fanRewardsRouter = require('./fanRewards');
router.use('/:streamerId/rewards', fanRewardsRouter);

module.exports = router;
