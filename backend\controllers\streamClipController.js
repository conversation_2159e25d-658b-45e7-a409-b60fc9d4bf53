const StreamClip = require('../models/StreamClip');
const LiveStream = require('../models/LiveStream');
const User = require('../models/User');
const asyncHandler = require('../middleware/async');
const ErrorResponse = require('../utils/errorResponse');
const { cloudinary, uploadToCloudinary } = require('../config/cloudinary');
const socketEmitter = require('../utils/socketEmitter');

/**
 * Get all clips for a stream
 * @route GET /api/live-streams/:streamId/clips
 * @access Public
 */
exports.getStreamClips = asyncHandler(async (req, res, next) => {
  const { page = 1, limit = 10, sort = '-createdAt', isAIGenerated, isRemixed } = req.query;
  const skip = (page - 1) * limit;

  // Build query
  const query = { stream: req.params.streamId, status: 'ready' };

  // Add filters if provided
  if (isAIGenerated !== undefined) {
    query.isAIGenerated = isAIGenerated === 'true';
  }

  if (isRemixed !== undefined) {
    query.isRemixed = isRemixed === 'true';
  }

  // Execute query
  const clips = await StreamClip.find(query)
    .sort(sort)
    .skip(skip)
    .limit(parseInt(limit))
    .populate('user', 'username name profilePicture isVerified');

  // Get total count
  const total = await StreamClip.countDocuments(query);

  res.status(200).json({
    success: true,
    count: clips.length,
    total,
    pagination: {
      page: parseInt(page),
      limit: parseInt(limit),
      totalPages: Math.ceil(total / limit),
    },
    data: clips,
  });
});

/**
 * Get a single clip
 * @route GET /api/clips/:id
 * @access Public
 */
exports.getClip = asyncHandler(async (req, res, next) => {
  const clip = await StreamClip.findById(req.params.id)
    .populate('user', 'username name profilePicture isVerified')
    .populate('stream', 'title user');

  if (!clip) {
    return next(new ErrorResponse(`Clip not found with id of ${req.params.id}`, 404));
  }

  // Increment view count
  clip.viewCount += 1;
  await clip.save();

  res.status(200).json({
    success: true,
    data: clip,
  });
});

/**
 * Create a new clip
 * @route POST /api/live-streams/:streamId/clips
 * @access Private
 */
exports.createClip = asyncHandler(async (req, res, next) => {
  const stream = await LiveStream.findById(req.params.streamId);

  if (!stream) {
    return next(new ErrorResponse(`Stream not found with id of ${req.params.streamId}`, 404));
  }

  // Check if user is authorized to create clips
  const isOwner = stream.user.toString() === req.user.id;
  const isCoHost = stream.coHosts.some(id => id.toString() === req.user.id);

  if (!isOwner && !isCoHost) {
    return next(new ErrorResponse('Not authorized to create clips for this stream', 403));
  }

  // Add user and stream to request body
  req.body.user = req.user.id;
  req.body.stream = req.params.streamId;

  // Calculate duration
  if (req.body.startTime && req.body.endTime) {
    req.body.duration = req.body.endTime - req.body.startTime;
  }

  // Create clip
  const clip = await StreamClip.create(req.body);

  // Populate user and stream
  await clip.populate('user', 'username name profilePicture isVerified');

  // Emit socket event for new clip
  socketEmitter.emitNewClip(clip);

  res.status(201).json({
    success: true,
    data: clip,
  });
});

/**
 * Update a clip
 * @route PUT /api/clips/:id
 * @access Private
 */
exports.updateClip = asyncHandler(async (req, res, next) => {
  let clip = await StreamClip.findById(req.params.id);

  if (!clip) {
    return next(new ErrorResponse(`Clip not found with id of ${req.params.id}`, 404));
  }

  // Check if user is authorized to update clip
  if (clip.user.toString() !== req.user.id) {
    return next(new ErrorResponse('Not authorized to update this clip', 403));
  }

  // Update clip
  clip = await StreamClip.findByIdAndUpdate(req.params.id, req.body, {
    new: true,
    runValidators: true,
  });

  // Populate user and stream
  await clip.populate('user', 'username name profilePicture isVerified');

  res.status(200).json({
    success: true,
    data: clip,
  });
});

/**
 * Delete a clip
 * @route DELETE /api/clips/:id
 * @access Private
 */
exports.deleteClip = asyncHandler(async (req, res, next) => {
  const clip = await StreamClip.findById(req.params.id);

  if (!clip) {
    return next(new ErrorResponse(`Clip not found with id of ${req.params.id}`, 404));
  }

  // Check if user is authorized to delete clip
  if (clip.user.toString() !== req.user.id) {
    return next(new ErrorResponse('Not authorized to delete this clip', 403));
  }

  // Delete clip from Cloudinary if exists
  if (clip.publicId) {
    await cloudinary.uploader.destroy(clip.publicId);
  }

  // Delete clip
  await clip.remove();

  res.status(200).json({
    success: true,
    data: {},
  });
});

/**
 * Generate AI clips for a stream
 * @route POST /api/live-streams/:streamId/ai-clips
 * @access Private
 */
exports.generateAIClips = asyncHandler(async (req, res, next) => {
  const stream = await LiveStream.findById(req.params.streamId);

  if (!stream) {
    return next(new ErrorResponse(`Stream not found with id of ${req.params.streamId}`, 404));
  }

  // Check if user is authorized to generate AI clips
  if (stream.user.toString() !== req.user.id) {
    return next(new ErrorResponse('Not authorized to generate AI clips for this stream', 403));
  }

  // Check if AI clip remixing is enabled
  if (!stream.settings.aiClipRemixing.enabled) {
    return next(new ErrorResponse('AI clip remixing is not enabled for this stream', 400));
  }

  // Start AI clip generation process (this would be a background job in a real implementation)
  // For demo purposes, we'll create a sample AI-generated clip
  const aiClip = await StreamClip.create({
    stream: req.params.streamId,
    user: req.user.id,
    title: `AI Highlight: ${stream.title}`,
    description: 'Automatically generated highlight clip',
    startTime: 30, // Sample start time
    endTime: 60, // Sample end time
    duration: 30, // Sample duration
    clipUrl: 'https://example.com/sample-clip.mp4', // Sample URL
    thumbnailUrl: 'https://example.com/sample-thumbnail.jpg', // Sample URL
    isAIGenerated: true,
    aiMetadata: {
      confidence: 0.85,
      tags: ['highlight', 'action', 'engagement'],
      detectedEmotions: ['excitement', 'joy'],
      highlightReason: 'engagement_spike',
    },
    status: 'ready',
  });

  // Populate user
  await aiClip.populate('user', 'username name profilePicture isVerified');

  // Emit socket event for new AI clip
  socketEmitter.emitNewClip(aiClip);

  res.status(200).json({
    success: true,
    message: 'AI clip generation started',
    data: aiClip,
  });
});

/**
 * Remix a clip
 * @route POST /api/clips/:id/remix
 * @access Private
 */
exports.remixClip = asyncHandler(async (req, res, next) => {
  const clip = await StreamClip.findById(req.params.id);

  if (!clip) {
    return next(new ErrorResponse(`Clip not found with id of ${req.params.id}`, 404));
  }

  // Create a remixed version of the clip
  const remixedClip = await StreamClip.create({
    stream: clip.stream,
    user: req.user.id,
    title: `${clip.title} (Remixed)`,
    description: req.body.description || `Remixed version of ${clip.title}`,
    startTime: clip.startTime,
    endTime: clip.endTime,
    duration: clip.duration,
    clipUrl: clip.clipUrl, // In a real implementation, this would be a new processed URL
    thumbnailUrl: clip.thumbnailUrl, // In a real implementation, this would be a new processed URL
    isRemixed: true,
    remixMetadata: {
      effects: req.body.effects || [],
      filters: req.body.filters || [],
      music: req.body.music || null,
      transitions: req.body.transitions || [],
      textOverlays: req.body.textOverlays || [],
    },
    status: 'processing', // In a real implementation, this would start as processing
  });

  // In a real implementation, we would start a background job to process the remix
  // For demo purposes, we'll update the status to ready immediately
  remixedClip.status = 'ready';
  await remixedClip.save();

  // Populate user
  await remixedClip.populate('user', 'username name profilePicture isVerified');

  // Emit socket event for new remixed clip
  socketEmitter.emitNewClip(remixedClip);

  res.status(200).json({
    success: true,
    message: 'Clip remix started',
    data: remixedClip,
  });
});
