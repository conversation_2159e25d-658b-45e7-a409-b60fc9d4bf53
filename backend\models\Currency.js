const mongoose = require('mongoose');

const CurrencySchema = new mongoose.Schema({
  code: {
    type: String,
    required: true,
    unique: true,
    uppercase: true,
    length: 3
  },
  name: {
    type: String,
    required: true
  },
  symbol: {
    type: String,
    required: true
  },
  symbolPosition: {
    type: String,
    enum: ['before', 'after'],
    default: 'before'
  },
  decimalPlaces: {
    type: Number,
    default: 2,
    min: 0,
    max: 8
  },
  exchangeRates: [{
    toCurrency: {
      type: String,
      required: true,
      uppercase: true
    },
    rate: {
      type: Number,
      required: true,
      min: 0
    },
    provider: {
      type: String,
      enum: ['manual', 'fixer', 'openexchangerates', 'currencylayer', 'exchangerate-api'],
      default: 'manual'
    },
    lastUpdated: {
      type: Date,
      default: Date.now
    },
    isActive: {
      type: Boolean,
      default: true
    }
  }],
  taxConfiguration: {
    vatRate: {
      type: Number,
      default: 0,
      min: 0,
      max: 100
    },
    salesTaxRate: {
      type: Number,
      default: 0,
      min: 0,
      max: 100
    },
    withholdingTaxRate: {
      type: Number,
      default: 0,
      min: 0,
      max: 100
    },
    taxRegion: String,
    taxInclusive: {
      type: Boolean,
      default: false
    }
  },
  payoutSupport: {
    bankTransfer: {
      supported: {
        type: Boolean,
        default: true
      },
      minimumAmount: {
        type: Number,
        default: 10
      },
      processingFee: {
        type: Number,
        default: 0
      },
      processingTime: {
        type: String,
        default: '1-3 business days'
      }
    },
    paypal: {
      supported: {
        type: Boolean,
        default: true
      },
      minimumAmount: {
        type: Number,
        default: 1
      },
      processingFee: {
        type: Number,
        default: 0
      },
      processingTime: {
        type: String,
        default: 'Instant'
      }
    },
    mobileMoney: {
      supported: {
        type: Boolean,
        default: false
      },
      providers: [String],
      minimumAmount: {
        type: Number,
        default: 5
      },
      processingFee: {
        type: Number,
        default: 0
      },
      processingTime: {
        type: String,
        default: 'Instant'
      }
    },
    crypto: {
      supported: {
        type: Boolean,
        default: false
      },
      supportedCurrencies: [String],
      minimumAmount: {
        type: Number,
        default: 20
      },
      processingFee: {
        type: Number,
        default: 0
      },
      processingTime: {
        type: String,
        default: '10-60 minutes'
      }
    }
  },
  formatting: {
    thousandsSeparator: {
      type: String,
      default: ','
    },
    decimalSeparator: {
      type: String,
      default: '.'
    },
    positiveFormat: {
      type: String,
      default: '{symbol}{amount}'
    },
    negativeFormat: {
      type: String,
      default: '-{symbol}{amount}'
    }
  },
  isActive: {
    type: Boolean,
    default: true
  },
  isBaseCurrency: {
    type: Boolean,
    default: false
  },
  supportedCountries: [String],
  restrictions: {
    minimumTransaction: {
      type: Number,
      default: 0
    },
    maximumTransaction: {
      type: Number,
      default: null
    },
    dailyLimit: {
      type: Number,
      default: null
    },
    monthlyLimit: {
      type: Number,
      default: null
    }
  },
  metadata: {
    countryCode: String,
    region: String,
    timezone: String,
    locale: String
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Update the updatedAt field before saving
CurrencySchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

// Get exchange rate to another currency
CurrencySchema.methods.getExchangeRate = function(toCurrency) {
  const rate = this.exchangeRates.find(r => 
    r.toCurrency === toCurrency.toUpperCase() && r.isActive
  );
  return rate ? rate.rate : null;
};

// Update exchange rate
CurrencySchema.methods.updateExchangeRate = function(toCurrency, rate, provider = 'manual') {
  const existingRate = this.exchangeRates.find(r => r.toCurrency === toCurrency.toUpperCase());
  
  if (existingRate) {
    existingRate.rate = rate;
    existingRate.provider = provider;
    existingRate.lastUpdated = new Date();
  } else {
    this.exchangeRates.push({
      toCurrency: toCurrency.toUpperCase(),
      rate,
      provider,
      lastUpdated: new Date(),
      isActive: true
    });
  }
  
  return this.save();
};

// Convert amount to another currency
CurrencySchema.methods.convertTo = function(amount, toCurrency) {
  const rate = this.getExchangeRate(toCurrency);
  if (!rate) {
    throw new Error(`Exchange rate not found for ${this.code} to ${toCurrency}`);
  }
  return amount * rate;
};

// Format amount according to currency rules
CurrencySchema.methods.formatAmount = function(amount, showSymbol = true) {
  const absAmount = Math.abs(amount);
  const formattedAmount = absAmount.toLocaleString('en-US', {
    minimumFractionDigits: this.decimalPlaces,
    maximumFractionDigits: this.decimalPlaces
  });
  
  let formatted;
  if (showSymbol) {
    const template = amount >= 0 ? this.formatting.positiveFormat : this.formatting.negativeFormat;
    formatted = template
      .replace('{symbol}', this.symbol)
      .replace('{amount}', formattedAmount);
  } else {
    formatted = (amount >= 0 ? '' : '-') + formattedAmount;
  }
  
  return formatted;
};

// Check if payout method is supported
CurrencySchema.methods.isPayoutMethodSupported = function(method) {
  return this.payoutSupport[method]?.supported || false;
};

// Get minimum payout amount for method
CurrencySchema.methods.getMinimumPayoutAmount = function(method) {
  return this.payoutSupport[method]?.minimumAmount || 0;
};

// Get processing fee for method
CurrencySchema.methods.getProcessingFee = function(method, amount) {
  const methodConfig = this.payoutSupport[method];
  if (!methodConfig) return 0;
  
  // Fee can be fixed or percentage
  if (typeof methodConfig.processingFee === 'number') {
    return methodConfig.processingFee;
  } else if (typeof methodConfig.processingFee === 'object') {
    const { fixed = 0, percentage = 0 } = methodConfig.processingFee;
    return fixed + (amount * percentage / 100);
  }
  
  return 0;
};

// Static method to get all active currencies
CurrencySchema.statics.getActiveCurrencies = function() {
  return this.find({ isActive: true }).sort({ code: 1 });
};

// Static method to get base currency
CurrencySchema.statics.getBaseCurrency = function() {
  return this.findOne({ isBaseCurrency: true });
};

// Static method to convert between currencies
CurrencySchema.statics.convertAmount = async function(amount, fromCurrency, toCurrency) {
  if (fromCurrency === toCurrency) return amount;
  
  const fromCurr = await this.findOne({ code: fromCurrency.toUpperCase() });
  if (!fromCurr) {
    throw new Error(`Currency ${fromCurrency} not found`);
  }
  
  return fromCurr.convertTo(amount, toCurrency);
};

// Create indexes for better performance
CurrencySchema.index({ code: 1 });
CurrencySchema.index({ isActive: 1 });
CurrencySchema.index({ isBaseCurrency: 1 });
CurrencySchema.index({ supportedCountries: 1 });

module.exports = mongoose.model('Currency', CurrencySchema);
