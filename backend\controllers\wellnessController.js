const User = require('../models/User');
const Emotion = require('../models/Emotion');
const { createError } = require('../utils/error');

/**
 * Get wellness settings for the authenticated user
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.getWellnessSettings = async (req, res, next) => {
  try {
    const user = await User.findById(req.user.id)
      .populate('wellnessSettings.contentFilters.preferredEmotions', 'name category icon color')
      .populate('wellnessSettings.contentFilters.avoidedEmotions', 'name category icon color');
    
    if (!user) {
      return next(createError(404, 'User not found'));
    }
    
    res.status(200).json({
      success: true,
      data: user.wellnessSettings,
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Update content filters
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.updateContentFilters = async (req, res, next) => {
  try {
    const { hideNegativeContent, preferredEmotions, avoidedEmotions } = req.body;
    
    // Validate emotion IDs if provided
    if (preferredEmotions) {
      for (const emotionId of preferredEmotions) {
        const emotion = await Emotion.findById(emotionId);
        if (!emotion) {
          return next(createError(400, `Emotion with ID ${emotionId} not found`));
        }
      }
    }
    
    if (avoidedEmotions) {
      for (const emotionId of avoidedEmotions) {
        const emotion = await Emotion.findById(emotionId);
        if (!emotion) {
          return next(createError(400, `Emotion with ID ${emotionId} not found`));
        }
      }
    }
    
    // Update user's content filters
    const updateData = {};
    
    if (hideNegativeContent !== undefined) {
      updateData['wellnessSettings.contentFilters.hideNegativeContent'] = hideNegativeContent;
    }
    
    if (preferredEmotions) {
      updateData['wellnessSettings.contentFilters.preferredEmotions'] = preferredEmotions;
    }
    
    if (avoidedEmotions) {
      updateData['wellnessSettings.contentFilters.avoidedEmotions'] = avoidedEmotions;
    }
    
    const user = await User.findByIdAndUpdate(
      req.user.id,
      { $set: updateData },
      { new: true, runValidators: true }
    ).populate('wellnessSettings.contentFilters.preferredEmotions', 'name category icon color')
     .populate('wellnessSettings.contentFilters.avoidedEmotions', 'name category icon color');
    
    if (!user) {
      return next(createError(404, 'User not found'));
    }
    
    res.status(200).json({
      success: true,
      data: user.wellnessSettings.contentFilters,
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Update time limit settings
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.updateTimeLimit = async (req, res, next) => {
  try {
    const { enabled, dailyLimitMinutes } = req.body;
    
    // Validate daily limit if provided
    if (dailyLimitMinutes !== undefined && (dailyLimitMinutes < 1 || dailyLimitMinutes > 1440)) {
      return next(createError(400, 'Daily limit must be between 1 and 1440 minutes (24 hours)'));
    }
    
    // Update user's time limit settings
    const updateData = {};
    
    if (enabled !== undefined) {
      updateData['wellnessSettings.timeLimit.enabled'] = enabled;
    }
    
    if (dailyLimitMinutes !== undefined) {
      updateData['wellnessSettings.timeLimit.dailyLimitMinutes'] = dailyLimitMinutes;
    }
    
    const user = await User.findByIdAndUpdate(
      req.user.id,
      { $set: updateData },
      { new: true, runValidators: true }
    );
    
    if (!user) {
      return next(createError(404, 'User not found'));
    }
    
    res.status(200).json({
      success: true,
      data: user.wellnessSettings.timeLimit,
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Update hide metrics setting
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.updateHideMetrics = async (req, res, next) => {
  try {
    const { hideMetrics } = req.body;
    
    if (hideMetrics === undefined) {
      return next(createError(400, 'hideMetrics field is required'));
    }
    
    const user = await User.findByIdAndUpdate(
      req.user.id,
      { $set: { 'wellnessSettings.hideMetrics': hideMetrics } },
      { new: true, runValidators: true }
    );
    
    if (!user) {
      return next(createError(404, 'User not found'));
    }
    
    res.status(200).json({
      success: true,
      data: { hideMetrics: user.wellnessSettings.hideMetrics },
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Update wellness reminders settings
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.updateWellnessReminders = async (req, res, next) => {
  try {
    const { enabled, frequency } = req.body;
    
    // Validate frequency if provided
    const validFrequencies = ['hourly', 'every2hours', 'every3hours', 'every4hours'];
    if (frequency && !validFrequencies.includes(frequency)) {
      return next(createError(400, `Frequency must be one of: ${validFrequencies.join(', ')}`));
    }
    
    // Update user's wellness reminders settings
    const updateData = {};
    
    if (enabled !== undefined) {
      updateData['wellnessSettings.wellnessReminders.enabled'] = enabled;
    }
    
    if (frequency) {
      updateData['wellnessSettings.wellnessReminders.frequency'] = frequency;
    }
    
    const user = await User.findByIdAndUpdate(
      req.user.id,
      { $set: updateData },
      { new: true, runValidators: true }
    );
    
    if (!user) {
      return next(createError(404, 'User not found'));
    }
    
    res.status(200).json({
      success: true,
      data: user.wellnessSettings.wellnessReminders,
    });
  } catch (err) {
    next(err);
  }
};
