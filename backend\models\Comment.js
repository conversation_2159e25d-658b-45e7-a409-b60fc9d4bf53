const mongoose = require('mongoose');

const CommentSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  post: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Post',
  },
  reel: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Reel',
  },
  text: {
    type: String,
    required: [true, 'Please provide a comment text'],
    maxlength: [500, 'Comment cannot be more than 500 characters'],
  },
  emotions: [
    {
      emotion: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Emotion',
      },
      intensity: {
        type: Number,
        min: 1,
        max: 10,
        default: 5,
      },
    },
  ],
  parentComment: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Comment',
  },
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true },
});

// A comment must be associated with either a post or a reel
CommentSchema.pre('validate', function(next) {
  if (!this.post && !this.reel) {
    return next(new Error('Comment must be associated with either a post or a reel'));
  }
  if (this.post && this.reel) {
    return next(new Error('Comment cannot be associated with both a post and a reel'));
  }
  next();
});

// Virtual field for replies count
CommentSchema.virtual('repliesCount', {
  ref: 'Comment',
  localField: '_id',
  foreignField: 'parentComment',
  count: true,
});

// Virtual field for likes count
CommentSchema.virtual('likesCount', {
  ref: 'Like',
  localField: '_id',
  foreignField: 'comment',
  count: true,
});

module.exports = mongoose.model('Comment', CommentSchema);
