const asyncHandler = require('express-async-handler');
const Post = require('../models/Post');
const Reel = require('../models/Reel');

// @desc    Get explore content
// @route   GET /api/explore
// @access  Public
const getExplore = asyncHandler(async (req, res) => {
  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 20;
  const skip = (page - 1) * limit;

  // Get popular posts and reels
  const posts = await Post.find({ isArchived: false })
    .sort({ likesCount: -1, commentsCount: -1, createdAt: -1 })
    .skip(skip)
    .limit(Math.floor(limit / 2))
    .populate('user', 'username fullName avatar');

  const reels = await Reel.find({ isArchived: false })
    .sort({ views: -1, likesCount: -1, createdAt: -1 })
    .skip(skip)
    .limit(Math.ceil(limit / 2))
    .populate('user', 'username fullName avatar');

  // Combine and shuffle content
  const content = [...posts, ...reels].sort(() => Math.random() - 0.5);

  res.status(200).json({
    success: true,
    content,
    pagination: {
      page,
      limit,
      hasMore: content.length === limit
    }
  });
});

// @desc    Get trending content
// @route   GET /api/explore/trending
// @access  Public
const getTrending = asyncHandler(async (req, res) => {
  // Get trending hashtags
  const trendingHashtags = await Post.aggregate([
    { $unwind: '$hashtags' },
    { $group: { _id: '$hashtags', count: { $sum: 1 } } },
    { $sort: { count: -1 } },
    { $limit: 10 }
  ]);

  // Get trending posts
  const trendingPosts = await Post.find({ isArchived: false })
    .sort({ likesCount: -1, commentsCount: -1 })
    .limit(5)
    .populate('user', 'username fullName avatar');

  // Get trending reels
  const trendingReels = await Reel.find({ isArchived: false })
    .sort({ views: -1, likesCount: -1 })
    .limit(5)
    .populate('user', 'username fullName avatar');

  res.status(200).json({
    success: true,
    trending: {
      hashtags: trendingHashtags,
      posts: trendingPosts,
      reels: trendingReels
    }
  });
});

module.exports = {
  getExplore,
  getTrending
};
