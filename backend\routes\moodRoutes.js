const express = require('express');
const router = express.Router();
const {
  getMoods,
  getMoodById,
  createMood,
  updateMood,
  deleteMood,
  getCurrentMood,
  setCurrentMood,
  getMoodHistory
} = require('../controllers/moodController');
const { protect, admin } = require('../middleware/authMiddleware');

// Public routes
router.get('/', getMoods);
router.get('/:id', getMoodById);

// Private routes
router.get('/user/current', protect, getCurrentMood);
router.post('/user/current', protect, setCurrentMood);
router.get('/user/history', protect, getMoodHistory);

// Admin routes
router.post('/', protect, admin, createMood);
router.put('/:id', protect, admin, updateMood);
router.delete('/:id', protect, admin, deleteMood);

module.exports = router;
