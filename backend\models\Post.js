const mongoose = require('mongoose');

const PostSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  caption: {
    type: String,
    maxlength: [2200, 'Caption cannot be more than 2200 characters'],
  },
  media: [
    {
      url: {
        type: String,
        required: true,
      },
      type: {
        type: String,
        enum: ['image', 'video'],
        required: true,
      },
      filename: {
        type: String,
        required: true,
      },
      originalname: {
        type: String,
      },
      mimetype: {
        type: String,
      },
      size: {
        type: Number,
      },
      // Cloudinary specific fields
      cloudinaryPublicId: {
        type: String,
      },
      cloudinaryVersion: {
        type: String,
      },
      cloudinarySignature: {
        type: String,
      },
      cloudinaryFormat: {
        type: String,
      },
      // Video specific fields
      thumbnailUrl: {
        type: String,
      },
      duration: {
        type: Number,
      },
      videoCodec: {
        type: String,
      },
      audioCodec: {
        type: String,
      },
      width: {
        type: Number,
      },
      height: {
        type: Number,
      },
    },
  ],
  location: {
    type: String,
  },
  tags: [
    {
      type: String,
    },
  ],
  emotions: [
    {
      emotion: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Emotion',
        required: true,
      },
      intensity: {
        type: Number,
        min: 1,
        max: 10,
        default: 5,
      },
      position: {
        x: {
          type: Number,
          min: 0,
          max: 100,
          default: 50,
        },
        y: {
          type: Number,
          min: 0,
          max: 100,
          default: 50,
        },
      },
      audio: {
        enabled: {
          type: Boolean,
          default: false,
        },
        cue: {
          type: mongoose.Schema.Types.ObjectId,
          ref: 'AudioCue',
        },
        volume: {
          type: Number,
          min: 0,
          max: 1,
          default: 0.5,
        },
      },
      visualEffect: {
        enabled: {
          type: Boolean,
          default: false,
        },
        type: {
          type: String,
          enum: ['pulse', 'glow', 'particles', 'color', 'none'],
          default: 'none',
        },
        intensity: {
          type: Number,
          min: 1,
          max: 10,
          default: 5,
        },
      },
    },
  ],
  arEffects: [
    {
      type: {
        type: String,
        enum: ['filter', 'object', 'background', 'animation'],
        required: true,
      },
      effectId: {
        type: String,
        required: true,
      },
      position: {
        x: {
          type: Number,
          default: 50,
        },
        y: {
          type: Number,
          default: 50,
        },
        z: {
          type: Number,
          default: 0,
        },
      },
      scale: {
        x: {
          type: Number,
          default: 1,
        },
        y: {
          type: Number,
          default: 1,
        },
        z: {
          type: Number,
          default: 1,
        },
      },
      rotation: {
        x: {
          type: Number,
          default: 0,
        },
        y: {
          type: Number,
          default: 0,
        },
        z: {
          type: Number,
          default: 0,
        },
      },
    },
  ],
  viewsCount: {
    type: Number,
    default: 0,
  },
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true },
});

// Virtual fields for likes and comments counts
PostSchema.virtual('likesCount', {
  ref: 'Like',
  localField: '_id',
  foreignField: 'post',
  count: true,
});

PostSchema.virtual('commentsCount', {
  ref: 'Comment',
  localField: '_id',
  foreignField: 'post',
  count: true,
});

module.exports = mongoose.model('Post', PostSchema);
