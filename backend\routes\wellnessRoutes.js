const express = require('express');
const router = express.Router();
const {
  getWellnessSettings,
  updateWellnessSettings,
  getMoodBasedFeed,
  getWellnessInsights
} = require('../controllers/wellnessController');
const { protect } = require('../middleware/authMiddleware');

// All routes are private
router.get('/settings', protect, getWellnessSettings);
router.put('/settings', protect, updateWellnessSettings);
router.get('/feed', protect, getMoodBasedFeed);
router.get('/insights', protect, getWellnessInsights);

module.exports = router;
