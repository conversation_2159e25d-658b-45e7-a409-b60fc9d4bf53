const BlockedWord = require('../models/BlockedWord');
const ModerationSettings = require('../models/ModerationSettings');
const asyncHandler = require('../middleware/async');
const ErrorResponse = require('../utils/errorResponse');

/**
 * @desc    Get all blocked words
 * @route   GET /api/moderation/blocked-words
 * @access  Private (Admin/Moderator)
 */
exports.getBlockedWords = asyncHandler(async (req, res, next) => {
  // Check if user is admin or moderator
  if (req.user.role !== 'admin' && req.user.role !== 'moderator') {
    // Regular users can only see their own blocked words
    const blockedWords = await BlockedWord.find({
      scope: 'user',
      scopeId: req.user.id,
      isActive: true
    });

    return res.status(200).json({
      success: true,
      count: blockedWords.length,
      data: blockedWords
    });
  }

  // For admins/moderators, allow filtering
  const { scope, category, severity } = req.query;
  
  const filter = {};
  
  if (scope) filter.scope = scope;
  if (category) filter.category = category;
  if (severity) filter.severity = severity;
  
  const blockedWords = await BlockedWord.find(filter);

  res.status(200).json({
    success: true,
    count: blockedWords.length,
    data: blockedWords
  });
});

/**
 * @desc    Get blocked words for a specific scope (stream or user)
 * @route   GET /api/moderation/blocked-words/:scope/:scopeId
 * @access  Private
 */
exports.getScopedBlockedWords = asyncHandler(async (req, res, next) => {
  const { scope, scopeId } = req.params;

  // Validate scope
  if (scope !== 'stream' && scope !== 'user') {
    return next(new ErrorResponse('Invalid scope. Must be "stream" or "user"', 400));
  }

  // Check permissions
  if (scope === 'user' && scopeId !== req.user.id && req.user.role !== 'admin' && req.user.role !== 'moderator') {
    return next(new ErrorResponse('Not authorized to view these blocked words', 403));
  }

  if (scope === 'stream') {
    // Check if user is stream owner or moderator
    const moderationSettings = await ModerationSettings.findOne({
      ownerType: 'LiveStream',
      owner: scopeId
    });

    if (!moderationSettings) {
      return next(new ErrorResponse('Stream moderation settings not found', 404));
    }

    const isStreamModerator = moderationSettings.moderators.some(
      mod => mod.user.toString() === req.user.id
    );

    if (
      !isStreamModerator && 
      moderationSettings.owner.toString() !== req.user.id && 
      req.user.role !== 'admin' && 
      req.user.role !== 'moderator'
    ) {
      return next(new ErrorResponse('Not authorized to view these blocked words', 403));
    }
  }

  // Get blocked words
  const blockedWords = await BlockedWord.find({
    scope,
    scopeId,
    isActive: true
  });

  res.status(200).json({
    success: true,
    count: blockedWords.length,
    data: blockedWords
  });
});

/**
 * @desc    Create a new blocked word
 * @route   POST /api/moderation/blocked-words
 * @access  Private
 */
exports.createBlockedWord = asyncHandler(async (req, res, next) => {
  const { word, severity, category, scope, scopeId, action, replacement, exactMatch, isRegex } = req.body;

  // Validate input
  if (!word) {
    return next(new ErrorResponse('Word is required', 400));
  }

  // Check permissions
  if (scope === 'global' && req.user.role !== 'admin' && req.user.role !== 'moderator') {
    return next(new ErrorResponse('Not authorized to create global blocked words', 403));
  }

  if (scope === 'stream' && scopeId) {
    // Check if user is stream owner or moderator
    const moderationSettings = await ModerationSettings.findOne({
      ownerType: 'LiveStream',
      owner: scopeId
    });

    if (!moderationSettings) {
      return next(new ErrorResponse('Stream moderation settings not found', 404));
    }

    const isStreamModerator = moderationSettings.moderators.some(
      mod => mod.user.toString() === req.user.id && mod.permissions.canModifySettings
    );

    if (
      !isStreamModerator && 
      moderationSettings.owner.toString() !== req.user.id && 
      req.user.role !== 'admin'
    ) {
      return next(new ErrorResponse('Not authorized to create blocked words for this stream', 403));
    }
  }

  // Check if word already exists in the same scope
  const existingWord = await BlockedWord.findOne({
    word: word.toLowerCase(),
    scope,
    scopeId: scopeId || null
  });

  if (existingWord) {
    return next(new ErrorResponse('This word is already blocked in this scope', 400));
  }

  // Create blocked word
  const blockedWord = await BlockedWord.create({
    word: word.toLowerCase(),
    severity: severity || 'medium',
    category: category || 'custom',
    scope: scope || 'user',
    scopeId: scopeId || (scope === 'user' ? req.user.id : null),
    scopeModel: scope === 'stream' ? 'LiveStream' : scope === 'user' ? 'User' : null,
    action: action || 'flag',
    replacement: replacement || '****',
    exactMatch: exactMatch || false,
    isRegex: isRegex || false,
    createdBy: req.user.id
  });

  res.status(201).json({
    success: true,
    data: blockedWord
  });
});

/**
 * @desc    Update a blocked word
 * @route   PUT /api/moderation/blocked-words/:id
 * @access  Private
 */
exports.updateBlockedWord = asyncHandler(async (req, res, next) => {
  const { severity, category, action, replacement, exactMatch, isRegex, isActive } = req.body;

  // Find blocked word
  const blockedWord = await BlockedWord.findById(req.params.id);

  if (!blockedWord) {
    return next(new ErrorResponse('Blocked word not found', 404));
  }

  // Check permissions
  if (blockedWord.scope === 'global' && req.user.role !== 'admin' && req.user.role !== 'moderator') {
    return next(new ErrorResponse('Not authorized to update global blocked words', 403));
  }

  if (blockedWord.scope === 'user' && blockedWord.scopeId.toString() !== req.user.id && req.user.role !== 'admin') {
    return next(new ErrorResponse('Not authorized to update this blocked word', 403));
  }

  if (blockedWord.scope === 'stream') {
    // Check if user is stream owner or moderator
    const moderationSettings = await ModerationSettings.findOne({
      ownerType: 'LiveStream',
      owner: blockedWord.scopeId
    });

    if (!moderationSettings) {
      return next(new ErrorResponse('Stream moderation settings not found', 404));
    }

    const isStreamModerator = moderationSettings.moderators.some(
      mod => mod.user.toString() === req.user.id && mod.permissions.canModifySettings
    );

    if (
      !isStreamModerator && 
      moderationSettings.owner.toString() !== req.user.id && 
      req.user.role !== 'admin'
    ) {
      return next(new ErrorResponse('Not authorized to update blocked words for this stream', 403));
    }
  }

  // Update blocked word
  if (severity !== undefined) blockedWord.severity = severity;
  if (category !== undefined) blockedWord.category = category;
  if (action !== undefined) blockedWord.action = action;
  if (replacement !== undefined) blockedWord.replacement = replacement;
  if (exactMatch !== undefined) blockedWord.exactMatch = exactMatch;
  if (isRegex !== undefined) blockedWord.isRegex = isRegex;
  if (isActive !== undefined) blockedWord.isActive = isActive;

  await blockedWord.save();

  res.status(200).json({
    success: true,
    data: blockedWord
  });
});

/**
 * @desc    Delete a blocked word
 * @route   DELETE /api/moderation/blocked-words/:id
 * @access  Private
 */
exports.deleteBlockedWord = asyncHandler(async (req, res, next) => {
  // Find blocked word
  const blockedWord = await BlockedWord.findById(req.params.id);

  if (!blockedWord) {
    return next(new ErrorResponse('Blocked word not found', 404));
  }

  // Check permissions
  if (blockedWord.scope === 'global' && req.user.role !== 'admin' && req.user.role !== 'moderator') {
    return next(new ErrorResponse('Not authorized to delete global blocked words', 403));
  }

  if (blockedWord.scope === 'user' && blockedWord.scopeId.toString() !== req.user.id && req.user.role !== 'admin') {
    return next(new ErrorResponse('Not authorized to delete this blocked word', 403));
  }

  if (blockedWord.scope === 'stream') {
    // Check if user is stream owner or moderator
    const moderationSettings = await ModerationSettings.findOne({
      ownerType: 'LiveStream',
      owner: blockedWord.scopeId
    });

    if (!moderationSettings) {
      return next(new ErrorResponse('Stream moderation settings not found', 404));
    }

    const isStreamModerator = moderationSettings.moderators.some(
      mod => mod.user.toString() === req.user.id && mod.permissions.canModifySettings
    );

    if (
      !isStreamModerator && 
      moderationSettings.owner.toString() !== req.user.id && 
      req.user.role !== 'admin'
    ) {
      return next(new ErrorResponse('Not authorized to delete blocked words for this stream', 403));
    }
  }

  // Delete blocked word
  await blockedWord.remove();

  res.status(200).json({
    success: true,
    data: {}
  });
});
