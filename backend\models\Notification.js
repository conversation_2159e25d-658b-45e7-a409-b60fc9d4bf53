const mongoose = require('mongoose');

const NotificationSchema = new mongoose.Schema({
  recipient: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  sender: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  type: {
    type: String,
    enum: [
      'follow',
      'follow_request',
      'follow_accept',
      'like_post',
      'like_reel',
      'like_comment',
      'like_mood',
      'like_mood_comment',
      'comment_post',
      'comment_reel',
      'comment_mood',
      'reply_comment',
      'reply_mood_comment',
      'mention',
      'tag',
      'story_view',
      'wellness_reminder',
      // Admin & Commerce notifications
      'order_placed',
      'order_confirmed',
      'order_shipped',
      'order_delivered',
      'payment_received',
      'payout_processed',
      'vendor_approved',
      'vendor_banned',
      'promotion_created',
      'message_received',
      'admin_announcement',
      'system_update',
      'security_alert'
    ],
    required: true,
    index: true
  },
  post: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Post',
  },
  reel: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Reel',
  },
  comment: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Comment',
  },
  story: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Story',
  },
  mood: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'UserMood',
  },
  moodComment: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'MoodComment',
  },
  message: {
    type: String,
  },
  title: {
    type: String,
    maxlength: 100
  },
  data: {
    type: mongoose.Schema.Types.Mixed,
    default: {}
  },
  priority: {
    type: String,
    enum: ['low', 'normal', 'high', 'urgent'],
    default: 'normal',
    index: true
  },
  read: {
    type: Boolean,
    default: false,
    index: true
  },
  readAt: {
    type: Date
  },
  actionUrl: {
    type: String
  },
  actionText: {
    type: String
  },
  expiresAt: {
    type: Date
  },
  metadata: {
    source: {
      type: String,
      default: 'system'
    },
    category: {
      type: String,
      enum: ['order', 'payment', 'social', 'system', 'marketing', 'security'],
      default: 'social'
    },
    tags: [{
      type: String
    }]
  },
}, {
  timestamps: true,
});

// Additional indexes for performance
NotificationSchema.index({ recipient: 1, read: 1, createdAt: -1 });
NotificationSchema.index({ recipient: 1, type: 1 });
NotificationSchema.index({ recipient: 1, priority: 1 });
NotificationSchema.index({ createdAt: 1 }, { expireAfterSeconds: 2592000 }); // 30 days TTL

// Virtual for time ago
NotificationSchema.virtual('timeAgo').get(function() {
  const now = new Date();
  const diff = now - this.createdAt;
  const minutes = Math.floor(diff / 60000);
  const hours = Math.floor(diff / 3600000);
  const days = Math.floor(diff / 86400000);

  if (minutes < 1) return 'Just now';
  if (minutes < 60) return `${minutes}m ago`;
  if (hours < 24) return `${hours}h ago`;
  if (days < 7) return `${days}d ago`;
  return this.createdAt.toLocaleDateString();
});

// Instance methods
NotificationSchema.methods.markAsRead = function() {
  this.read = true;
  this.readAt = new Date();
  return this.save();
};

NotificationSchema.methods.isExpired = function() {
  return this.expiresAt && this.expiresAt < new Date();
};

NotificationSchema.methods.getDisplayData = function() {
  return {
    id: this._id,
    type: this.type,
    title: this.title,
    message: this.message,
    read: this.read,
    priority: this.priority,
    timeAgo: this.timeAgo,
    actionUrl: this.actionUrl,
    actionText: this.actionText,
    createdAt: this.createdAt,
    data: this.data
  };
};

// Static methods
NotificationSchema.statics.getUnreadCount = function(userId) {
  return this.countDocuments({ recipient: userId, read: false });
};

NotificationSchema.statics.markAllAsRead = function(userId) {
  return this.updateMany(
    { recipient: userId, read: false },
    { read: true, readAt: new Date() }
  );
};

NotificationSchema.statics.getRecentNotifications = function(userId, limit = 20) {
  return this.find({ recipient: userId })
    .sort({ createdAt: -1 })
    .limit(limit);
};

module.exports = mongoose.model('Notification', NotificationSchema);
