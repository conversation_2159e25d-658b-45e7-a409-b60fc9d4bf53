const express = require('express');
const router = express.Router();
const { protect, optionalAuth } = require('../middleware/auth');
const { upload, processUploadedFiles } = require('../middleware/fileUpload');
const {
  getPosts,
  getPost,
  createPost,
  updatePost,
  deletePost,
  getUserPosts,
  likePost,
  unlikePost,
  getPostLikes,
  addComment,
  getPostComments,
  deleteComment,
  getTrendingPosts,
  getFeedPosts,
  getRelatedPosts,
  viewPost,
} = require('../controllers/postController');

// Public routes
router.get('/', getPosts);
router.get('/trending', getTrendingPosts);
router.get('/feed', optionalAuth, getFeedPosts);
router.get('/related/:id', getRelatedPosts);
router.get('/user/:userId', getUserPosts);
router.get('/:id', getPost);
router.get('/:id/likes', getPostLikes);
router.get('/:id/comments', getPostComments);

// Protected routes
router.use(protect);
router.post('/', upload.array('media', 10), processUploadedFiles, createPost);
router.put('/:id', upload.array('media', 10), processUploadedFiles, updatePost);
router.delete('/:id', deletePost);
router.post('/:id/like', likePost);
router.delete('/:id/like', unlikePost);
router.post('/:id/view', viewPost);
router.post('/:id/comments', addComment);
router.delete('/:id/comments/:commentId', deleteComment);

module.exports = router;
