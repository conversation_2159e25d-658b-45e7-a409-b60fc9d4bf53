const express = require('express');
const router = express.Router();
const { protect } = require('../middleware/auth');
const {
  getComments,
  getComment,
  createComment,
  updateComment,
  deleteComment,
  likeComment,
  unlikeComment,
  getCommentLikes,
  getCommentReplies,
  createCommentReply,
} = require('../controllers/commentController');

// Public routes for getting comments
router.get('/post/:id', (req, res, next) => {
  req.params.type = 'post';
  next();
}, getComments);

router.get('/reel/:id', (req, res, next) => {
  req.params.type = 'reel';
  next();
}, getComments);

// Protected routes
router.use(protect);
router.get('/', getComments);
router.get('/:id', getComment);
router.post('/', createComment);
router.put('/:id', updateComment);
router.delete('/:id', deleteComment);
router.post('/:id/like', likeComment);
router.delete('/:id/like', unlikeComment);
router.get('/:id/likes', getCommentLikes);
router.get('/:id/replies', getCommentReplies);
router.post('/:id/replies', createCommentReply);

module.exports = router;
