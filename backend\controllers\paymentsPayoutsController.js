const Commission = require('../models/Commission');
const PayoutRequest = require('../models/PayoutRequest');
const Currency = require('../models/Currency');
const Vendor = require('../models/Vendor');
const paymentsPayoutsService = require('../services/paymentsPayoutsService');

// @desc    Get vendor balance
// @route   GET /api/payments/balance
// @access  Private (Vendor)
exports.getVendorBalance = async (req, res) => {
  try {
    const { currency = 'USD' } = req.query;

    const vendor = await Vendor.findOne({ user: req.user.id });
    if (!vendor) {
      return res.status(404).json({
        success: false,
        message: 'Vendor profile not found'
      });
    }

    const balance = await paymentsPayoutsService.getVendorBalance(vendor._id, currency);

    // Get pending commissions count
    const pendingCommissions = await Commission.countDocuments({
      vendor: vendor._id,
      payoutStatus: 'pending',
      currency: currency
    });

    res.status(200).json({
      success: true,
      data: {
        balance,
        currency,
        pendingCommissions,
        minimumPayout: paymentsPayoutsService.minimumPayoutAmount
      }
    });
  } catch (error) {
    console.error('Error getting vendor balance:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Get commission analytics
// @route   GET /api/payments/analytics
// @access  Private (Vendor)
exports.getCommissionAnalytics = async (req, res) => {
  try {
    const {
      startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 days ago
      endDate = new Date(),
      currency = 'USD'
    } = req.query;

    const vendor = await Vendor.findOne({ user: req.user.id });
    if (!vendor) {
      return res.status(404).json({
        success: false,
        message: 'Vendor profile not found'
      });
    }

    const analytics = await paymentsPayoutsService.getCommissionAnalytics(
      vendor._id,
      startDate,
      endDate,
      currency
    );

    res.status(200).json({
      success: true,
      data: analytics
    });
  } catch (error) {
    console.error('Error getting commission analytics:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Create payout request
// @route   POST /api/payments/payout-request
// @access  Private (Vendor)
exports.createPayoutRequest = async (req, res) => {
  try {
    const { amount, payoutMethod, currency = 'USD' } = req.body;

    const vendor = await Vendor.findOne({ user: req.user.id });
    if (!vendor) {
      return res.status(404).json({
        success: false,
        message: 'Vendor profile not found'
      });
    }

    // Validate minimum amount
    if (amount < paymentsPayoutsService.minimumPayoutAmount) {
      return res.status(400).json({
        success: false,
        message: `Minimum payout amount is ${paymentsPayoutsService.minimumPayoutAmount}`
      });
    }

    const payoutRequest = await paymentsPayoutsService.createPayoutRequest(
      vendor._id,
      amount,
      payoutMethod,
      currency
    );

    res.status(201).json({
      success: true,
      data: payoutRequest,
      message: 'Payout request created successfully'
    });
  } catch (error) {
    console.error('Error creating payout request:', error);
    res.status(500).json({
      success: false,
      message: error.message || 'Server error'
    });
  }
};

// @desc    Get payout history
// @route   GET /api/payments/payout-history
// @access  Private (Vendor)
exports.getPayoutHistory = async (req, res) => {
  try {
    const { page = 1, limit = 20 } = req.query;

    const vendor = await Vendor.findOne({ user: req.user.id });
    if (!vendor) {
      return res.status(404).json({
        success: false,
        message: 'Vendor profile not found'
      });
    }

    const history = await paymentsPayoutsService.getVendorPayoutHistory(
      vendor._id,
      parseInt(page),
      parseInt(limit)
    );

    res.status(200).json({
      success: true,
      data: history.payouts,
      pagination: history.pagination
    });
  } catch (error) {
    console.error('Error getting payout history:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Get payout request details
// @route   GET /api/payments/payout-request/:id
// @access  Private (Vendor)
exports.getPayoutRequestDetails = async (req, res) => {
  try {
    const payoutRequest = await PayoutRequest.findById(req.params.id)
      .populate('vendor user commissions');

    if (!payoutRequest) {
      return res.status(404).json({
        success: false,
        message: 'Payout request not found'
      });
    }

    // Check if user owns this payout request
    if (payoutRequest.user._id.toString() !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: 'Not authorized to view this payout request'
      });
    }

    res.status(200).json({
      success: true,
      data: payoutRequest
    });
  } catch (error) {
    console.error('Error getting payout request details:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Cancel payout request
// @route   PUT /api/payments/payout-request/:id/cancel
// @access  Private (Vendor)
exports.cancelPayoutRequest = async (req, res) => {
  try {
    const payoutRequest = await PayoutRequest.findById(req.params.id);

    if (!payoutRequest) {
      return res.status(404).json({
        success: false,
        message: 'Payout request not found'
      });
    }

    // Check if user owns this payout request
    if (payoutRequest.user.toString() !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: 'Not authorized to cancel this payout request'
      });
    }

    // Check if cancellable
    if (!['pending', 'approved'].includes(payoutRequest.status)) {
      return res.status(400).json({
        success: false,
        message: 'Payout request cannot be cancelled at this stage'
      });
    }

    await payoutRequest.updateStatus('cancelled', 'Cancelled by vendor', req.user.id);

    // Release commissions back to pending
    await Commission.updateMany(
      { _id: { $in: payoutRequest.commissions } },
      { payoutStatus: 'pending', $unset: { payoutRequest: 1 } }
    );

    res.status(200).json({
      success: true,
      data: payoutRequest,
      message: 'Payout request cancelled successfully'
    });
  } catch (error) {
    console.error('Error cancelling payout request:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Setup automatic payouts
// @route   POST /api/payments/automatic-payouts
// @access  Private (Vendor)
exports.setupAutomaticPayouts = async (req, res) => {
  try {
    const { schedule, minimumAmount } = req.body;

    const vendor = await Vendor.findOne({ user: req.user.id });
    if (!vendor) {
      return res.status(404).json({
        success: false,
        message: 'Vendor profile not found'
      });
    }

    const settings = await paymentsPayoutsService.setupAutomaticPayouts(
      vendor._id,
      schedule,
      minimumAmount
    );

    res.status(200).json({
      success: true,
      data: settings,
      message: 'Automatic payouts configured successfully'
    });
  } catch (error) {
    console.error('Error setting up automatic payouts:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Get supported currencies
// @route   GET /api/payments/currencies
// @access  Public
exports.getSupportedCurrencies = async (req, res) => {
  try {
    const currencies = await Currency.getActiveCurrencies();

    res.status(200).json({
      success: true,
      data: currencies
    });
  } catch (error) {
    console.error('Error getting supported currencies:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Get currency exchange rates
// @route   GET /api/payments/exchange-rates/:fromCurrency
// @access  Public
exports.getExchangeRates = async (req, res) => {
  try {
    const { fromCurrency } = req.params;
    const { toCurrency } = req.query;

    const currency = await Currency.findOne({ code: fromCurrency.toUpperCase() });
    if (!currency) {
      return res.status(404).json({
        success: false,
        message: 'Currency not found'
      });
    }

    if (toCurrency) {
      const rate = currency.getExchangeRate(toCurrency);
      res.status(200).json({
        success: true,
        data: {
          from: fromCurrency.toUpperCase(),
          to: toCurrency.toUpperCase(),
          rate
        }
      });
    } else {
      res.status(200).json({
        success: true,
        data: {
          from: fromCurrency.toUpperCase(),
          rates: currency.exchangeRates
        }
      });
    }
  } catch (error) {
    console.error('Error getting exchange rates:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Get all payout requests (Admin)
// @route   GET /api/payments/admin/payout-requests
// @access  Private (Admin)
exports.getAllPayoutRequests = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      status,
      currency,
      payoutMethod
    } = req.query;

    let query = {};
    if (status) query.status = status;
    if (currency) query.currency = currency;
    if (payoutMethod) query['payoutMethod.type'] = payoutMethod;

    const payoutRequests = await PayoutRequest.find(query)
      .populate('vendor user', 'businessName name email')
      .sort({ createdAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit);

    const total = await PayoutRequest.countDocuments(query);

    res.status(200).json({
      success: true,
      data: payoutRequests,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error getting all payout requests:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Process payout request (Admin)
// @route   PUT /api/payments/admin/payout-requests/:id/process
// @access  Private (Admin)
exports.processPayoutRequest = async (req, res) => {
  try {
    const result = await paymentsPayoutsService.processPayoutRequest(
      req.params.id,
      req.user.id
    );

    res.status(200).json({
      success: true,
      data: result,
      message: 'Payout request processed successfully'
    });
  } catch (error) {
    console.error('Error processing payout request:', error);
    res.status(500).json({
      success: false,
      message: error.message || 'Server error'
    });
  }
};

// @desc    Reject payout request (Admin)
// @route   PUT /api/payments/admin/payout-requests/:id/reject
// @access  Private (Admin)
exports.rejectPayoutRequest = async (req, res) => {
  try {
    const { reason } = req.body;

    const payoutRequest = await PayoutRequest.findById(req.params.id);
    if (!payoutRequest) {
      return res.status(404).json({
        success: false,
        message: 'Payout request not found'
      });
    }

    await payoutRequest.updateStatus('rejected', reason, req.user.id);

    // Release commissions back to pending
    await Commission.updateMany(
      { _id: { $in: payoutRequest.commissions } },
      { payoutStatus: 'pending', $unset: { payoutRequest: 1 } }
    );

    res.status(200).json({
      success: true,
      data: payoutRequest,
      message: 'Payout request rejected'
    });
  } catch (error) {
    console.error('Error rejecting payout request:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Get platform commission analytics (Admin)
// @route   GET /api/payments/admin/analytics
// @access  Private (Admin)
exports.getPlatformAnalytics = async (req, res) => {
  try {
    const {
      startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
      endDate = new Date(),
      currency = 'USD'
    } = req.query;

    const analytics = await Commission.aggregate([
      {
        $match: {
          currency: currency,
          createdAt: {
            $gte: new Date(startDate),
            $lte: new Date(endDate)
          }
        }
      },
      {
        $group: {
          _id: null,
          totalGrossRevenue: { $sum: '$amounts.grossAmount' },
          totalPlatformCommission: { $sum: '$amounts.platformCommission' },
          totalVendorEarnings: { $sum: '$amounts.vendorEarnings' },
          totalTaxes: { $sum: '$amounts.taxes' },
          totalTransactions: { $sum: 1 },
          averageOrderValue: { $avg: '$amounts.grossAmount' },
          averageCommissionRate: { $avg: '$commissionRate.percentage' }
        }
      }
    ]);

    const payoutStats = await PayoutRequest.aggregate([
      {
        $match: {
          currency: currency,
          createdAt: {
            $gte: new Date(startDate),
            $lte: new Date(endDate)
          }
        }
      },
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 },
          totalAmount: { $sum: '$amounts.requestedAmount' }
        }
      }
    ]);

    res.status(200).json({
      success: true,
      data: {
        summary: analytics[0] || {},
        payoutStats
      }
    });
  } catch (error) {
    console.error('Error getting platform analytics:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

module.exports = {
  getVendorBalance: exports.getVendorBalance,
  getCommissionAnalytics: exports.getCommissionAnalytics,
  createPayoutRequest: exports.createPayoutRequest,
  getPayoutHistory: exports.getPayoutHistory,
  getPayoutRequestDetails: exports.getPayoutRequestDetails,
  cancelPayoutRequest: exports.cancelPayoutRequest,
  setupAutomaticPayouts: exports.setupAutomaticPayouts,
  getSupportedCurrencies: exports.getSupportedCurrencies,
  getExchangeRates: exports.getExchangeRates,
  getAllPayoutRequests: exports.getAllPayoutRequests,
  processPayoutRequest: exports.processPayoutRequest,
  rejectPayoutRequest: exports.rejectPayoutRequest,
  getPlatformAnalytics: exports.getPlatformAnalytics
};
