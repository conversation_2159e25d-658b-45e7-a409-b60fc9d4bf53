const mongoose = require('mongoose');

const AudioCueSchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: [true, 'Please provide a name for the audio cue'],
      trim: true,
      maxlength: [50, 'Name cannot be more than 50 characters'],
    },
    description: {
      type: String,
      trim: true,
      maxlength: [200, 'Description cannot be more than 200 characters'],
    },
    url: {
      type: String,
      required: [true, 'Please provide a URL for the audio file'],
    },
    duration: {
      type: Number, // Duration in seconds
      default: 3,
    },
    emotion: {
      type: String,
      enum: ['positive', 'neutral', 'negative', 'complex'],
      required: [true, 'Please specify the emotion category'],
    },
    intensity: {
      type: Number,
      min: 1,
      max: 10,
      default: 5,
    },
    isDefault: {
      type: Boolean,
      default: false,
    },
    isPublic: {
      type: Boolean,
      default: true,
    },
    creator: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
    },
  },
  { timestamps: true }
);

// Add indexes for better query performance
AudioCueSchema.index({ emotion: 1 });
AudioCueSchema.index({ isPublic: 1 });
AudioCueSchema.index({ creator: 1 });

module.exports = mongoose.model('AudioCue', AudioCueSchema);
