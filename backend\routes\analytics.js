const express = require('express');
const router = express.Router();
const { protect, optionalAuth } = require('../middleware/auth');
const {
  getAccountOverview,
  getContentPerformance,
  getAudienceInsights,
  getEngagementMetrics,
  getReachAndImpressions,
  getSalesAnalytics,
  trackVisitor,
  getProductAnalytics,
  getVisitorAnalytics,
  getCartAbandonmentAnalytics,
  getHeatmapData
} = require('../controllers/analyticsController');

// Social media analytics routes (protected)
router.get('/account-overview', protect, getAccountOverview);
router.get('/content-performance', protect, getContentPerformance);
router.get('/audience-insights', protect, getAudienceInsights);
router.get('/engagement', protect, getEngagementMetrics);
router.get('/reach-impressions', protect, getReachAndImpressions);

// E-commerce analytics routes (protected)
router.get('/sales', protect, getSalesAnalytics);
router.get('/products', protect, getProductAnalytics);
router.get('/visitors', protect, getVisitorAnalytics);
router.get('/cart-abandonment', protect, getCartAbandonmentAnalytics);
router.get('/heatmap', protect, getHeatmapData);

// Tracking routes (public with optional auth)
router.post('/track', optionalAuth, trackVisitor);

module.exports = router;
