const mongoose = require('mongoose');

const StorySchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  media: {
    url: {
      type: String,
      required: true,
    },
    type: {
      type: String,
      enum: ['image', 'video'],
      required: true,
    },
    publicId: {
      type: String,
      required: true,
    },
  },
  caption: {
    type: String,
    maxlength: [100, 'Caption cannot be more than 100 characters'],
  },
  emotions: [
    {
      emotion: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Emotion',
        required: true,
      },
      intensity: {
        type: Number,
        min: 1,
        max: 10,
        default: 5,
      },
      position: {
        x: {
          type: Number,
          min: 0,
          max: 100,
          default: 50,
        },
        y: {
          type: Number,
          min: 0,
          max: 100,
          default: 50,
        },
      },
      audio: {
        enabled: {
          type: Boolean,
          default: false,
        },
        cue: {
          type: mongoose.Schema.Types.ObjectId,
          ref: 'AudioCue',
        },
        volume: {
          type: Number,
          min: 0,
          max: 1,
          default: 0.5,
        },
        playOnView: {
          type: Boolean,
          default: true,
        },
      },
      visualEffect: {
        enabled: {
          type: Boolean,
          default: false,
        },
        type: {
          type: String,
          enum: ['pulse', 'glow', 'particles', 'color', 'none'],
          default: 'none',
        },
        intensity: {
          type: Number,
          min: 1,
          max: 10,
          default: 5,
        },
      },
    },
  ],
  arEffects: [
    {
      type: {
        type: String,
        enum: ['filter', 'object', 'background', 'animation'],
        required: true,
      },
      effectId: {
        type: String,
        required: true,
      },
      position: {
        x: {
          type: Number,
          default: 50,
        },
        y: {
          type: Number,
          default: 50,
        },
        z: {
          type: Number,
          default: 0,
        },
      },
      scale: {
        x: {
          type: Number,
          default: 1,
        },
        y: {
          type: Number,
          default: 1,
        },
        z: {
          type: Number,
          default: 1,
        },
      },
      rotation: {
        x: {
          type: Number,
          default: 0,
        },
        y: {
          type: Number,
          default: 0,
        },
        z: {
          type: Number,
          default: 0,
        },
      },
    },
  ],
  viewers: [
    {
      user: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
      },
      viewedAt: {
        type: Date,
        default: Date.now,
      },
    },
  ],
  expiresAt: {
    type: Date,
    default: function() {
      // Stories expire after 24 hours
      const now = new Date();
      return new Date(now.getTime() + 24 * 60 * 60 * 1000);
    },
  },
}, {
  timestamps: true,
});

// Index to automatically delete expired stories
StorySchema.index({ expiresAt: 1 }, { expireAfterSeconds: 0 });

module.exports = mongoose.model('Story', StorySchema);
