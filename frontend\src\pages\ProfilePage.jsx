import React, { useState, useEffect } from 'react'
import {
  Container,
  Typo<PERSON>,
  Box,
  Card,
  CardContent,
  Avatar,
  Button,
  Grid,
  Tabs,
  Tab,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  CircularProgress,
  Chip,
  CardMedia,
  CardActions,
  Divider,
  Paper,
  Badge
} from '@mui/material'
import {
  Edit,
  Settings,
  LocationOn,
  CalendarToday,
  Link as LinkIcon,
  Favorite,
  FavoriteBorder,
  Comment,
  Share,
  PhotoCamera,
  PersonAdd,
  PersonRemove,
  Message,
  MoreVert
} from '@mui/icons-material'
import { useSnackbar } from 'notistack'
import { useParams, useNavigate } from 'react-router-dom'
import { useAuth } from '../context/AuthContext'
import axios from '../utils/fixedAxios'

const ProfilePage = () => {
  const [profile, setProfile] = useState(null)
  const [posts, setPosts] = useState([])
  const [followers, setFollowers] = useState([])
  const [following, setFollowing] = useState([])
  const [activeTab, setActiveTab] = useState(0)
  const [loading, setLoading] = useState(true)
  const [editDialogOpen, setEditDialogOpen] = useState(false)
  const [isFollowing, setIsFollowing] = useState(false)
  const [followLoading, setFollowLoading] = useState(false)
  const [editForm, setEditForm] = useState({
    name: '',
    bio: '',
    location: '',
    website: ''
  })

  const { userId } = useParams()
  const { user } = useAuth()
  const { enqueueSnackbar } = useSnackbar()
  const navigate = useNavigate()

  const isOwnProfile = !userId || userId === user?.id

  useEffect(() => {
    fetchProfile()
  }, [userId])

  useEffect(() => {
    if (activeTab === 0) {
      fetchPosts()
    } else if (activeTab === 1) {
      fetchFollowers()
    } else if (activeTab === 2) {
      fetchFollowing()
    }
  }, [activeTab, profile])

  const fetchProfile = async () => {
    try {
      setLoading(true)
      const targetUserId = userId || user?.id

      // If it's own profile, use current user data
      if (isOwnProfile && user) {
        setProfile(user)
        setEditForm({
          name: user.name || '',
          bio: user.bio || '',
          location: user.location || '',
          website: user.website || ''
        })
      } else {
        const response = await axios.get(`/api/users/${targetUserId}`)
        const profileData = response.data.data || response.data.user

        setProfile(profileData)
        setEditForm({
          name: profileData.name || '',
          bio: profileData.bio || '',
          location: profileData.location || '',
          website: profileData.website || ''
        })

        // Check if following (only for other users' profiles)
        checkFollowStatus(targetUserId)
      }

    } catch (error) {
      console.error('Error fetching profile:', error)
      enqueueSnackbar('Failed to load profile', { variant: 'error' })
    } finally {
      setLoading(false)
    }
  }

  const checkFollowStatus = async (targetUserId) => {
    try {
      const response = await axios.get(`/api/follow/status/${targetUserId}`)
      setIsFollowing(response.data.isFollowing)
    } catch (error) {
      console.error('Error checking follow status:', error)
    }
  }

  const fetchPosts = async () => {
    try {
      const targetUserId = userId || user?.id
      const response = await axios.get(`/api/posts/user/${targetUserId}`)
      setPosts(response.data.data || response.data.posts || [])
    } catch (error) {
      console.error('Error fetching posts:', error)
    }
  }

  const fetchFollowers = async () => {
    try {
      const targetUserId = userId || user?.id
      const response = await axios.get(`/api/follow/${targetUserId}/followers`)
      setFollowers(response.data.data || response.data.followers || [])
    } catch (error) {
      console.error('Error fetching followers:', error)
    }
  }

  const fetchFollowing = async () => {
    try {
      const targetUserId = userId || user?.id
      const response = await axios.get(`/api/follow/${targetUserId}/following`)
      setFollowing(response.data.data || response.data.following || [])
    } catch (error) {
      console.error('Error fetching following:', error)
    }
  }

  const handleFollow = async () => {
    if (!userId) return

    try {
      setFollowLoading(true)

      if (isFollowing) {
        await axios.delete(`/api/follow/${userId}`)
        setIsFollowing(false)
        enqueueSnackbar('Unfollowed successfully', { variant: 'info' })
      } else {
        await axios.post(`/api/follow/${userId}`)
        setIsFollowing(true)
        enqueueSnackbar('Following successfully', { variant: 'success' })
      }

      // Update follower count
      setProfile(prev => ({
        ...prev,
        followersCount: (prev.followersCount || 0) + (isFollowing ? -1 : 1)
      }))

    } catch (error) {
      console.error('Error following/unfollowing:', error)
      enqueueSnackbar('Failed to update follow status', { variant: 'error' })
    } finally {
      setFollowLoading(false)
    }
  }

  const handleEditProfile = async () => {
    try {
      const response = await axios.put('/api/users/profile', editForm)
      setProfile(response.data.data || response.data.user)
      setEditDialogOpen(false)
      enqueueSnackbar('Profile updated successfully', { variant: 'success' })
    } catch (error) {
      console.error('Error updating profile:', error)
      enqueueSnackbar('Failed to update profile', { variant: 'error' })
    }
  }

  const handleAvatarUpload = async (event) => {
    const file = event.target.files[0]
    if (!file) return

    const formData = new FormData()
    formData.append('avatar', file)

    try {
      const response = await axios.post('/api/users/avatar', formData, {
        headers: { 'Content-Type': 'multipart/form-data' }
      })
      setProfile(prev => ({ ...prev, avatar: response.data.avatarUrl }))
      enqueueSnackbar('Avatar updated successfully', { variant: 'success' })
    } catch (error) {
      console.error('Error uploading avatar:', error)
      enqueueSnackbar('Failed to update avatar', { variant: 'error' })
    }
  }

  const tabs = ['Posts', 'Followers', 'Following']

  if (loading) {
    return (
      <Container maxWidth="md" sx={{ py: 3 }}>
        <Box display="flex" justifyContent="center" py={6}>
          <CircularProgress />
        </Box>
      </Container>
    )
  }

  if (!profile) {
    return (
      <Container maxWidth="md" sx={{ py: 3 }}>
        <Box textAlign="center" py={6}>
          <Typography variant="h6" gutterBottom>
            Profile not found
          </Typography>
          <Button variant="contained" onClick={() => navigate('/')}>
            Go Home
          </Button>
        </Box>
      </Container>
    )
  }

  return (
    <Container maxWidth="md" sx={{ py: 3 }}>
      {/* Profile Header */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 3 }}>
            {/* Avatar */}
            <Box sx={{ position: 'relative' }}>
              <Avatar
                src={profile.avatar}
                sx={{ width: 120, height: 120 }}
              >
                {profile.name?.charAt(0)?.toUpperCase()}
              </Avatar>
              {isOwnProfile && (
                <IconButton
                  sx={{
                    position: 'absolute',
                    bottom: 0,
                    right: 0,
                    bgcolor: 'primary.main',
                    color: 'white',
                    '&:hover': { bgcolor: 'primary.dark' }
                  }}
                  component="label"
                >
                  <PhotoCamera />
                  <input
                    type="file"
                    hidden
                    accept="image/*"
                    onChange={handleAvatarUpload}
                  />
                </IconButton>
              )}
            </Box>

            {/* Profile Info */}
            <Box sx={{ flex: 1 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                <Typography variant="h4" fontWeight="bold">
                  {profile.name}
                </Typography>

                {isOwnProfile ? (
                  <Box>
                    <Button
                      variant="outlined"
                      startIcon={<Edit />}
                      onClick={() => setEditDialogOpen(true)}
                      sx={{ mr: 1 }}
                    >
                      Edit Profile
                    </Button>
                    <IconButton onClick={() => navigate('/settings')}>
                      <Settings />
                    </IconButton>
                  </Box>
                ) : (
                  <Box>
                    <Button
                      variant={isFollowing ? "outlined" : "contained"}
                      startIcon={isFollowing ? <PersonRemove /> : <PersonAdd />}
                      onClick={handleFollow}
                      disabled={followLoading}
                      sx={{ mr: 1 }}
                    >
                      {followLoading ? <CircularProgress size={20} /> : (isFollowing ? 'Unfollow' : 'Follow')}
                    </Button>
                    <Button
                      variant="outlined"
                      startIcon={<Message />}
                      onClick={() => navigate(`/messages?user=${profile._id}`)}
                      sx={{ mr: 1 }}
                    >
                      Message
                    </Button>
                    <IconButton>
                      <MoreVert />
                    </IconButton>
                  </Box>
                )}
              </Box>

              <Typography variant="body1" color="text.secondary" gutterBottom>
                @{profile.username}
              </Typography>

              {profile.bio && (
                <Typography variant="body1" sx={{ mb: 2 }}>
                  {profile.bio}
                </Typography>
              )}

              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2, mb: 2 }}>
                {profile.location && (
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                    <LocationOn fontSize="small" color="action" />
                    <Typography variant="body2" color="text.secondary">
                      {profile.location}
                    </Typography>
                  </Box>
                )}

                {profile.website && (
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                    <LinkIcon fontSize="small" color="action" />
                    <Typography
                      variant="body2"
                      color="primary"
                      component="a"
                      href={profile.website}
                      target="_blank"
                      rel="noopener noreferrer"
                      sx={{ textDecoration: 'none' }}
                    >
                      {profile.website}
                    </Typography>
                  </Box>
                )}

                <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                  <CalendarToday fontSize="small" color="action" />
                  <Typography variant="body2" color="text.secondary">
                    Joined {new Date(profile.createdAt || Date.now()).toLocaleDateString('en-US', { month: 'long', year: 'numeric' })}
                  </Typography>
                </Box>
              </Box>

              {/* Stats */}
              <Box sx={{ display: 'flex', gap: 3 }}>
                <Typography variant="body2">
                  <strong>{posts.length}</strong> Posts
                </Typography>
                <Typography variant="body2" sx={{ cursor: 'pointer' }} onClick={() => setActiveTab(1)}>
                  <strong>{followers.length}</strong> Followers
                </Typography>
                <Typography variant="body2" sx={{ cursor: 'pointer' }} onClick={() => setActiveTab(2)}>
                  <strong>{following.length}</strong> Following
                </Typography>
              </Box>
            </Box>
          </Box>
        </CardContent>
      </Card>

      {/* Tabs */}
      <Tabs value={activeTab} onChange={(e, newValue) => setActiveTab(newValue)} sx={{ mb: 3 }}>
        {tabs.map((tab, index) => (
          <Tab key={index} label={tab} />
        ))}
      </Tabs>

      {/* Tab Content */}
      {activeTab === 0 && (
        <Box>
          {posts.length === 0 ? (
            <Box textAlign="center" py={6}>
              <Typography variant="h6" gutterBottom>
                No posts yet
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {isOwnProfile ? 'Share your first post!' : 'This user hasn\'t posted anything yet.'}
              </Typography>
              {isOwnProfile && (
                <Button variant="contained" onClick={() => navigate('/create')} sx={{ mt: 2 }}>
                  Create Post
                </Button>
              )}
            </Box>
          ) : (
            <Grid container spacing={2}>
              {posts.map((post) => (
                <Grid item xs={12} key={post._id}>
                  <Card>
                    <CardContent>
                      <Box display="flex" alignItems="center" mb={2}>
                        <Avatar src={post.user?.avatar} sx={{ mr: 2 }}>
                          {post.user?.name?.charAt(0)?.toUpperCase()}
                        </Avatar>
                        <Box>
                          <Typography variant="subtitle1" fontWeight="medium">
                            {post.user?.name}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {new Date(post.createdAt).toLocaleDateString()}
                          </Typography>
                        </Box>
                      </Box>

                      {post.caption && (
                        <Typography variant="body1" mb={2}>
                          {post.caption}
                        </Typography>
                      )}

                      {post.media && post.media.length > 0 && (
                        <CardMedia
                          component="img"
                          image={post.media[0].url}
                          alt="Post media"
                          sx={{ borderRadius: 1, maxHeight: 400, objectFit: 'cover' }}
                        />
                      )}
                    </CardContent>

                    <CardActions>
                      <IconButton>
                        <FavoriteBorder />
                      </IconButton>
                      <Typography variant="caption">
                        {post.likesCount || 0}
                      </Typography>

                      <IconButton>
                        <Comment />
                      </IconButton>
                      <Typography variant="caption">
                        {post.commentsCount || 0}
                      </Typography>

                      <IconButton>
                        <Share />
                      </IconButton>
                    </CardActions>
                  </Card>
                </Grid>
              ))}
            </Grid>
          )}
        </Box>
      )}

      {activeTab === 1 && (
        <Box>
          {followers.length === 0 ? (
            <Box textAlign="center" py={6}>
              <Typography variant="h6" gutterBottom>
                No followers yet
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {isOwnProfile ? 'Start connecting with people!' : 'This user has no followers yet.'}
              </Typography>
            </Box>
          ) : (
            <Grid container spacing={2}>
              {followers.map((follower) => (
                <Grid item xs={12} sm={6} key={follower._id}>
                  <Card>
                    <CardContent>
                      <Box display="flex" alignItems="center" justifyContent="space-between">
                        <Box display="flex" alignItems="center">
                          <Avatar src={follower.avatar} sx={{ mr: 2 }}>
                            {follower.name?.charAt(0)?.toUpperCase()}
                          </Avatar>
                          <Box>
                            <Typography variant="subtitle1" fontWeight="medium">
                              {follower.name}
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                              @{follower.username}
                            </Typography>
                          </Box>
                        </Box>
                        <Button
                          variant="outlined"
                          size="small"
                          onClick={() => navigate(`/profile/${follower._id}`)}
                        >
                          View
                        </Button>
                      </Box>
                    </CardContent>
                  </Card>
                </Grid>
              ))}
            </Grid>
          )}
        </Box>
      )}

      {activeTab === 2 && (
        <Box>
          {following.length === 0 ? (
            <Box textAlign="center" py={6}>
              <Typography variant="h6" gutterBottom>
                Not following anyone yet
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {isOwnProfile ? 'Discover people to follow!' : 'This user isn\'t following anyone yet.'}
              </Typography>
            </Box>
          ) : (
            <Grid container spacing={2}>
              {following.map((followedUser) => (
                <Grid item xs={12} sm={6} key={followedUser._id}>
                  <Card>
                    <CardContent>
                      <Box display="flex" alignItems="center" justifyContent="space-between">
                        <Box display="flex" alignItems="center">
                          <Avatar src={followedUser.avatar} sx={{ mr: 2 }}>
                            {followedUser.name?.charAt(0)?.toUpperCase()}
                          </Avatar>
                          <Box>
                            <Typography variant="subtitle1" fontWeight="medium">
                              {followedUser.name}
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                              @{followedUser.username}
                            </Typography>
                          </Box>
                        </Box>
                        <Button
                          variant="outlined"
                          size="small"
                          onClick={() => navigate(`/profile/${followedUser._id}`)}
                        >
                          View
                        </Button>
                      </Box>
                    </CardContent>
                  </Card>
                </Grid>
              ))}
            </Grid>
          )}
        </Box>
      )}

      {/* Edit Profile Dialog */}
      <Dialog open={editDialogOpen} onClose={() => setEditDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Edit Profile</DialogTitle>
        <DialogContent>
          <TextField
            fullWidth
            label="Name"
            value={editForm.name}
            onChange={(e) => setEditForm({ ...editForm, name: e.target.value })}
            margin="normal"
          />
          <TextField
            fullWidth
            label="Bio"
            value={editForm.bio}
            onChange={(e) => setEditForm({ ...editForm, bio: e.target.value })}
            margin="normal"
            multiline
            rows={3}
          />
          <TextField
            fullWidth
            label="Location"
            value={editForm.location}
            onChange={(e) => setEditForm({ ...editForm, location: e.target.value })}
            margin="normal"
          />
          <TextField
            fullWidth
            label="Website"
            value={editForm.website}
            onChange={(e) => setEditForm({ ...editForm, website: e.target.value })}
            margin="normal"
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setEditDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleEditProfile} variant="contained">Save</Button>
        </DialogActions>
      </Dialog>
    </Container>
  )
}

export default ProfilePage
