let stripe = null;

try {
  if (process.env.STRIPE_SECRET_KEY) {
    stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);
  }
} catch (error) {
  console.warn('Stripe not configured:', error.message);
}

class PaymentGatewayService {
  constructor() {
    this.stripe = stripe;
    this.isAvailable = !!stripe;
  }

  _checkAvailability() {
    if (!this.isAvailable) {
      throw new Error('Payment gateway not available - Stripe not configured');
    }
  }

  // Check if payment gateway is available
  isPaymentGatewayAvailable() {
    return this.isAvailable;
  }

  // Create payment intent with buyer protection
  async createPaymentIntent(amount, currency = 'usd', metadata = {}) {
    try {
      this._checkAvailability();
      const paymentIntent = await this.stripe.paymentIntents.create({
        amount: Math.round(amount * 100), // Convert to cents
        currency,
        metadata: {
          ...metadata,
          buyer_protection: 'enabled'
        },
        capture_method: 'manual' // Hold funds for buyer protection
      });

      return {
        success: true,
        data: {
          clientSecret: paymentIntent.client_secret,
          paymentIntentId: paymentIntent.id,
          amount: paymentIntent.amount,
          currency: paymentIntent.currency,
          status: paymentIntent.status
        }
      };
    } catch (error) {
      console.error('Error creating payment intent:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // Capture payment (release from escrow)
  async capturePayment(paymentIntentId, amountToCapture = null) {
    try {
      const captureData = {};
      if (amountToCapture) {
        captureData.amount_to_capture = Math.round(amountToCapture * 100);
      }

      const paymentIntent = await this.stripe.paymentIntents.capture(
        paymentIntentId,
        captureData
      );

      return {
        success: true,
        data: {
          paymentIntentId: paymentIntent.id,
          amount: paymentIntent.amount,
          status: paymentIntent.status,
          charges: paymentIntent.charges.data
        }
      };
    } catch (error) {
      console.error('Error capturing payment:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // Create refund
  async createRefund(paymentIntentId, amount = null, reason = 'requested_by_customer') {
    try {
      const refundData = {
        payment_intent: paymentIntentId,
        reason
      };

      if (amount) {
        refundData.amount = Math.round(amount * 100);
      }

      const refund = await this.stripe.refunds.create(refundData);

      return {
        success: true,
        data: {
          refundId: refund.id,
          amount: refund.amount,
          status: refund.status,
          reason: refund.reason
        }
      };
    } catch (error) {
      console.error('Error creating refund:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // Hold payment in escrow
  async holdPaymentInEscrow(paymentIntentId, holdUntil) {
    try {
      const paymentIntent = await this.stripe.paymentIntents.update(
        paymentIntentId,
        {
          metadata: {
            escrow_hold: 'true',
            hold_until: holdUntil.toISOString()
          }
        }
      );

      return {
        success: true,
        data: {
          paymentIntentId: paymentIntent.id,
          status: paymentIntent.status,
          escrowHold: true
        }
      };
    } catch (error) {
      console.error('Error holding payment in escrow:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // Release payment from escrow
  async releaseFromEscrow(paymentIntentId, releaseTo = 'vendor') {
    try {
      if (releaseTo === 'vendor') {
        // Capture the payment to release to vendor
        return await this.capturePayment(paymentIntentId);
      } else if (releaseTo === 'buyer') {
        // Refund to buyer
        return await this.createRefund(paymentIntentId);
      } else {
        throw new Error('Invalid release target');
      }
    } catch (error) {
      console.error('Error releasing from escrow:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // Get payment status
  async getPaymentStatus(paymentIntentId) {
    try {
      const paymentIntent = await this.stripe.paymentIntents.retrieve(paymentIntentId);

      return {
        success: true,
        data: {
          paymentIntentId: paymentIntent.id,
          status: paymentIntent.status,
          amount: paymentIntent.amount,
          currency: paymentIntent.currency,
          metadata: paymentIntent.metadata,
          charges: paymentIntent.charges.data
        }
      };
    } catch (error) {
      console.error('Error getting payment status:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // Create customer for recurring payments
  async createCustomer(email, name, metadata = {}) {
    try {
      const customer = await this.stripe.customers.create({
        email,
        name,
        metadata
      });

      return {
        success: true,
        data: {
          customerId: customer.id,
          email: customer.email,
          name: customer.name
        }
      };
    } catch (error) {
      console.error('Error creating customer:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // Setup payment method for future use
  async setupPaymentMethod(customerId, paymentMethodId) {
    try {
      await this.stripe.paymentMethods.attach(paymentMethodId, {
        customer: customerId
      });

      return {
        success: true,
        data: {
          paymentMethodId,
          customerId
        }
      };
    } catch (error) {
      console.error('Error setting up payment method:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // Process dispute chargeback
  async handleDispute(chargeId, evidence = {}) {
    try {
      const dispute = await this.stripe.disputes.update(chargeId, {
        evidence
      });

      return {
        success: true,
        data: {
          disputeId: dispute.id,
          status: dispute.status,
          reason: dispute.reason,
          amount: dispute.amount
        }
      };
    } catch (error) {
      console.error('Error handling dispute:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // Validate payment for PCI compliance
  async validatePayment(paymentIntentId) {
    try {
      const paymentIntent = await this.stripe.paymentIntents.retrieve(paymentIntentId);

      const validation = {
        isValid: paymentIntent.status === 'succeeded',
        pciCompliant: true, // Stripe handles PCI compliance
        fraudCheck: {
          passed: true,
          riskLevel: 'low'
        },
        verification: {
          cvcCheck: paymentIntent.charges.data[0]?.payment_method_details?.card?.checks?.cvc_check,
          addressCheck: paymentIntent.charges.data[0]?.payment_method_details?.card?.checks?.address_line1_check,
          postalCodeCheck: paymentIntent.charges.data[0]?.payment_method_details?.card?.checks?.address_postal_code_check
        }
      };

      return {
        success: true,
        data: validation
      };
    } catch (error) {
      console.error('Error validating payment:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // Calculate platform fees
  calculatePlatformFee(amount, feePercentage = 2.9, fixedFee = 0.30) {
    const percentageFee = (amount * feePercentage) / 100;
    const totalFee = percentageFee + fixedFee;
    const netAmount = amount - totalFee;

    return {
      grossAmount: amount,
      platformFee: totalFee,
      netAmount,
      breakdown: {
        percentageFee,
        fixedFee
      }
    };
  }

  // Create connected account for vendors
  async createConnectedAccount(email, businessType = 'individual', country = 'US') {
    try {
      const account = await this.stripe.accounts.create({
        type: 'express',
        email,
        business_type: businessType,
        country,
        capabilities: {
          card_payments: { requested: true },
          transfers: { requested: true }
        }
      });

      return {
        success: true,
        data: {
          accountId: account.id,
          email: account.email,
          type: account.type,
          country: account.country
        }
      };
    } catch (error) {
      console.error('Error creating connected account:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // Transfer funds to vendor
  async transferToVendor(amount, vendorAccountId, metadata = {}) {
    try {
      const transfer = await this.stripe.transfers.create({
        amount: Math.round(amount * 100),
        currency: 'usd',
        destination: vendorAccountId,
        metadata
      });

      return {
        success: true,
        data: {
          transferId: transfer.id,
          amount: transfer.amount,
          destination: transfer.destination,
          status: 'pending'
        }
      };
    } catch (error) {
      console.error('Error transferring to vendor:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }
}

module.exports = new PaymentGatewayService();
