const asyncHandler = require('../middleware/async');
const ErrorResponse = require('../utils/errorResponse');
const User = require('../models/User');

/**
 * @desc    Get user theme preference
 * @route   GET /api/users/me/theme
 * @access  Private
 */
exports.getThemePreference = asyncHandler(async (req, res, next) => {
  const user = await User.findById(req.user.id);

  if (!user) {
    return next(new ErrorResponse('User not found', 404));
  }

  // Return user's theme preference or default
  const themePreference = user.themePreference || {
    mode: 'light',
    updatedAt: new Date()
  };

  res.status(200).json({
    success: true,
    data: themePreference
  });
});

/**
 * @desc    Update user theme preference
 * @route   PUT /api/users/me/theme
 * @access  Private
 */
exports.updateThemePreference = asyncHandler(async (req, res, next) => {
  const { mode } = req.body;

  // Validate mode
  if (mode && !['light', 'dark', 'system'].includes(mode)) {
    return next(new ErrorResponse('Invalid theme mode. Must be "light", "dark", or "system"', 400));
  }

  const user = await User.findById(req.user.id);

  if (!user) {
    return next(new ErrorResponse('User not found', 404));
  }

  // Update theme preference
  user.themePreference = {
    mode: mode || user.themePreference?.mode || 'light',
    updatedAt: new Date()
  };

  await user.save();

  res.status(200).json({
    success: true,
    data: user.themePreference
  });
});
