const express = require('express');
const router = express.Router();
const { protect } = require('../middleware/auth');
const {
  getWellnessSettings,
  updateContentFilters,
  updateTimeLimit,
  updateHideMetrics,
  updateWellnessReminders,
} = require('../controllers/wellnessController');

// All routes require authentication
router.use(protect);

// Get wellness settings
router.get('/', getWellnessSettings);

// Update content filters
router.put('/content-filters', updateContentFilters);

// Update time limit settings
router.put('/time-limit', updateTimeLimit);

// Update hide metrics setting
router.put('/hide-metrics', updateHideMetrics);

// Update wellness reminders settings
router.put('/reminders', updateWellnessReminders);

module.exports = router;
