const mongoose = require('mongoose');

const DisputeSchema = new mongoose.Schema({
  disputeId: {
    type: String,
    unique: true,
    required: true
  },
  order: {
    type: mongoose.Schema.ObjectId,
    ref: 'Order',
    required: true
  },
  initiator: {
    type: mongoose.Schema.ObjectId,
    ref: 'User',
    required: true
  },
  respondent: {
    type: mongoose.Schema.ObjectId,
    ref: 'User',
    required: true
  },
  vendor: {
    type: mongoose.Schema.ObjectId,
    ref: 'Vendor'
  },
  type: {
    type: String,
    enum: ['order_dispute', 'payment_dispute', 'quality_dispute', 'shipping_dispute', 'refund_dispute', 'service_dispute'],
    required: true
  },
  category: {
    type: String,
    enum: ['item_not_received', 'item_not_as_described', 'damaged_item', 'wrong_item', 'unauthorized_charge', 'billing_error', 'service_issue', 'policy_violation'],
    required: true
  },
  priority: {
    type: String,
    enum: ['low', 'medium', 'high', 'urgent'],
    default: 'medium'
  },
  status: {
    type: String,
    enum: ['open', 'in_review', 'awaiting_response', 'escalated', 'resolved', 'closed', 'cancelled'],
    default: 'open'
  },
  details: {
    title: {
      type: String,
      required: true,
      maxlength: 200
    },
    description: {
      type: String,
      required: true,
      maxlength: 2000
    },
    amount: {
      type: Number,
      min: 0
    },
    currency: {
      type: String,
      default: 'USD'
    },
    requestedResolution: {
      type: String,
      enum: ['full_refund', 'partial_refund', 'replacement', 'repair', 'store_credit', 'apology', 'policy_change', 'account_action'],
      required: true
    }
  },
  evidence: {
    initiator: [{
      type: {
        type: String,
        enum: ['image', 'document', 'video', 'audio', 'screenshot', 'receipt']
      },
      url: String,
      description: String,
      uploadedAt: {
        type: Date,
        default: Date.now
      }
    }],
    respondent: [{
      type: {
        type: String,
        enum: ['image', 'document', 'video', 'audio', 'screenshot', 'receipt']
      },
      url: String,
      description: String,
      uploadedAt: {
        type: Date,
        default: Date.now
      }
    }]
  },
  communication: [{
    sender: {
      type: mongoose.Schema.ObjectId,
      ref: 'User',
      required: true
    },
    senderType: {
      type: String,
      enum: ['buyer', 'vendor', 'admin', 'mediator'],
      required: true
    },
    message: {
      type: String,
      required: true,
      maxlength: 1000
    },
    attachments: [String],
    isInternal: {
      type: Boolean,
      default: false
    },
    timestamp: {
      type: Date,
      default: Date.now
    },
    readBy: [{
      user: {
        type: mongoose.Schema.ObjectId,
        ref: 'User'
      },
      readAt: {
        type: Date,
        default: Date.now
      }
    }]
  }],
  mediation: {
    isRequired: {
      type: Boolean,
      default: false
    },
    mediator: {
      type: mongoose.Schema.ObjectId,
      ref: 'User'
    },
    assignedAt: Date,
    scheduledSession: {
      date: Date,
      duration: Number, // in minutes
      platform: String, // zoom, teams, etc.
      meetingLink: String
    },
    outcome: {
      decision: String,
      reasoning: String,
      decidedAt: Date
    }
  },
  escalation: {
    level: {
      type: Number,
      default: 0,
      min: 0,
      max: 3
    },
    escalatedAt: Date,
    escalatedBy: {
      type: mongoose.Schema.ObjectId,
      ref: 'User'
    },
    reason: String,
    assignedTo: {
      type: mongoose.Schema.ObjectId,
      ref: 'User'
    }
  },
  resolution: {
    resolvedAt: Date,
    resolvedBy: {
      type: mongoose.Schema.ObjectId,
      ref: 'User'
    },
    resolutionType: {
      type: String,
      enum: ['agreement', 'mediation', 'admin_decision', 'automatic', 'withdrawal']
    },
    outcome: {
      action: String,
      amount: Number,
      description: String,
      agreedByBuyer: Boolean,
      agreedByVendor: Boolean
    },
    followUpRequired: {
      type: Boolean,
      default: false
    },
    followUpDate: Date
  },
  timeline: [{
    action: String,
    description: String,
    performedBy: {
      type: mongoose.Schema.ObjectId,
      ref: 'User'
    },
    timestamp: {
      type: Date,
      default: Date.now
    },
    metadata: mongoose.Schema.Types.Mixed
  }],
  sla: {
    responseTime: {
      type: Number, // in hours
      default: 24
    },
    resolutionTime: {
      type: Number, // in hours
      default: 72
    },
    firstResponseAt: Date,
    lastResponseAt: Date,
    isOverdue: {
      type: Boolean,
      default: false
    }
  },
  satisfaction: {
    buyerRating: {
      type: Number,
      min: 1,
      max: 5
    },
    vendorRating: {
      type: Number,
      min: 1,
      max: 5
    },
    feedback: {
      buyer: String,
      vendor: String
    },
    submittedAt: Date
  },
  compliance: {
    gdprProcessed: {
      type: Boolean,
      default: false
    },
    dataRetentionUntil: Date,
    sensitiveDataRedacted: {
      type: Boolean,
      default: false
    }
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Generate unique dispute ID
DisputeSchema.pre('save', function(next) {
  if (this.isNew && !this.disputeId) {
    this.disputeId = `DSP-${Date.now()}-${Math.random().toString(36).substr(2, 6).toUpperCase()}`;
  }
  this.updatedAt = Date.now();
  next();
});

// Add timeline entry
DisputeSchema.methods.addTimelineEntry = function(action, description, performedBy, metadata = {}) {
  this.timeline.push({
    action,
    description,
    performedBy,
    metadata
  });
  return this.save();
};

// Add communication message
DisputeSchema.methods.addMessage = function(sender, senderType, message, attachments = [], isInternal = false) {
  this.communication.push({
    sender,
    senderType,
    message,
    attachments,
    isInternal
  });
  
  this.sla.lastResponseAt = new Date();
  if (!this.sla.firstResponseAt) {
    this.sla.firstResponseAt = new Date();
  }
  
  return this.addTimelineEntry(
    'message_added',
    `New message from ${senderType}`,
    sender,
    { messageLength: message.length, hasAttachments: attachments.length > 0 }
  );
};

// Escalate dispute
DisputeSchema.methods.escalate = function(escalatedBy, reason, assignedTo) {
  this.escalation.level += 1;
  this.escalation.escalatedAt = new Date();
  this.escalation.escalatedBy = escalatedBy;
  this.escalation.reason = reason;
  this.escalation.assignedTo = assignedTo;
  this.status = 'escalated';
  
  return this.addTimelineEntry(
    'dispute_escalated',
    `Dispute escalated to level ${this.escalation.level}`,
    escalatedBy,
    { level: this.escalation.level, reason }
  );
};

// Assign mediator
DisputeSchema.methods.assignMediator = function(mediator, assignedBy) {
  this.mediation.isRequired = true;
  this.mediation.mediator = mediator;
  this.mediation.assignedAt = new Date();
  this.status = 'in_review';
  
  return this.addTimelineEntry(
    'mediator_assigned',
    'Mediator assigned to dispute',
    assignedBy,
    { mediatorId: mediator }
  );
};

// Resolve dispute
DisputeSchema.methods.resolve = function(resolvedBy, resolutionType, outcome) {
  this.resolution = {
    resolvedAt: new Date(),
    resolvedBy,
    resolutionType,
    outcome
  };
  this.status = 'resolved';
  
  return this.addTimelineEntry(
    'dispute_resolved',
    `Dispute resolved via ${resolutionType}`,
    resolvedBy,
    { resolutionType, outcome }
  );
};

// Check if dispute is overdue
DisputeSchema.methods.checkSLA = function() {
  const now = new Date();
  const createdHoursAgo = (now - this.createdAt) / (1000 * 60 * 60);
  
  if (!this.sla.firstResponseAt && createdHoursAgo > this.sla.responseTime) {
    this.sla.isOverdue = true;
  } else if (this.status !== 'resolved' && createdHoursAgo > this.sla.resolutionTime) {
    this.sla.isOverdue = true;
  }
  
  return this.sla.isOverdue;
};

// Create indexes for better performance
DisputeSchema.index({ disputeId: 1 });
DisputeSchema.index({ order: 1 });
DisputeSchema.index({ initiator: 1, status: 1 });
DisputeSchema.index({ respondent: 1, status: 1 });
DisputeSchema.index({ vendor: 1, status: 1 });
DisputeSchema.index({ status: 1, priority: 1 });
DisputeSchema.index({ createdAt: -1 });
DisputeSchema.index({ 'escalation.level': 1 });
DisputeSchema.index({ 'sla.isOverdue': 1 });
DisputeSchema.index({ 'mediation.mediator': 1 });

module.exports = mongoose.model('Dispute', DisputeSchema);
