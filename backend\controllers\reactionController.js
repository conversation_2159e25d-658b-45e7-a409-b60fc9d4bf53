const Reaction = require('../models/Reaction');
const LiveStream = require('../models/LiveStream');
const User = require('../models/User');
const asyncHandler = require('../middleware/async');
const ErrorResponse = require('../utils/errorResponse');
const socketEmitter = require('../utils/socketEmitter');

/**
 * Get all reactions
 * @route GET /api/reactions
 * @access Public
 */
exports.getReactions = asyncHandler(async (req, res, next) => {
  const { page = 1, limit = 10, category } = req.query;
  const skip = (page - 1) * limit;

  // Build query
  let query = {};
  
  // Add category filter if provided
  if (category) {
    query.category = category;
  }

  // Execute query
  const reactions = await Reaction.find(query)
    .sort({ popularity: -1 })
    .skip(skip)
    .limit(parseInt(limit));

  // Get total count
  const total = await Reaction.countDocuments(query);

  res.status(200).json({
    success: true,
    count: reactions.length,
    total,
    pagination: {
      page: parseInt(page),
      limit: parseInt(limit),
      totalPages: Math.ceil(total / limit),
    },
    data: reactions,
  });
});

/**
 * Get a single reaction
 * @route GET /api/reactions/:id
 * @access Public
 */
exports.getReaction = asyncHandler(async (req, res, next) => {
  const reaction = await Reaction.findById(req.params.id);

  if (!reaction) {
    return next(new ErrorResponse(`Reaction not found with id of ${req.params.id}`, 404));
  }

  res.status(200).json({
    success: true,
    data: reaction,
  });
});

/**
 * Create a new reaction
 * @route POST /api/reactions
 * @access Private
 */
exports.createReaction = asyncHandler(async (req, res, next) => {
  // Add user to request body
  req.body.user = req.user.id;

  // Create reaction
  const reaction = await Reaction.create(req.body);

  res.status(201).json({
    success: true,
    data: reaction,
  });
});

/**
 * Update a reaction
 * @route PUT /api/reactions/:id
 * @access Private
 */
exports.updateReaction = asyncHandler(async (req, res, next) => {
  let reaction = await Reaction.findById(req.params.id);

  if (!reaction) {
    return next(new ErrorResponse(`Reaction not found with id of ${req.params.id}`, 404));
  }

  // Make sure user is the reaction owner
  if (reaction.user.toString() !== req.user.id && req.user.role !== 'admin') {
    return next(new ErrorResponse('Not authorized to update this reaction', 403));
  }

  // Update reaction
  reaction = await Reaction.findByIdAndUpdate(req.params.id, req.body, {
    new: true,
    runValidators: true,
  });

  res.status(200).json({
    success: true,
    data: reaction,
  });
});

/**
 * Delete a reaction
 * @route DELETE /api/reactions/:id
 * @access Private
 */
exports.deleteReaction = asyncHandler(async (req, res, next) => {
  const reaction = await Reaction.findById(req.params.id);

  if (!reaction) {
    return next(new ErrorResponse(`Reaction not found with id of ${req.params.id}`, 404));
  }

  // Make sure user is the reaction owner
  if (reaction.user.toString() !== req.user.id && req.user.role !== 'admin') {
    return next(new ErrorResponse('Not authorized to delete this reaction', 403));
  }

  // Delete reaction
  await reaction.remove();

  res.status(200).json({
    success: true,
    data: {},
  });
});

/**
 * Send a reaction to a live stream
 * @route POST /api/live-streams/:streamId/reactions
 * @access Private
 */
exports.sendReaction = asyncHandler(async (req, res, next) => {
  const { streamId } = req.params;
  const { reactionId, message, position, emotions } = req.body;

  // Check if stream exists
  const stream = await LiveStream.findById(streamId);
  if (!stream) {
    return next(new ErrorResponse(`Live stream not found with id of ${streamId}`, 404));
  }

  // Check if reaction exists if reactionId is provided
  let reaction = null;
  if (reactionId) {
    reaction = await Reaction.findById(reactionId);
    if (!reaction) {
      return next(new ErrorResponse(`Reaction not found with id of ${reactionId}`, 404));
    }
  }

  // Create reaction data
  const reactionData = {
    user: req.user._id,
    stream: streamId,
    reaction: reactionId,
    message,
    position,
    emotions,
    createdAt: Date.now(),
  };

  // Emit socket event for the reaction
  socketEmitter.emitLiveStreamReaction(streamId, {
    ...reactionData,
    user: {
      _id: req.user._id,
      name: req.user.name,
      username: req.user.username,
      profilePicture: req.user.profilePicture,
    },
    reaction: reaction ? {
      _id: reaction._id,
      name: reaction.name,
      icon: reaction.icon,
      animation: reaction.animation,
    } : null,
  });

  // Increment reaction count for the stream
  await LiveStream.findByIdAndUpdate(streamId, {
    $inc: { reactionCount: 1 },
  });

  res.status(200).json({
    success: true,
    data: reactionData,
  });
});
