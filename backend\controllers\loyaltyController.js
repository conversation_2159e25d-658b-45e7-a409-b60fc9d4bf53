const { LoyaltyProgram, UserLoyalty } = require('../models/LoyaltyProgram');
const User = require('../models/User');
const Order = require('../models/Order');

// @desc    Get user's loyalty account
// @route   GET /api/loyalty/account
// @access  Private
exports.getLoyaltyAccount = async (req, res) => {
  try {
    let userLoyalty = await UserLoyalty.findOne({ user: req.user.id })
      .populate('program', 'name type pointsConfig tiersConfig')
      .lean();

    if (!userLoyalty) {
      // Create default loyalty account
      const defaultProgram = await LoyaltyProgram.findOne({ type: 'points', isActive: true });

      if (defaultProgram) {
        userLoyalty = new UserLoyalty({
          user: req.user.id,
          program: defaultProgram._id
        });
        await userLoyalty.save();
        await userLoyalty.populate('program', 'name type pointsConfig tiersConfig');
      } else {
        return res.status(404).json({
          success: false,
          message: 'No active loyalty program found'
        });
      }
    }

    // Calculate next tier requirements
    if (userLoyalty.program.type === 'tiers' && userLoyalty.program.tiersConfig.tiers) {
      const tiers = userLoyalty.program.tiersConfig.tiers.sort((a, b) => a.minSpent - b.minSpent);
      const currentTierIndex = tiers.findIndex(tier => tier.name === userLoyalty.tier.current);

      if (currentTierIndex < tiers.length - 1) {
        const nextTier = tiers[currentTierIndex + 1];
        userLoyalty.tier.nextTier = nextTier.name;
        userLoyalty.tier.nextTierRequirement = nextTier.minSpent - userLoyalty.tier.totalSpent;
      }
    }

    res.status(200).json({
      success: true,
      data: userLoyalty
    });
  } catch (error) {
    console.error('Error getting loyalty account:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Get loyalty programs
// @route   GET /api/loyalty/programs
// @access  Public
exports.getLoyaltyPrograms = async (req, res) => {
  try {
    const programs = await LoyaltyProgram.find({ isActive: true })
      .select('-creator')
      .lean();

    res.status(200).json({
      success: true,
      data: programs
    });
  } catch (error) {
    console.error('Error getting loyalty programs:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Join loyalty program
// @route   POST /api/loyalty/join/:programId
// @access  Private
exports.joinLoyaltyProgram = async (req, res) => {
  try {
    const program = await LoyaltyProgram.findById(req.params.programId);

    if (!program || !program.isActive) {
      return res.status(404).json({
        success: false,
        message: 'Loyalty program not found or inactive'
      });
    }

    // Check if user already has a loyalty account
    const existingAccount = await UserLoyalty.findOne({ user: req.user.id });

    if (existingAccount) {
      return res.status(400).json({
        success: false,
        message: 'User already enrolled in a loyalty program'
      });
    }

    // Create new loyalty account
    const userLoyalty = new UserLoyalty({
      user: req.user.id,
      program: program._id
    });

    // Add signup bonus if configured
    const signupBonus = program.pointsConfig?.bonusEvents?.find(event => event.event === 'signup');
    if (signupBonus) {
      await userLoyalty.addPoints(signupBonus.points, 'Signup bonus');
    }

    await userLoyalty.save();
    await userLoyalty.populate('program', 'name type');

    res.status(201).json({
      success: true,
      data: userLoyalty,
      message: 'Successfully joined loyalty program'
    });
  } catch (error) {
    console.error('Error joining loyalty program:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Redeem points
// @route   POST /api/loyalty/redeem
// @access  Private
exports.redeemPoints = async (req, res) => {
  try {
    const { points, description } = req.body;

    if (!points || points <= 0) {
      return res.status(400).json({
        success: false,
        message: 'Invalid points amount'
      });
    }

    const userLoyalty = await UserLoyalty.findOne({ user: req.user.id })
      .populate('program');

    if (!userLoyalty) {
      return res.status(404).json({
        success: false,
        message: 'Loyalty account not found'
      });
    }

    if (userLoyalty.points.available < points) {
      return res.status(400).json({
        success: false,
        message: 'Insufficient points'
      });
    }

    await userLoyalty.redeemPoints(points, description || 'Points redemption');

    res.status(200).json({
      success: true,
      data: userLoyalty,
      message: 'Points redeemed successfully'
    });
  } catch (error) {
    console.error('Error redeeming points:', error);
    res.status(500).json({
      success: false,
      message: error.message || 'Server error'
    });
  }
};

// @desc    Get referral link
// @route   GET /api/loyalty/referral
// @access  Private
exports.getReferralLink = async (req, res) => {
  try {
    const userLoyalty = await UserLoyalty.findOne({ user: req.user.id })
      .populate('program');

    if (!userLoyalty) {
      return res.status(404).json({
        success: false,
        message: 'Loyalty account not found'
      });
    }

    if (userLoyalty.program.type !== 'referral') {
      return res.status(400).json({
        success: false,
        message: 'Referral program not available'
      });
    }

    // Generate referral link
    const referralCode = Buffer.from(req.user.id).toString('base64').slice(0, 8);
    const referralLink = `${process.env.FRONTEND_URL}/signup?ref=${referralCode}`;

    res.status(200).json({
      success: true,
      data: {
        referralCode,
        referralLink,
        referralStats: userLoyalty.referrals,
        rewards: userLoyalty.program.referralConfig
      }
    });
  } catch (error) {
    console.error('Error getting referral link:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Process referral
// @route   POST /api/loyalty/referral/process
// @access  Private
exports.processReferral = async (req, res) => {
  try {
    const { referralCode } = req.body;

    if (!referralCode) {
      return res.status(400).json({
        success: false,
        message: 'Referral code is required'
      });
    }

    // Decode referral code to get referrer user ID
    const referrerId = Buffer.from(referralCode, 'base64').toString();

    if (referrerId === req.user.id) {
      return res.status(400).json({
        success: false,
        message: 'Cannot refer yourself'
      });
    }

    const referrerLoyalty = await UserLoyalty.findOne({ user: referrerId })
      .populate('program');

    if (!referrerLoyalty || referrerLoyalty.program.type !== 'referral') {
      return res.status(404).json({
        success: false,
        message: 'Invalid referral code'
      });
    }

    // Check if user already used a referral
    const existingReferral = await UserLoyalty.findOne({
      user: req.user.id,
      'referrals.referredUsers.user': req.user.id
    });

    if (existingReferral) {
      return res.status(400).json({
        success: false,
        message: 'User already used a referral'
      });
    }

    // Process referral rewards
    const referralConfig = referrerLoyalty.program.referralConfig;

    // Reward referrer
    if (referralConfig.referrerReward === 'points') {
      await referrerLoyalty.addPoints(
        referralConfig.referrerValue,
        `Referral bonus for referring ${req.user.name}`
      );
    }

    // Add referral to referrer's account
    await referrerLoyalty.addReferral(req.user.id, referralConfig.referrerValue);

    // Reward referee (new user)
    let refereeLoyalty = await UserLoyalty.findOne({ user: req.user.id });

    if (!refereeLoyalty) {
      refereeLoyalty = new UserLoyalty({
        user: req.user.id,
        program: referrerLoyalty.program._id
      });
    }

    if (referralConfig.refereeReward === 'points') {
      await refereeLoyalty.addPoints(
        referralConfig.refereeValue,
        'Referral welcome bonus'
      );
    }

    await refereeLoyalty.save();

    res.status(200).json({
      success: true,
      message: 'Referral processed successfully',
      data: {
        referrerReward: referralConfig.referrerValue,
        refereeReward: referralConfig.refereeValue
      }
    });
  } catch (error) {
    console.error('Error processing referral:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Get leaderboard
// @route   GET /api/loyalty/leaderboard
// @access  Public
exports.getLeaderboard = async (req, res) => {
  try {
    const { programId, period = 'monthly', limit = 10 } = req.query;

    if (!programId) {
      return res.status(400).json({
        success: false,
        message: 'Program ID is required'
      });
    }

    const leaderboard = await UserLoyalty.getLeaderboard(programId, period, parseInt(limit));

    res.status(200).json({
      success: true,
      data: leaderboard
    });
  } catch (error) {
    console.error('Error getting leaderboard:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Get user badges
// @route   GET /api/loyalty/badges
// @access  Private
exports.getUserBadges = async (req, res) => {
  try {
    const userLoyalty = await UserLoyalty.findOne({ user: req.user.id })
      .populate('program')
      .lean();

    if (!userLoyalty) {
      return res.status(404).json({
        success: false,
        message: 'Loyalty account not found'
      });
    }

    const availableBadges = userLoyalty.program.gamificationConfig?.badges || [];
    const earnedBadges = userLoyalty.badges || [];

    // Check for new badges to award
    const newBadges = [];

    for (const badge of availableBadges) {
      const alreadyEarned = earnedBadges.some(earned => earned.badge === badge.name);

      if (!alreadyEarned) {
        // Check if user meets criteria
        let meetsRequirement = false;

        switch (badge.criteria.type) {
          case 'points_earned':
            meetsRequirement = userLoyalty.points.earned >= badge.criteria.value;
            break;
          case 'total_spent':
            meetsRequirement = userLoyalty.tier.totalSpent >= badge.criteria.value;
            break;
          case 'referrals':
            meetsRequirement = userLoyalty.referrals.successful >= badge.criteria.value;
            break;
        }

        if (meetsRequirement) {
          newBadges.push(badge);
        }
      }
    }

    res.status(200).json({
      success: true,
      data: {
        earnedBadges,
        availableBadges,
        newBadges
      }
    });
  } catch (error) {
    console.error('Error getting user badges:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

module.exports = {
  getLoyaltyAccount: exports.getLoyaltyAccount,
  getLoyaltyPrograms: exports.getLoyaltyPrograms,
  joinLoyaltyProgram: exports.joinLoyaltyProgram,
  redeemPoints: exports.redeemPoints,
  getReferralLink: exports.getReferralLink,
  processReferral: exports.processReferral,
  getLeaderboard: exports.getLeaderboard,
  getUserBadges: exports.getUserBadges
};
