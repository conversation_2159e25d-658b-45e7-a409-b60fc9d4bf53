const mongoose = require('mongoose');

const CartItemSchema = new mongoose.Schema({
  product: {
    type: mongoose.Schema.ObjectId,
    ref: 'Product',
    required: true
  },
  quantity: {
    type: Number,
    required: true,
    min: [1, 'Quantity must be at least 1'],
    default: 1
  },
  price: {
    type: Number,
    required: true
  },
  addedAt: {
    type: Date,
    default: Date.now
  }
});

const CartSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.ObjectId,
    ref: 'User',
    required: true,
    unique: true
  },
  items: [CartItemSchema],
  promoCode: {
    code: String,
    discount: Number,
    type: {
      type: String,
      enum: ['percentage', 'fixed']
    }
  },
  subtotal: {
    type: Number,
    default: 0
  },
  discount: {
    type: Number,
    default: 0
  },
  tax: {
    type: Number,
    default: 0
  },
  shipping: {
    type: Number,
    default: 0
  },
  total: {
    type: Number,
    default: 0
  },
  updatedAt: {
    type: Date,
    default: Date.now
  },
  createdAt: {
    type: Date,
    default: Date.now
  }
});

// Update the updatedAt field before saving
CartSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

// Calculate totals before saving
CartSchema.pre('save', async function(next) {
  if (this.items && this.items.length > 0) {
    // Populate products to get current prices
    await this.populate('items.product');
    
    let subtotal = 0;
    this.items.forEach(item => {
      if (item.product) {
        item.price = item.product.price;
        subtotal += item.product.price * item.quantity;
      }
    });
    
    this.subtotal = subtotal;
    
    // Apply promo code discount
    let discount = 0;
    if (this.promoCode) {
      if (this.promoCode.type === 'percentage') {
        discount = (subtotal * this.promoCode.discount) / 100;
      } else {
        discount = this.promoCode.discount;
      }
    }
    this.discount = discount;
    
    // Calculate tax (assuming 10% tax rate)
    this.tax = (subtotal - discount) * 0.1;
    
    // Calculate total
    this.total = subtotal - discount + this.tax + this.shipping;
  } else {
    this.subtotal = 0;
    this.discount = 0;
    this.tax = 0;
    this.total = this.shipping;
  }
  
  next();
});

// Instance method to get cart summary
CartSchema.methods.getSummary = function() {
  return {
    itemCount: this.items.reduce((total, item) => total + item.quantity, 0),
    subtotal: this.subtotal,
    discount: this.discount,
    tax: this.tax,
    shipping: this.shipping,
    total: this.total
  };
};

// Instance method to clear cart
CartSchema.methods.clearCart = function() {
  this.items = [];
  this.promoCode = undefined;
  this.subtotal = 0;
  this.discount = 0;
  this.tax = 0;
  this.total = this.shipping;
  return this.save();
};

// Create indexes for better performance
CartSchema.index({ user: 1 });
CartSchema.index({ 'items.product': 1 });
CartSchema.index({ updatedAt: -1 });

module.exports = mongoose.model('Cart', CartSchema);
