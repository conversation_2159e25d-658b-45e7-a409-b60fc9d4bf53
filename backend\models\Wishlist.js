const mongoose = require('mongoose');

const WishlistSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.ObjectId,
    ref: 'User',
    required: true,
    unique: true
  },
  products: [{
    type: mongoose.Schema.ObjectId,
    ref: 'Product'
  }],
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Update the updatedAt field before saving
WishlistSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

// Instance method to add product
WishlistSchema.methods.addProduct = function(productId) {
  if (!this.products.includes(productId)) {
    this.products.push(productId);
  }
  return this.save();
};

// Instance method to remove product
WishlistSchema.methods.removeProduct = function(productId) {
  this.products = this.products.filter(id => id.toString() !== productId.toString());
  return this.save();
};

// Instance method to check if product is in wishlist
WishlistSchema.methods.hasProduct = function(productId) {
  return this.products.some(id => id.toString() === productId.toString());
};

// Instance method to get product count
WishlistSchema.methods.getProductCount = function() {
  return this.products.length;
};

// Static method to find or create wishlist for user
WishlistSchema.statics.findOrCreateForUser = async function(userId) {
  let wishlist = await this.findOne({ user: userId });
  
  if (!wishlist) {
    wishlist = new this({
      user: userId,
      products: []
    });
    await wishlist.save();
  }
  
  return wishlist;
};

// Create indexes for better performance
WishlistSchema.index({ user: 1 });
WishlistSchema.index({ products: 1 });
WishlistSchema.index({ updatedAt: -1 });

module.exports = mongoose.model('Wishlist', WishlistSchema);
