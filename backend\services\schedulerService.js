const schedule = require('node-schedule');
const Message = require('../models/Message');
const Conversation = require('../models/Conversation');
const socketEmitter = require('../utils/socketEmitter');

// Store scheduled jobs
const scheduledJobs = new Map();

/**
 * Initialize the scheduler service
 * This should be called when the server starts
 */
const initScheduler = async () => {
  try {
    console.log('Initializing message scheduler...');

    // Find all scheduled messages
    const scheduledMessages = await Message.find({
      isScheduled: true,
      scheduledFor: { $gt: new Date() }
    }).populate('sender', 'username name profilePicture');

    console.log(`Found ${scheduledMessages.length} scheduled messages`);

    // Schedule each message
    scheduledMessages.forEach(message => {
      scheduleMessage(message);
    });
  } catch (error) {
    console.error('Error initializing scheduler:', error);
  }
};

/**
 * Schedule a message to be sent at the specified time
 * @param {Object} message - The message document
 */
const scheduleMessage = (message) => {
  // Skip if message is already sent or doesn't have a valid scheduledFor date
  if (!message.isScheduled || !message.scheduledFor) {
    return;
  }

  const scheduledTime = new Date(message.scheduledFor);

  // Skip if scheduled time is in the past
  if (scheduledTime <= new Date()) {
    return;
  }

  console.log(`Scheduling message ${message._id} for ${scheduledTime}`);

  // Create a job
  const job = schedule.scheduleJob(scheduledTime, async () => {
    try {
      // Find the message again to make sure it still exists and is still scheduled
      const updatedMessage = await Message.findById(message._id);

      if (!updatedMessage || !updatedMessage.isScheduled) {
        console.log(`Message ${message._id} no longer exists or is not scheduled`);
        return;
      }

      // Mark as no longer scheduled
      updatedMessage.isScheduled = false;
      await updatedMessage.save();

      // Update conversation with last message
      await Conversation.findByIdAndUpdate(
        updatedMessage.conversation,
        { lastMessage: updatedMessage._id }
      );

      // Populate sender details if not already populated
      if (!updatedMessage.populated('sender')) {
        await updatedMessage.populate('sender', 'username name profilePicture');
      }

      // Populate reply details if any
      if (updatedMessage.replyTo && !updatedMessage.populated('replyTo')) {
        await updatedMessage.populate('replyTo');
      }

      // Emit message to all participants
      socketEmitter.emitNewMessage(updatedMessage);

      console.log(`Scheduled message ${message._id} sent`);

      // Remove job from map
      scheduledJobs.delete(message._id.toString());
    } catch (error) {
      console.error(`Error sending scheduled message ${message._id}:`, error);
    }
  });

  // Store job in map
  scheduledJobs.set(message._id.toString(), job);
};

/**
 * Cancel a scheduled message
 * @param {string} messageId - The message ID
 */
const cancelScheduledMessage = (messageId) => {
  const job = scheduledJobs.get(messageId);

  if (job) {
    job.cancel();
    scheduledJobs.delete(messageId);
    console.log(`Scheduled message ${messageId} cancelled`);
    return true;
  }

  return false;
};

/**
 * Reschedule a message
 * @param {Object} message - The message document
 */
const rescheduleMessage = (message) => {
  // Cancel existing job if any
  cancelScheduledMessage(message._id.toString());

  // Schedule new job
  scheduleMessage(message);
};

module.exports = {
  initScheduler,
  scheduleMessage,
  cancelScheduledMessage,
  rescheduleMessage
};
