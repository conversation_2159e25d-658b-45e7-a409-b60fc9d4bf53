const express = require('express');
const router = express.Router();
const {
  getEmotions,
  getEmotionById,
  createEmotion,
  updateEmotion,
  deleteEmotion,
  getPopularEmotions
} = require('../controllers/emotionController');
const { protect, admin } = require('../middleware/authMiddleware');

// Public routes
router.get('/', getEmotions);
router.get('/popular', getPopularEmotions);
router.get('/:id', getEmotionById);

// Admin routes
router.post('/', protect, admin, createEmotion);
router.put('/:id', protect, admin, updateEmotion);
router.delete('/:id', protect, admin, deleteEmotion);

module.exports = router;
