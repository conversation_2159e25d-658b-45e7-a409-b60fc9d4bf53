const mongoose = require('mongoose');

const TipSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  recipient: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  stream: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'LiveStream',
    default: null
  },
  amount: {
    type: Number,
    required: true
  },
  currency: {
    type: String,
    enum: ['usd', 'eth', 'matic', 'sol', 'bnb', 'flow', 'coins'],
    default: 'coins'
  },
  message: {
    type: String,
    maxlength: [200, 'Message cannot be more than 200 characters'],
    default: null
  },
  createdAt: {
    type: Date,
    default: Date.now
  }
});

module.exports = mongoose.model('Tip', TipSchema);
