// Script to clean Vite cache and restart the development server
import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';

console.log('🧹 Cleaning Vite cache...');

// Path to the .vite directory
const viteCachePath = path.resolve('node_modules/.vite');

// Check if the directory exists
if (fs.existsSync(viteCachePath)) {
  try {
    // Remove the directory
    fs.rmSync(viteCachePath, { recursive: true, force: true });
    console.log('✅ Vite cache cleaned successfully!');
  } catch (error) {
    console.error('❌ Error cleaning Vite cache:', error);
    process.exit(1);
  }
} else {
  console.log('ℹ️ No Vite cache found, nothing to clean.');
}

// Clear browser cache by touching the vite.config.js file
try {
  const viteConfigPath = path.resolve('vite.config.js');
  const now = new Date();
  fs.utimesSync(viteConfigPath, now, now);
  console.log('✅ Updated vite.config.js timestamp to force browser cache refresh');
} catch (error) {
  console.error('❌ Error updating vite.config.js timestamp:', error);
}

console.log('🚀 Starting development server...');

try {
  // Start the development server
  execSync('npm start', { stdio: 'inherit' });
} catch (error) {
  console.error('❌ Error starting development server:', error);
  process.exit(1);
}
