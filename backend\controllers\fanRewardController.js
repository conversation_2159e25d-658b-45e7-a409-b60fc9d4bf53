const FanReward = require('../models/FanReward');
const User = require('../models/User');
const LiveStream = require('../models/LiveStream');
const asyncHandler = require('../middleware/async');
const ErrorResponse = require('../utils/errorResponse');
const socketEmitter = require('../utils/socketEmitter');

/**
 * Get fan rewards for a user and streamer
 * @route GET /api/users/:streamerId/rewards
 * @access Private
 */
exports.getUserRewards = asyncHandler(async (req, res, next) => {
  const streamerId = req.params.streamerId;
  const userId = req.user.id;

  // Check if streamer exists
  const streamer = await User.findById(streamerId);
  if (!streamer) {
    return next(new ErrorResponse(`User not found with id of ${streamerId}`, 404));
  }

  // Get or create fan rewards
  let fanReward = await FanReward.findOne({ user: userId, streamer: streamerId });

  if (!fanReward) {
    // Create new fan reward record
    fanReward = await FanReward.create({
      user: userId,
      streamer: streamerId,
      points: 0,
      level: 1,
    });
  }

  res.status(200).json({
    success: true,
    data: fanReward,
  });
});

/**
 * Get leaderboard for a streamer
 * @route GET /api/users/:streamerId/rewards/leaderboard
 * @access Public
 */
exports.getLeaderboard = asyncHandler(async (req, res, next) => {
  const streamerId = req.params.streamerId;
  const { limit = 10 } = req.query;

  // Check if streamer exists
  const streamer = await User.findById(streamerId);
  if (!streamer) {
    return next(new ErrorResponse(`User not found with id of ${streamerId}`, 404));
  }

  // Get top fans by points
  const leaderboard = await FanReward.find({ streamer: streamerId })
    .sort({ points: -1 })
    .limit(parseInt(limit))
    .populate('user', 'username name profilePicture isVerified');

  res.status(200).json({
    success: true,
    count: leaderboard.length,
    data: leaderboard,
  });
});

/**
 * Award points to a user
 * @route POST /api/users/:streamerId/rewards/points
 * @access Private
 */
exports.awardPoints = asyncHandler(async (req, res, next) => {
  const streamerId = req.params.streamerId;
  const userId = req.body.userId || req.user.id;
  const { points, reason, source, streamId } = req.body;

  // Validate input
  if (!points || points <= 0) {
    return next(new ErrorResponse('Please provide a valid number of points', 400));
  }

  if (!reason) {
    return next(new ErrorResponse('Please provide a reason for awarding points', 400));
  }

  // Check if streamer exists
  const streamer = await User.findById(streamerId);
  if (!streamer) {
    return next(new ErrorResponse(`User not found with id of ${streamerId}`, 404));
  }

  // Check if user exists
  const user = await User.findById(userId);
  if (!user) {
    return next(new ErrorResponse(`User not found with id of ${userId}`, 404));
  }

  // Check if stream exists if streamId is provided
  if (streamId) {
    const stream = await LiveStream.findById(streamId);
    if (!stream) {
      return next(new ErrorResponse(`Stream not found with id of ${streamId}`, 404));
    }
  }

  // Only streamer can award points to others
  if (userId !== req.user.id && streamerId !== req.user.id) {
    return next(new ErrorResponse('Not authorized to award points to other users', 403));
  }

  // Get or create fan rewards
  let fanReward = await FanReward.findOne({ user: userId, streamer: streamerId });

  if (!fanReward) {
    // Create new fan reward record
    fanReward = await FanReward.create({
      user: userId,
      streamer: streamerId,
      points: 0,
      level: 1,
    });
  }

  // Add points
  const oldPoints = fanReward.points;
  const oldLevel = fanReward.level;
  fanReward.points += points;

  // Add to points history
  fanReward.pointsHistory.push({
    amount: points,
    reason,
    source: source || 'other',
    timestamp: Date.now(),
    relatedStream: streamId,
  });

  // Check for level up
  const streamerSettings = streamer.fanRewardsSettings || {};
  const levelUpThresholds = streamerSettings.levelUpThresholds || [100, 250, 500, 1000, 2000, 5000, 10000];
  
  // Find the appropriate level based on points
  let newLevel = 1;
  for (let i = 0; i < levelUpThresholds.length; i++) {
    if (fanReward.points >= levelUpThresholds[i]) {
      newLevel = i + 2; // Level 1 is default, so we start at level 2
    } else {
      break;
    }
  }
  
  fanReward.level = newLevel;

  // Save changes
  await fanReward.save();

  // Check if user leveled up
  const didLevelUp = oldLevel < fanReward.level;

  // If user leveled up, emit socket event
  if (didLevelUp) {
    socketEmitter.emitUserLevelUp({
      userId,
      streamerId,
      oldLevel,
      newLevel: fanReward.level,
      points: fanReward.points,
    });
  }

  res.status(200).json({
    success: true,
    data: {
      fanReward,
      pointsAdded: points,
      oldPoints,
      newPoints: fanReward.points,
      didLevelUp,
      oldLevel,
      newLevel: fanReward.level,
    },
  });
});

/**
 * Award a badge to a user
 * @route POST /api/users/:streamerId/rewards/badges
 * @access Private
 */
exports.awardBadge = asyncHandler(async (req, res, next) => {
  const streamerId = req.params.streamerId;
  const userId = req.body.userId;
  const { name, description, imageUrl, category, tier } = req.body;

  // Validate input
  if (!name) {
    return next(new ErrorResponse('Please provide a badge name', 400));
  }

  if (!userId) {
    return next(new ErrorResponse('Please provide a user ID', 400));
  }

  // Check if streamer exists and is the requester
  if (streamerId !== req.user.id) {
    return next(new ErrorResponse('Not authorized to award badges', 403));
  }

  // Check if user exists
  const user = await User.findById(userId);
  if (!user) {
    return next(new ErrorResponse(`User not found with id of ${userId}`, 404));
  }

  // Get or create fan rewards
  let fanReward = await FanReward.findOne({ user: userId, streamer: streamerId });

  if (!fanReward) {
    // Create new fan reward record
    fanReward = await FanReward.create({
      user: userId,
      streamer: streamerId,
      points: 0,
      level: 1,
    });
  }

  // Add badge
  fanReward.badges.push({
    name,
    description: description || '',
    imageUrl: imageUrl || '',
    earnedAt: Date.now(),
    category: category || 'achievement',
    tier: tier || 1,
    isActive: true,
  });

  // Save changes
  await fanReward.save();

  // Emit socket event for new badge
  socketEmitter.emitUserBadgeAwarded({
    userId,
    streamerId,
    badge: fanReward.badges[fanReward.badges.length - 1],
  });

  res.status(200).json({
    success: true,
    data: {
      fanReward,
      badge: fanReward.badges[fanReward.badges.length - 1],
    },
  });
});

/**
 * Redeem a reward
 * @route POST /api/users/:streamerId/rewards/redeem
 * @access Private
 */
exports.redeemReward = asyncHandler(async (req, res, next) => {
  const streamerId = req.params.streamerId;
  const userId = req.user.id;
  const { rewardId } = req.body;

  // Validate input
  if (!rewardId) {
    return next(new ErrorResponse('Please provide a reward ID', 400));
  }

  // Check if streamer exists
  const streamer = await User.findById(streamerId);
  if (!streamer) {
    return next(new ErrorResponse(`User not found with id of ${streamerId}`, 404));
  }

  // Get fan rewards
  const fanReward = await FanReward.findOne({ user: userId, streamer: streamerId });

  if (!fanReward) {
    return next(new ErrorResponse('Fan rewards not found', 404));
  }

  // Find the reward in streamer's custom rewards
  const streamerSettings = streamer.fanRewardsSettings || {};
  const customRewards = streamerSettings.customRewards || [];
  const reward = customRewards.find(r => r._id.toString() === rewardId);

  if (!reward) {
    return next(new ErrorResponse(`Reward not found with id of ${rewardId}`, 404));
  }

  // Check if user has enough points
  if (fanReward.points < reward.pointCost) {
    return next(new ErrorResponse('Not enough points to redeem this reward', 400));
  }

  // Deduct points
  fanReward.points -= reward.pointCost;

  // Add to points history
  fanReward.pointsHistory.push({
    amount: -reward.pointCost,
    reason: `Redeemed reward: ${reward.name}`,
    source: 'redemption',
    timestamp: Date.now(),
  });

  // Add to redeemed rewards
  fanReward.rewards.push({
    name: reward.name,
    description: reward.description,
    imageUrl: reward.imageUrl,
    pointCost: reward.pointCost,
    type: reward.type,
    redeemedAt: Date.now(),
    isRedeemed: true,
    redemptionCode: Math.random().toString(36).substring(2, 10).toUpperCase(), // Generate a random code
  });

  // Save changes
  await fanReward.save();

  // Emit socket event for reward redemption
  socketEmitter.emitUserRewardRedeemed({
    userId,
    streamerId,
    reward: fanReward.rewards[fanReward.rewards.length - 1],
  });

  res.status(200).json({
    success: true,
    data: {
      fanReward,
      redeemedReward: fanReward.rewards[fanReward.rewards.length - 1],
    },
  });
});
