const express = require('express');
const router = express.Router();
const {
  getARAssets,
  getARAssetById,
  createARAsset,
  updateARAsset,
  deleteARAsset,
  getPopularARAssets,
  incrementARAssetUsage
} = require('../controllers/arController');
const { protect, admin } = require('../middleware/authMiddleware');

// Public routes
router.get('/assets', getARAssets);
router.get('/assets/popular', getPopularARAssets);
router.get('/assets/:id', getARAssetById);

// Private routes
router.post('/assets', protect, createARAsset);
router.put('/assets/:id', protect, updateARAsset);
router.delete('/assets/:id', protect, deleteARAsset);
router.post('/assets/:id/use', protect, incrementARAssetUsage);

module.exports = router;
