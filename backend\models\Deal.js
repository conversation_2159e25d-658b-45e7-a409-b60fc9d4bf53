const mongoose = require('mongoose');

const DealSchema = new mongoose.Schema({
  product: {
    type: mongoose.Schema.ObjectId,
    ref: 'Product',
    required: true
  },
  stream: {
    type: mongoose.Schema.ObjectId,
    ref: 'LiveStream',
    required: true
  },
  discount: {
    type: Number,
    required: [true, 'Please add a discount percentage'],
    min: [1, 'Discount must be at least 1%'],
    max: [99, 'Discount cannot be more than 99%']
  },
  duration: {
    type: Number,
    required: [true, 'Please add a duration in minutes'],
    min: [1, 'Duration must be at least 1 minute']
  },
  startTime: {
    type: Date,
    default: Date.now
  },
  endTime: {
    type: Date,
    required: [true, 'Please add an end time']
  },
  isActive: {
    type: Boolean,
    default: true
  },
  createdAt: {
    type: Date,
    default: Date.now
  }
});

// Create index for faster queries
DealSchema.index({ stream: 1, endTime: -1 });
DealSchema.index({ product: 1 });
DealSchema.index({ isActive: 1 });

module.exports = mongoose.model('Deal', DealSchema);
