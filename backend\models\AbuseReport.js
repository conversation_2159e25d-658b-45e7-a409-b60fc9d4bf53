const mongoose = require('mongoose');

const AbuseReportSchema = new mongoose.Schema({
  reportId: {
    type: String,
    unique: true,
    required: true
  },
  reporter: {
    type: mongoose.Schema.ObjectId,
    ref: 'User',
    required: true
  },
  reportedUser: {
    type: mongoose.Schema.ObjectId,
    ref: 'User'
  },
  reportedVendor: {
    type: mongoose.Schema.ObjectId,
    ref: 'Vendor'
  },
  reportedContent: {
    contentType: {
      type: String,
      enum: ['product', 'review', 'message', 'profile', 'store', 'post', 'comment', 'order'],
      required: true
    },
    contentId: {
      type: mongoose.Schema.ObjectId,
      required: true
    },
    contentUrl: String,
    contentSnapshot: String // Store content at time of report
  },
  category: {
    type: String,
    enum: [
      'spam',
      'harassment',
      'hate_speech',
      'violence',
      'sexual_content',
      'fake_products',
      'counterfeit',
      'fraud',
      'scam',
      'identity_theft',
      'copyright_violation',
      'trademark_violation',
      'privacy_violation',
      'misinformation',
      'inappropriate_content',
      'policy_violation',
      'other'
    ],
    required: true
  },
  subcategory: String,
  severity: {
    type: String,
    enum: ['low', 'medium', 'high', 'critical'],
    default: 'medium'
  },
  description: {
    type: String,
    required: true,
    maxlength: 1000
  },
  evidence: [{
    type: {
      type: String,
      enum: ['screenshot', 'document', 'video', 'audio', 'link', 'other']
    },
    url: String,
    description: String,
    uploadedAt: {
      type: Date,
      default: Date.now
    }
  }],
  status: {
    type: String,
    enum: ['pending', 'under_review', 'investigating', 'action_taken', 'no_action', 'dismissed', 'escalated'],
    default: 'pending'
  },
  priority: {
    type: String,
    enum: ['low', 'normal', 'high', 'urgent'],
    default: 'normal'
  },
  assignedTo: {
    type: mongoose.Schema.ObjectId,
    ref: 'User'
  },
  assignedAt: Date,
  investigation: {
    startedAt: Date,
    investigator: {
      type: mongoose.Schema.ObjectId,
      ref: 'User'
    },
    findings: String,
    evidence: [String],
    conclusion: String,
    completedAt: Date
  },
  actions: [{
    actionType: {
      type: String,
      enum: [
        'warning_issued',
        'content_removed',
        'account_suspended',
        'account_banned',
        'product_delisted',
        'store_suspended',
        'review_removed',
        'comment_removed',
        'profile_restricted',
        'payment_frozen',
        'legal_notice',
        'no_action'
      ]
    },
    description: String,
    performedBy: {
      type: mongoose.Schema.ObjectId,
      ref: 'User'
    },
    performedAt: {
      type: Date,
      default: Date.now
    },
    duration: String, // e.g., "7 days", "permanent"
    reversible: {
      type: Boolean,
      default: true
    },
    metadata: mongoose.Schema.Types.Mixed
  }],
  communication: [{
    sender: {
      type: mongoose.Schema.ObjectId,
      ref: 'User'
    },
    recipient: {
      type: mongoose.Schema.ObjectId,
      ref: 'User'
    },
    message: String,
    isInternal: {
      type: Boolean,
      default: false
    },
    timestamp: {
      type: Date,
      default: Date.now
    }
  }],
  resolution: {
    resolvedAt: Date,
    resolvedBy: {
      type: mongoose.Schema.ObjectId,
      ref: 'User'
    },
    resolutionType: {
      type: String,
      enum: ['action_taken', 'no_violation_found', 'insufficient_evidence', 'duplicate_report', 'false_report']
    },
    summary: String,
    actionsTaken: [String],
    appealable: {
      type: Boolean,
      default: true
    },
    appealDeadline: Date
  },
  appeal: {
    isAppealed: {
      type: Boolean,
      default: false
    },
    appealedAt: Date,
    appealedBy: {
      type: mongoose.Schema.ObjectId,
      ref: 'User'
    },
    appealReason: String,
    appealEvidence: [String],
    appealStatus: {
      type: String,
      enum: ['pending', 'under_review', 'approved', 'denied']
    },
    appealResolvedAt: Date,
    appealResolvedBy: {
      type: mongoose.Schema.ObjectId,
      ref: 'User'
    },
    appealOutcome: String
  },
  relatedReports: [{
    type: mongoose.Schema.ObjectId,
    ref: 'AbuseReport'
  }],
  automation: {
    autoDetected: {
      type: Boolean,
      default: false
    },
    detectionMethod: String, // AI, keyword filter, pattern matching, etc.
    confidence: Number, // 0-1 confidence score
    autoActionTaken: String,
    requiresHumanReview: {
      type: Boolean,
      default: true
    }
  },
  metrics: {
    responseTime: Number, // in hours
    resolutionTime: Number, // in hours
    escalationCount: {
      type: Number,
      default: 0
    },
    viewCount: {
      type: Number,
      default: 0
    }
  },
  compliance: {
    gdprProcessed: {
      type: Boolean,
      default: false
    },
    legalHold: {
      type: Boolean,
      default: false
    },
    dataRetentionUntil: Date,
    lawEnforcementNotified: {
      type: Boolean,
      default: false
    },
    notificationDate: Date
  },
  feedback: {
    reporterSatisfaction: {
      type: Number,
      min: 1,
      max: 5
    },
    reporterFeedback: String,
    reportedUserFeedback: String,
    improvementSuggestions: String
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Generate unique report ID
AbuseReportSchema.pre('save', function(next) {
  if (this.isNew && !this.reportId) {
    this.reportId = `ABR-${Date.now()}-${Math.random().toString(36).substr(2, 6).toUpperCase()}`;
  }
  this.updatedAt = Date.now();
  next();
});

// Add action
AbuseReportSchema.methods.addAction = function(actionType, description, performedBy, duration, metadata = {}) {
  this.actions.push({
    actionType,
    description,
    performedBy,
    duration,
    metadata
  });
  
  if (actionType !== 'no_action') {
    this.status = 'action_taken';
  }
  
  return this.save();
};

// Add communication
AbuseReportSchema.methods.addCommunication = function(sender, recipient, message, isInternal = false) {
  this.communication.push({
    sender,
    recipient,
    message,
    isInternal
  });
  return this.save();
};

// Assign to moderator
AbuseReportSchema.methods.assignTo = function(moderator) {
  this.assignedTo = moderator;
  this.assignedAt = new Date();
  this.status = 'under_review';
  return this.save();
};

// Start investigation
AbuseReportSchema.methods.startInvestigation = function(investigator) {
  this.investigation.startedAt = new Date();
  this.investigation.investigator = investigator;
  this.status = 'investigating';
  return this.save();
};

// Complete investigation
AbuseReportSchema.methods.completeInvestigation = function(findings, evidence, conclusion) {
  this.investigation.findings = findings;
  this.investigation.evidence = evidence;
  this.investigation.conclusion = conclusion;
  this.investigation.completedAt = new Date();
  return this.save();
};

// Resolve report
AbuseReportSchema.methods.resolve = function(resolvedBy, resolutionType, summary, actionsTaken = []) {
  this.resolution = {
    resolvedAt: new Date(),
    resolvedBy,
    resolutionType,
    summary,
    actionsTaken,
    appealable: resolutionType !== 'no_violation_found',
    appealDeadline: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days
  };
  
  this.status = resolutionType === 'action_taken' ? 'action_taken' : 'no_action';
  
  // Calculate metrics
  const now = new Date();
  this.metrics.resolutionTime = (now - this.createdAt) / (1000 * 60 * 60); // hours
  
  return this.save();
};

// Submit appeal
AbuseReportSchema.methods.submitAppeal = function(appealedBy, appealReason, appealEvidence = []) {
  this.appeal = {
    isAppealed: true,
    appealedAt: new Date(),
    appealedBy,
    appealReason,
    appealEvidence,
    appealStatus: 'pending'
  };
  return this.save();
};

// Process appeal
AbuseReportSchema.methods.processAppeal = function(resolvedBy, outcome, approved = false) {
  this.appeal.appealStatus = approved ? 'approved' : 'denied';
  this.appeal.appealResolvedAt = new Date();
  this.appeal.appealResolvedBy = resolvedBy;
  this.appeal.appealOutcome = outcome;
  return this.save();
};

// Check if report is overdue
AbuseReportSchema.methods.isOverdue = function() {
  const hoursOld = (new Date() - this.createdAt) / (1000 * 60 * 60);
  const slaHours = this.severity === 'critical' ? 2 : 
                   this.severity === 'high' ? 8 : 
                   this.severity === 'medium' ? 24 : 72;
  return hoursOld > slaHours && !['action_taken', 'no_action', 'dismissed'].includes(this.status);
};

// Create indexes for better performance
AbuseReportSchema.index({ reportId: 1 });
AbuseReportSchema.index({ reporter: 1 });
AbuseReportSchema.index({ reportedUser: 1 });
AbuseReportSchema.index({ reportedVendor: 1 });
AbuseReportSchema.index({ 'reportedContent.contentType': 1, 'reportedContent.contentId': 1 });
AbuseReportSchema.index({ category: 1, status: 1 });
AbuseReportSchema.index({ status: 1, priority: 1 });
AbuseReportSchema.index({ assignedTo: 1, status: 1 });
AbuseReportSchema.index({ createdAt: -1 });
AbuseReportSchema.index({ severity: 1, status: 1 });
AbuseReportSchema.index({ 'automation.autoDetected': 1 });

module.exports = mongoose.model('AbuseReport', AbuseReportSchema);
