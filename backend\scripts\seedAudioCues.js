/**
 * Seed script to populate the AudioCue collection with default audio cues
 * Run with: node scripts/seedAudioCues.js
 */

const mongoose = require('mongoose');
const AudioCue = require('../models/AudioCue');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// Default audio cues data
const defaultAudioCues = [
  {
    name: 'Joy Sound',
    description: 'A cheerful sound for positive emotions',
    url: 'https://res.cloudinary.com/dtx9ed1hh/video/upload/v1/audio_cues/joy.mp3',
    duration: 2,
    emotion: 'positive',
    intensity: 5,
    isDefault: true,
    isPublic: true
  },
  {
    name: 'Sadness Sound',
    description: 'A melancholic sound for negative emotions',
    url: 'https://res.cloudinary.com/dtx9ed1hh/video/upload/v1/audio_cues/sadness.mp3',
    duration: 2,
    emotion: 'negative',
    intensity: 5,
    isDefault: true,
    isPublic: true
  },
  {
    name: 'Neutral Sound',
    description: 'A balanced sound for neutral emotions',
    url: 'https://res.cloudinary.com/dtx9ed1hh/video/upload/v1/audio_cues/neutral.mp3',
    duration: 2,
    emotion: 'neutral',
    intensity: 5,
    isDefault: true,
    isPublic: true
  },
  {
    name: 'Surprise Sound',
    description: 'An unexpected sound for complex emotions',
    url: 'https://res.cloudinary.com/dtx9ed1hh/video/upload/v1/audio_cues/surprise.mp3',
    duration: 2,
    emotion: 'complex',
    intensity: 5,
    isDefault: true,
    isPublic: true
  }
];

// Connect to MongoDB
mongoose.connect(process.env.MONGO_URI || 'mongodb://localhost:27017/letstalk', {
  useNewUrlParser: true,
  useUnifiedTopology: true
})
.then(async () => {
  console.log('MongoDB connected');
  
  try {
    // Check if audio cues already exist
    const existingCount = await AudioCue.countDocuments({ isDefault: true });
    
    if (existingCount > 0) {
      console.log(`${existingCount} default audio cues already exist. Skipping seed.`);
    } else {
      // Insert default audio cues
      await AudioCue.insertMany(defaultAudioCues);
      console.log(`${defaultAudioCues.length} default audio cues inserted successfully`);
    }
    
    // Disconnect from MongoDB
    mongoose.disconnect();
    console.log('MongoDB disconnected');
  } catch (error) {
    console.error('Error seeding audio cues:', error);
    mongoose.disconnect();
  }
})
.catch(err => {
  console.error('MongoDB connection error:', err);
});
