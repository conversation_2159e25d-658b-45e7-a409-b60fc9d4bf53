const asyncHandler = require('../middleware/async');
const ErrorResponse = require('../utils/errorResponse');
const StreamReminder = require('../models/StreamReminder');
const LiveStream = require('../models/LiveStream');
const User = require('../models/User');
const socketEmitter = require('../utils/socketEmitter');
const { subMinutes } = require('date-fns');

/**
 * @desc    Get all reminders for the current user
 * @route   GET /api/users/me/reminders
 * @access  Private
 */
exports.getUserReminders = asyncHandler(async (req, res, next) => {
  const reminders = await StreamReminder.find({ user: req.user.id })
    .populate({
      path: 'stream',
      select: 'title thumbnail scheduledFor duration user status',
      populate: {
        path: 'user',
        select: 'name username profilePicture'
      }
    })
    .sort({ reminderTime: 1 });

  res.status(200).json({
    success: true,
    count: reminders.length,
    data: reminders
  });
});

/**
 * @desc    Get a single reminder
 * @route   GET /api/users/me/reminders/:id
 * @access  Private
 */
exports.getReminder = asyncHandler(async (req, res, next) => {
  const reminder = await StreamReminder.findById(req.params.id)
    .populate({
      path: 'stream',
      select: 'title thumbnail scheduledFor duration user status',
      populate: {
        path: 'user',
        select: 'name username profilePicture'
      }
    });

  if (!reminder) {
    return next(new ErrorResponse(`Reminder not found with id of ${req.params.id}`, 404));
  }

  // Make sure reminder belongs to user
  if (reminder.user.toString() !== req.user.id) {
    return next(new ErrorResponse(`User not authorized to access this reminder`, 401));
  }

  res.status(200).json({
    success: true,
    data: reminder
  });
});

/**
 * @desc    Create a new reminder
 * @route   POST /api/users/me/reminders
 * @access  Private
 */
exports.createReminder = asyncHandler(async (req, res, next) => {
  // Add user to body
  req.body.user = req.user.id;

  // Check if stream exists
  const stream = await LiveStream.findById(req.body.stream);
  if (!stream) {
    return next(new ErrorResponse(`Stream not found with id of ${req.body.stream}`, 404));
  }

  // Check if stream is scheduled
  if (stream.status !== 'scheduled') {
    return next(new ErrorResponse(`Cannot set reminder for a stream that is not scheduled`, 400));
  }

  // Calculate default reminder time (15 minutes before stream starts)
  if (!req.body.reminderTime) {
    req.body.reminderTime = subMinutes(new Date(stream.scheduledFor), 15);
  }

  // Check if reminder already exists
  const existingReminder = await StreamReminder.findOne({
    user: req.user.id,
    stream: req.body.stream
  });

  if (existingReminder) {
    return next(new ErrorResponse(`You already have a reminder for this stream`, 400));
  }

  // Create reminder
  const reminder = await StreamReminder.create(req.body);

  // Populate stream data
  await reminder.populate({
    path: 'stream',
    select: 'title thumbnail scheduledFor duration user status',
    populate: {
      path: 'user',
      select: 'name username profilePicture'
    }
  });

  res.status(201).json({
    success: true,
    data: reminder
  });
});

/**
 * @desc    Update reminder
 * @route   PUT /api/users/me/reminders/:id
 * @access  Private
 */
exports.updateReminder = asyncHandler(async (req, res, next) => {
  let reminder = await StreamReminder.findById(req.params.id);

  if (!reminder) {
    return next(new ErrorResponse(`Reminder not found with id of ${req.params.id}`, 404));
  }

  // Make sure reminder belongs to user
  if (reminder.user.toString() !== req.user.id) {
    return next(new ErrorResponse(`User not authorized to update this reminder`, 401));
  }

  // Update reminder
  reminder = await StreamReminder.findByIdAndUpdate(req.params.id, req.body, {
    new: true,
    runValidators: true
  });

  // Populate stream data
  await reminder.populate({
    path: 'stream',
    select: 'title thumbnail scheduledFor duration user status',
    populate: {
      path: 'user',
      select: 'name username profilePicture'
    }
  });

  res.status(200).json({
    success: true,
    data: reminder
  });
});

/**
 * @desc    Delete reminder
 * @route   DELETE /api/users/me/reminders/:id
 * @access  Private
 */
exports.deleteReminder = asyncHandler(async (req, res, next) => {
  const reminder = await StreamReminder.findById(req.params.id);

  if (!reminder) {
    return next(new ErrorResponse(`Reminder not found with id of ${req.params.id}`, 404));
  }

  // Make sure reminder belongs to user
  if (reminder.user.toString() !== req.user.id) {
    return next(new ErrorResponse(`User not authorized to delete this reminder`, 401));
  }

  await reminder.remove();

  res.status(200).json({
    success: true,
    data: {}
  });
});

/**
 * @desc    Get RSVP status for a stream
 * @route   GET /api/live-streams/:streamId/rsvp
 * @access  Private
 */
exports.getStreamRSVP = asyncHandler(async (req, res, next) => {
  const reminder = await StreamReminder.findOne({
    user: req.user.id,
    stream: req.params.streamId
  });

  res.status(200).json({
    success: true,
    data: reminder ? {
      status: reminder.rsvpStatus,
      reminderTime: reminder.reminderTime,
      addedToCalendar: reminder.addedToCalendar
    } : null
  });
});

/**
 * @desc    Set RSVP status for a stream
 * @route   POST /api/live-streams/:streamId/rsvp
 * @access  Private
 */
exports.setStreamRSVP = asyncHandler(async (req, res, next) => {
  const { status, reminderTime, addToCalendar } = req.body;

  // Check if stream exists
  const stream = await LiveStream.findById(req.params.streamId);
  if (!stream) {
    return next(new ErrorResponse(`Stream not found with id of ${req.params.streamId}`, 404));
  }

  // Check if stream is scheduled
  if (stream.status !== 'scheduled') {
    return next(new ErrorResponse(`Cannot RSVP to a stream that is not scheduled`, 400));
  }

  // Calculate default reminder time (15 minutes before stream starts)
  const defaultReminderTime = subMinutes(new Date(stream.scheduledFor), 15);

  // Find existing reminder or create new one
  let reminder = await StreamReminder.findOne({
    user: req.user.id,
    stream: req.params.streamId
  });

  if (reminder) {
    // Update existing reminder
    reminder.rsvpStatus = status || reminder.rsvpStatus;
    reminder.reminderTime = reminderTime || reminder.reminderTime;
    reminder.addedToCalendar = addToCalendar !== undefined ? addToCalendar : reminder.addedToCalendar;
    await reminder.save();
  } else {
    // Create new reminder
    reminder = await StreamReminder.create({
      user: req.user.id,
      stream: req.params.streamId,
      rsvpStatus: status || 'going',
      reminderTime: reminderTime || defaultReminderTime,
      addedToCalendar: addToCalendar || false
    });
  }

  // Populate stream data
  await reminder.populate({
    path: 'stream',
    select: 'title thumbnail scheduledFor duration user status',
    populate: {
      path: 'user',
      select: 'name username profilePicture'
    }
  });

  // Emit socket event for RSVP update
  socketEmitter.emitStreamRSVPUpdate(req.params.streamId, {
    userId: req.user.id,
    status: reminder.rsvpStatus,
    stream: {
      _id: stream._id,
      title: stream.title
    }
  });

  res.status(200).json({
    success: true,
    data: reminder
  });
});
