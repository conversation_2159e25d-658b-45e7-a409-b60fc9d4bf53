const express = require('express');
const router = express.Router();
const { protect } = require('../middleware/auth');
const { upload, processUploadedFiles } = require('../middleware/fileUpload');
const {
  getMessages,
  sendMessage,
  markMessagesAsRead,
  deleteMessage,
  addReaction,
  removeReaction,
  searchMessages,
  pinMessage,
  unpinMessage,
  votePoll,
  removeVote,
  processCommand,
  cancelScheduledMessage,
} = require('../controllers/messageController');

// All routes require authentication
router.use(protect);

// Get messages for a conversation
router.get('/:conversationId', getMessages);

// Send a new message
router.post('/:conversationId', upload.array('media', 10), processUploadedFiles, sendMessage);

// Mark messages as read
router.put('/:conversationId/read', markMessagesAsRead);

// Delete a message
router.delete('/:messageId', deleteMessage);

// Add reaction to a message
router.post('/:messageId/reactions', addReaction);

// Remove reaction from a message
router.delete('/:messageId/reactions', removeReaction);

// Search messages
router.get('/:conversationId/search', searchMessages);

// Pin a message
router.post('/:messageId/pin', pinMessage);

// Unpin a message
router.delete('/:messageId/pin', unpinMessage);

// Poll voting
router.post('/:messageId/poll/:optionId/vote', votePoll);
router.delete('/:messageId/poll/:optionId/vote', removeVote);

// Process command
router.post('/:messageId/command/process', processCommand);

// Cancel scheduled message
router.delete('/:messageId/schedule', cancelScheduledMessage);

module.exports = router;
