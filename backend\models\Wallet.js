const mongoose = require('mongoose');

const WalletSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    unique: true
  },
  address: {
    type: String,
    default: null
  },
  blockchain: {
    type: String,
    enum: ['ethereum', 'polygon', 'solana', 'binance', 'flow', 'other', null],
    default: null
  },
  isConnected: {
    type: Boolean,
    default: false
  },
  balance: {
    type: Number,
    default: 0
  },
  currency: {
    type: String,
    enum: ['usd', 'eth', 'matic', 'sol', 'bnb', 'flow', 'coins'],
    default: 'usd'
  },
  lastConnected: {
    type: Date,
    default: null
  },
  lastDisconnected: {
    type: Date,
    default: null
  },
  createdAt: {
    type: Date,
    default: Date.now
  }
});

module.exports = mongoose.model('Wallet', WalletSchema);
