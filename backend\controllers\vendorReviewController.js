const VendorReview = require('../models/VendorReview');
const Vendor = require('../models/Vendor');
const Order = require('../models/Order');
const Product = require('../models/Product');

// @desc    Create vendor review
// @route   POST /api/vendor-reviews
// @access  Private
exports.createVendorReview = async (req, res) => {
  try {
    const {
      vendorId,
      orderId,
      rating,
      title,
      comment,
      aspects
    } = req.body;

    // Check if order exists and belongs to user
    const order = await Order.findOne({
      _id: orderId,
      user: req.user.id
    });

    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Order not found or not authorized'
      });
    }

    // Check if vendor exists
    const vendor = await Vendor.findById(vendorId);
    if (!vendor) {
      return res.status(404).json({
        success: false,
        message: 'Vendor not found'
      });
    }

    // Check if user has already reviewed this vendor for this order
    const existingReview = await VendorReview.findOne({
      user: req.user.id,
      order: orderId,
      vendor: vendorId
    });

    if (existingReview) {
      return res.status(400).json({
        success: false,
        message: 'You have already reviewed this vendor for this order'
      });
    }

    // Verify that the order contains products from this vendor
    const vendorProducts = await Product.find({ user: vendor.user }).distinct('_id');
    const hasVendorProducts = order.items.some(item =>
      vendorProducts.some(productId => productId.toString() === item.product.toString())
    );

    if (!hasVendorProducts) {
      return res.status(400).json({
        success: false,
        message: 'This order does not contain products from this vendor'
      });
    }

    const review = new VendorReview({
      vendor: vendorId,
      user: req.user.id,
      order: orderId,
      rating,
      title,
      comment,
      aspects,
      isVerifiedPurchase: true
    });

    await review.save();

    // Populate the review for response
    await review.populate('user', 'name');
    await review.populate('order', 'orderNumber');

    res.status(201).json({
      success: true,
      data: review,
      message: 'Review created successfully'
    });
  } catch (error) {
    console.error('Error creating vendor review:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Get vendor reviews
// @route   GET /api/vendor-reviews/vendor/:vendorId
// @access  Public
exports.getVendorReviews = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      rating,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    const vendor = await Vendor.findById(req.params.vendorId);
    if (!vendor) {
      return res.status(404).json({
        success: false,
        message: 'Vendor not found'
      });
    }

    let query = {
      vendor: req.params.vendorId,
      status: 'active'
    };

    if (rating) {
      query.rating = parseInt(rating);
    }

    const sortOptions = {};
    sortOptions[sortBy] = sortOrder === 'desc' ? -1 : 1;

    const reviews = await VendorReview.find(query)
      .populate('user', 'name')
      .populate('order', 'orderNumber createdAt')
      .sort(sortOptions)
      .limit(limit * 1)
      .skip((page - 1) * limit);

    const total = await VendorReview.countDocuments(query);

    // Get review statistics
    const stats = await VendorReview.aggregate([
      {
        $match: { vendor: vendor._id, status: 'active' }
      },
      {
        $group: {
          _id: null,
          averageRating: { $avg: '$rating' },
          totalReviews: { $sum: 1 },
          ratingDistribution: {
            $push: '$rating'
          },
          averageAspects: {
            $avg: {
              communication: '$aspects.communication',
              shipping: '$aspects.shipping',
              productQuality: '$aspects.productQuality',
              customerService: '$aspects.customerService'
            }
          }
        }
      }
    ]);

    // Calculate rating distribution
    let distribution = { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 };
    if (stats.length > 0) {
      stats[0].ratingDistribution.forEach(rating => {
        distribution[rating] = (distribution[rating] || 0) + 1;
      });
    }

    res.status(200).json({
      success: true,
      data: {
        reviews,
        stats: stats[0] || {
          averageRating: 0,
          totalReviews: 0,
          averageAspects: {
            communication: 0,
            shipping: 0,
            productQuality: 0,
            customerService: 0
          }
        },
        distribution,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    console.error('Error getting vendor reviews:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Get user's reviews
// @route   GET /api/vendor-reviews/my-reviews
// @access  Private
exports.getUserReviews = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10
    } = req.query;

    const reviews = await VendorReview.find({ user: req.user.id })
      .populate('vendor', 'businessName storeSettings.storeName logo')
      .populate('order', 'orderNumber createdAt')
      .sort({ createdAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit);

    const total = await VendorReview.countDocuments({ user: req.user.id });

    res.status(200).json({
      success: true,
      data: reviews,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error getting user reviews:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Update vendor review
// @route   PUT /api/vendor-reviews/:id
// @access  Private
exports.updateVendorReview = async (req, res) => {
  try {
    const review = await VendorReview.findById(req.params.id);

    if (!review) {
      return res.status(404).json({
        success: false,
        message: 'Review not found'
      });
    }

    // Check if user owns the review
    if (review.user.toString() !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: 'Not authorized to update this review'
      });
    }

    const allowedFields = ['rating', 'title', 'comment', 'aspects'];
    allowedFields.forEach(field => {
      if (req.body[field] !== undefined) {
        review[field] = req.body[field];
      }
    });

    await review.save();

    await review.populate('user', 'name');
    await review.populate('order', 'orderNumber');

    res.status(200).json({
      success: true,
      data: review,
      message: 'Review updated successfully'
    });
  } catch (error) {
    console.error('Error updating vendor review:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Delete vendor review
// @route   DELETE /api/vendor-reviews/:id
// @access  Private
exports.deleteVendorReview = async (req, res) => {
  try {
    const review = await VendorReview.findById(req.params.id);

    if (!review) {
      return res.status(404).json({
        success: false,
        message: 'Review not found'
      });
    }

    // Check if user owns the review or is admin
    if (review.user.toString() !== req.user.id && req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Not authorized to delete this review'
      });
    }

    await review.deleteOne();

    res.status(200).json({
      success: true,
      data: {},
      message: 'Review deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting vendor review:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Mark review as helpful
// @route   POST /api/vendor-reviews/:id/helpful
// @access  Private
exports.markReviewHelpful = async (req, res) => {
  try {
    const review = await VendorReview.findById(req.params.id);

    if (!review) {
      return res.status(404).json({
        success: false,
        message: 'Review not found'
      });
    }

    await review.markHelpful();

    res.status(200).json({
      success: true,
      data: review,
      message: 'Review marked as helpful'
    });
  } catch (error) {
    console.error('Error marking review as helpful:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Report review
// @route   POST /api/vendor-reviews/:id/report
// @access  Private
exports.reportReview = async (req, res) => {
  try {
    const review = await VendorReview.findById(req.params.id);

    if (!review) {
      return res.status(404).json({
        success: false,
        message: 'Review not found'
      });
    }

    await review.reportReview();

    res.status(200).json({
      success: true,
      data: review,
      message: 'Review reported successfully'
    });
  } catch (error) {
    console.error('Error reporting review:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

module.exports = {
  createVendorReview: exports.createVendorReview,
  getVendorReviews: exports.getVendorReviews,
  getUserReviews: exports.getUserReviews,
  updateVendorReview: exports.updateVendorReview,
  deleteVendorReview: exports.deleteVendorReview,
  markReviewHelpful: exports.markReviewHelpful,
  reportReview: exports.reportReview
};
