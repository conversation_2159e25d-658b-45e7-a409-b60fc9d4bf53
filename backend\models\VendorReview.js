const mongoose = require('mongoose');

const VendorReviewSchema = new mongoose.Schema({
  vendor: {
    type: mongoose.Schema.ObjectId,
    ref: 'Vendor',
    required: true
  },
  user: {
    type: mongoose.Schema.ObjectId,
    ref: 'User',
    required: true
  },
  order: {
    type: mongoose.Schema.ObjectId,
    ref: 'Order',
    required: true
  },
  rating: {
    type: Number,
    min: 1,
    max: 5,
    required: [true, 'Please add a rating between 1 and 5']
  },
  title: {
    type: String,
    trim: true,
    maxlength: [100, 'Title cannot be more than 100 characters']
  },
  comment: {
    type: String,
    required: [true, 'Please add a comment'],
    maxlength: [500, 'Comment cannot be more than 500 characters']
  },
  aspects: {
    communication: {
      type: Number,
      min: 1,
      max: 5
    },
    shipping: {
      type: Number,
      min: 1,
      max: 5
    },
    productQuality: {
      type: Number,
      min: 1,
      max: 5
    },
    customerService: {
      type: Number,
      min: 1,
      max: 5
    }
  },
  images: [String],
  isVerifiedPurchase: {
    type: Boolean,
    default: true
  },
  helpfulVotes: {
    type: Number,
    default: 0
  },
  reportedCount: {
    type: Number,
    default: 0
  },
  status: {
    type: String,
    enum: ['active', 'hidden', 'reported', 'removed'],
    default: 'active'
  },
  vendorResponse: {
    message: String,
    respondedAt: Date,
    respondedBy: {
      type: mongoose.Schema.ObjectId,
      ref: 'User'
    }
  },
  createdAt: {
    type: Date,
    default: Date.now
  }
});

// Prevent user from submitting more than one review per order
VendorReviewSchema.index({ user: 1, order: 1 }, { unique: true });

// Create indexes for better performance
VendorReviewSchema.index({ vendor: 1, createdAt: -1 });
VendorReviewSchema.index({ vendor: 1, rating: -1 });
VendorReviewSchema.index({ user: 1 });
VendorReviewSchema.index({ status: 1 });

// Static method to get average rating and calculate vendor analytics
VendorReviewSchema.statics.getAverageRating = async function(vendorId) {
  const obj = await this.aggregate([
    {
      $match: { vendor: vendorId, status: 'active' }
    },
    {
      $group: {
        _id: '$vendor',
        averageRating: { $avg: '$rating' },
        totalReviews: { $sum: 1 },
        ratingDistribution: {
          $push: '$rating'
        },
        aspectRatings: {
          $avg: {
            communication: '$aspects.communication',
            shipping: '$aspects.shipping',
            productQuality: '$aspects.productQuality',
            customerService: '$aspects.customerService'
          }
        }
      }
    }
  ]);

  if (obj.length > 0) {
    // Calculate rating distribution
    const distribution = { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 };
    obj[0].ratingDistribution.forEach(rating => {
      distribution[rating] = (distribution[rating] || 0) + 1;
    });

    try {
      await this.model('Vendor').findByIdAndUpdate(vendorId, {
        'analytics.averageRating': Math.round(obj[0].averageRating * 10) / 10,
        'analytics.totalReviews': obj[0].totalReviews,
        'analytics.ratingDistribution': distribution,
        'analytics.aspectRatings': obj[0].aspectRatings
      });
    } catch (err) {
      console.error('Error updating vendor rating:', err);
    }
  }
};

// Call getAverageRating after save
VendorReviewSchema.post('save', function() {
  this.constructor.getAverageRating(this.vendor);
});

// Call getAverageRating before remove
VendorReviewSchema.pre('remove', function() {
  this.constructor.getAverageRating(this.vendor);
});

// Method to mark review as helpful
VendorReviewSchema.methods.markHelpful = function() {
  this.helpfulVotes += 1;
  return this.save();
};

// Method to report review
VendorReviewSchema.methods.reportReview = function() {
  this.reportedCount += 1;
  if (this.reportedCount >= 5) {
    this.status = 'reported';
  }
  return this.save();
};

// Method to add vendor response
VendorReviewSchema.methods.addVendorResponse = function(message, respondedBy) {
  this.vendorResponse = {
    message,
    respondedAt: new Date(),
    respondedBy
  };
  return this.save();
};

module.exports = mongoose.model('VendorReview', VendorReviewSchema);
