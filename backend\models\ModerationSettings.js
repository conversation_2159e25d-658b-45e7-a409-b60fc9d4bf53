const mongoose = require('mongoose');

const ModerationSettingsSchema = new mongoose.Schema({
  // Owner of these settings (user or stream)
  owner: {
    type: mongoose.Schema.Types.ObjectId,
    required: true,
    refPath: 'ownerType'
  },
  // Type of owner (User or LiveStream)
  ownerType: {
    type: String,
    enum: ['User', 'LiveStream'],
    required: true
  },
  // Chat moderation settings
  chat: {
    // Enable/disable chat
    enabled: {
      type: Boolean,
      default: true
    },
    // Followers-only mode
    followersOnly: {
      type: <PERSON>olean,
      default: false
    },
    // Minimum account age to chat (in hours)
    minimumAccountAge: {
      type: Number,
      default: 0
    },
    // Slow mode
    slowMode: {
      type: Boolean,
      default: false
    },
    // Slow mode interval (in seconds)
    slowModeInterval: {
      type: Number,
      default: 5,
      min: 1,
      max: 120
    },
    // Chat delay (in seconds)
    chatDelay: {
      type: Number,
      default: 0,
      min: 0,
      max: 30
    },
    // Emote-only mode
    emoteOnly: {
      type: <PERSON><PERSON><PERSON>,
      default: false
    },
    // Subscriber-only mode (for monetized streams)
    subscriberOnly: {
      type: Boolean,
      default: false
    },
    // Auto-moderation level
    autoModeration: {
      type: String,
      enum: ['off', 'low', 'medium', 'high'],
      default: 'medium'
    },
    // AI co-pilot for chat moderation
    aiCoPilot: {
      type: Boolean,
      default: false
    }
  },
  // Content filtering settings
  contentFiltering: {
    // Enable keyword filtering
    keywordFilter: {
      type: Boolean,
      default: true
    },
    // Enable AI content moderation
    aiModeration: {
      type: Boolean,
      default: true
    },
    // AI moderation threshold (0-1)
    aiModerationThreshold: {
      type: Number,
      min: 0,
      max: 1,
      default: 0.7
    },
    // Auto-hide flagged content
    autoHideFlagged: {
      type: Boolean,
      default: true
    },
    // Sentiment analysis for toxic comments
    sentimentAnalysis: {
      type: Boolean,
      default: true
    },
    // Minimum sentiment score (-1 to 1, where -1 is very negative)
    minimumSentimentScore: {
      type: Number,
      min: -1,
      max: 1,
      default: -0.7
    }
  },
  // User safety settings
  userSafety: {
    // Enable age verification for mature content
    ageVerification: {
      type: Boolean,
      default: false
    },
    // Minimum age required (if age verification is enabled)
    minimumAge: {
      type: Number,
      min: 13,
      max: 21,
      default: 18
    },
    // Restricted mode (family-friendly content only)
    restrictedMode: {
      type: Boolean,
      default: false
    },
    // Auto-mute new viewers
    autoMuteNewViewers: {
      type: Boolean,
      default: false
    },
    // Require approval for new chat participants
    requireApproval: {
      type: Boolean,
      default: false
    }
  },
  // Moderator roles and permissions
  moderators: [{
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    permissions: {
      canTimeout: {
        type: Boolean,
        default: true
      },
      canBan: {
        type: Boolean,
        default: true
      },
      canDeleteMessages: {
        type: Boolean,
        default: true
      },
      canModifySettings: {
        type: Boolean,
        default: false
      }
    },
    addedAt: {
      type: Date,
      default: Date.now
    },
    addedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    }
  }]
}, {
  timestamps: true
});

// Create indexes for efficient queries
ModerationSettingsSchema.index({ owner: 1, ownerType: 1 }, { unique: true });
ModerationSettingsSchema.index({ 'moderators.user': 1 });

module.exports = mongoose.model('ModerationSettings', ModerationSettingsSchema);
