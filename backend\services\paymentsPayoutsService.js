const Commission = require('../models/Commission');
const PayoutRequest = require('../models/PayoutRequest');
const Currency = require('../models/Currency');
const Order = require('../models/Order');
const Vendor = require('../models/Vendor');
const paymentGatewayService = require('./paymentGatewayService');

class PaymentsPayoutsService {
  constructor() {
    this.defaultCommissionRate = 5; // 5% platform commission
    this.minimumPayoutAmount = 50;
    this.payoutSchedule = 'weekly'; // weekly, bi-weekly, monthly
  }

  // Calculate commission for an order
  async calculateCommission(orderId, customRate = null) {
    try {
      const order = await Order.findById(orderId).populate('vendor user');
      if (!order) {
        throw new Error('Order not found');
      }

      const vendor = await Vendor.findOne({ user: order.vendor });
      if (!vendor) {
        throw new Error('Vendor not found');
      }

      // Determine commission rate (can be vendor-specific or default)
      const commissionRate = customRate || vendor.commissionRate || this.defaultCommissionRate;

      // Get currency and tax configuration
      const currency = await Currency.findOne({ code: order.currency || 'USD' });
      const taxConfig = currency?.taxConfiguration || {};

      // Create commission record
      const commission = new Commission({
        order: orderId,
        vendor: vendor._id,
        buyer: order.user,
        product: order.items[0]?.product, // Assuming single product for now
        commissionRate: {
          percentage: commissionRate,
          type: 'percentage'
        },
        currency: order.currency || 'USD',
        paymentMethod: order.paymentMethod || 'stripe'
      });

      // Calculate amounts
      commission.calculateCommission(order.total, commissionRate, taxConfig);

      await commission.save();
      await commission.addTimelineEntry('calculated', 'Commission calculated', null);

      return commission;
    } catch (error) {
      console.error('Error calculating commission:', error);
      throw error;
    }
  }

  // Process commission after successful payment
  async processCommission(orderId) {
    try {
      const commission = await Commission.findOne({ order: orderId });
      if (!commission) {
        throw new Error('Commission not found for order');
      }

      // Update commission status
      await commission.updateStatus('processed', 'Commission processed after payment confirmation');

      // Check if vendor is eligible for payout
      const vendor = await Vendor.findById(commission.vendor);
      if (vendor && this.isVendorEligibleForPayout(vendor)) {
        await commission.markEligibleForPayout();
      }

      return commission;
    } catch (error) {
      console.error('Error processing commission:', error);
      throw error;
    }
  }

  // Get commission analytics
  async getCommissionAnalytics(vendorId, startDate, endDate, currency = 'USD') {
    try {
      const matchStage = {
        vendor: vendorId,
        currency: currency,
        createdAt: {
          $gte: new Date(startDate),
          $lte: new Date(endDate)
        }
      };

      const analytics = await Commission.aggregate([
        { $match: matchStage },
        {
          $group: {
            _id: null,
            totalGrossAmount: { $sum: '$amounts.grossAmount' },
            totalPlatformCommission: { $sum: '$amounts.platformCommission' },
            totalVendorEarnings: { $sum: '$amounts.vendorEarnings' },
            totalTaxes: { $sum: '$amounts.taxes' },
            totalTransactions: { $sum: 1 },
            averageOrderValue: { $avg: '$amounts.grossAmount' },
            averageCommission: { $avg: '$amounts.platformCommission' }
          }
        }
      ]);

      const statusBreakdown = await Commission.aggregate([
        { $match: matchStage },
        {
          $group: {
            _id: '$status',
            count: { $sum: 1 },
            totalAmount: { $sum: '$amounts.grossAmount' }
          }
        }
      ]);

      const payoutStatusBreakdown = await Commission.aggregate([
        { $match: matchStage },
        {
          $group: {
            _id: '$payoutStatus',
            count: { $sum: 1 },
            totalEarnings: { $sum: '$amounts.vendorEarnings' }
          }
        }
      ]);

      return {
        summary: analytics[0] || {},
        statusBreakdown,
        payoutStatusBreakdown
      };
    } catch (error) {
      console.error('Error getting commission analytics:', error);
      throw error;
    }
  }

  // Create payout request
  async createPayoutRequest(vendorId, amount, payoutMethod, currency = 'USD') {
    try {
      const vendor = await Vendor.findById(vendorId).populate('user');
      if (!vendor) {
        throw new Error('Vendor not found');
      }

      // Get available balance
      const availableBalance = await this.getVendorBalance(vendorId, currency);

      if (amount > availableBalance) {
        throw new Error('Insufficient balance for payout request');
      }

      // Get eligible commissions
      const eligibleCommissions = await Commission.find({
        vendor: vendorId,
        payoutStatus: 'pending',
        currency: currency
      });

      // Create payout request
      const payoutRequest = new PayoutRequest({
        vendor: vendorId,
        user: vendor.user._id,
        type: 'manual',
        amounts: {
          requestedAmount: amount,
          availableBalance: availableBalance
        },
        currency: currency,
        payoutMethod: payoutMethod,
        commissions: eligibleCommissions.map(c => c._id)
      });

      // Calculate processing fees
      const currencyConfig = await Currency.findOne({ code: currency });
      if (currencyConfig) {
        const processingFee = currencyConfig.getProcessingFee(payoutMethod.type, amount);
        payoutRequest.amounts.processingFee = processingFee;
      }

      payoutRequest.calculateNetAmount();
      await payoutRequest.save();

      // Update commission statuses
      await Commission.updateMany(
        { _id: { $in: eligibleCommissions.map(c => c._id) } },
        { payoutStatus: 'processing', payoutRequest: payoutRequest._id }
      );

      await payoutRequest.addTimelineEntry(
        'created',
        'Payout request created',
        vendor.user._id
      );

      return payoutRequest;
    } catch (error) {
      console.error('Error creating payout request:', error);
      throw error;
    }
  }

  // Process payout request
  async processPayoutRequest(payoutRequestId, adminUserId) {
    try {
      const payoutRequest = await PayoutRequest.findById(payoutRequestId)
        .populate('vendor user commissions');

      if (!payoutRequest) {
        throw new Error('Payout request not found');
      }

      if (!payoutRequest.isEligibleForPayout()) {
        throw new Error('Payout request is not eligible for processing');
      }

      // Start processing
      await payoutRequest.updateStatus('approved', 'Payout approved for processing', adminUserId);

      // Process based on payout method
      let result;
      switch (payoutRequest.payoutMethod.type) {
        case 'paypal':
          result = await this.processPayPalPayout(payoutRequest);
          break;
        case 'bank_transfer':
          result = await this.processBankTransferPayout(payoutRequest);
          break;
        case 'mobile_money':
          result = await this.processMobileMoneyPayout(payoutRequest);
          break;
        case 'crypto':
          result = await this.processCryptoPayout(payoutRequest);
          break;
        default:
          throw new Error(`Unsupported payout method: ${payoutRequest.payoutMethod.type}`);
      }

      if (result.success) {
        await payoutRequest.startProcessing(adminUserId, result.transactionId);

        // Update commissions
        await Commission.updateMany(
          { _id: { $in: payoutRequest.commissions } },
          { payoutStatus: 'completed' }
        );

        // Mark as completed if instant
        if (result.instant) {
          await payoutRequest.markCompleted(new Date(), result.response);
        }
      } else {
        await payoutRequest.markFailed(result.error, result.code, result.details);
      }

      return result;
    } catch (error) {
      console.error('Error processing payout request:', error);

      // Mark as failed
      const payoutRequest = await PayoutRequest.findById(payoutRequestId);
      if (payoutRequest) {
        await payoutRequest.markFailed(error.message, 'PROCESSING_ERROR', error.stack);
      }

      throw error;
    }
  }

  // Process PayPal payout
  async processPayPalPayout(payoutRequest) {
    try {
      // Simulate PayPal API call
      const paypalResponse = {
        payout_batch_id: `PAYOUT_${Date.now()}`,
        batch_status: 'SUCCESS',
        items: [{
          payout_item_id: `ITEM_${Date.now()}`,
          transaction_status: 'SUCCESS'
        }]
      };

      return {
        success: true,
        transactionId: paypalResponse.payout_batch_id,
        instant: true,
        response: paypalResponse
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        code: 'PAYPAL_ERROR',
        details: error.stack
      };
    }
  }

  // Process bank transfer payout
  async processBankTransferPayout(payoutRequest) {
    try {
      // Simulate bank transfer API call
      const transferResponse = {
        transfer_id: `TRANSFER_${Date.now()}`,
        status: 'PENDING',
        estimated_arrival: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000) // 3 days
      };

      return {
        success: true,
        transactionId: transferResponse.transfer_id,
        instant: false,
        estimatedArrival: transferResponse.estimated_arrival,
        response: transferResponse
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        code: 'BANK_TRANSFER_ERROR',
        details: error.stack
      };
    }
  }

  // Process mobile money payout
  async processMobileMoneyPayout(payoutRequest) {
    try {
      // Simulate mobile money API call
      const mobileMoneyResponse = {
        transaction_id: `MM_${Date.now()}`,
        status: 'SUCCESS',
        provider: payoutRequest.payoutMethod.details.mobileProvider
      };

      return {
        success: true,
        transactionId: mobileMoneyResponse.transaction_id,
        instant: true,
        response: mobileMoneyResponse
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        code: 'MOBILE_MONEY_ERROR',
        details: error.stack
      };
    }
  }

  // Process crypto payout
  async processCryptoPayout(payoutRequest) {
    try {
      // Simulate crypto transfer
      const cryptoResponse = {
        transaction_hash: `0x${Math.random().toString(16).substr(2, 64)}`,
        status: 'PENDING',
        network: payoutRequest.payoutMethod.details.network
      };

      return {
        success: true,
        transactionId: cryptoResponse.transaction_hash,
        instant: false,
        response: cryptoResponse
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        code: 'CRYPTO_ERROR',
        details: error.stack
      };
    }
  }

  // Get vendor balance
  async getVendorBalance(vendorId, currency = 'USD') {
    try {
      const result = await Commission.aggregate([
        {
          $match: {
            vendor: vendorId,
            payoutStatus: 'pending',
            currency: currency
          }
        },
        {
          $group: {
            _id: null,
            totalBalance: { $sum: '$amounts.vendorEarnings' },
            totalCommissions: { $sum: 1 }
          }
        }
      ]);

      return result.length > 0 ? result[0].totalBalance : 0;
    } catch (error) {
      console.error('Error getting vendor balance:', error);
      return 0;
    }
  }

  // Get vendor payout history
  async getVendorPayoutHistory(vendorId, page = 1, limit = 20) {
    try {
      const skip = (page - 1) * limit;

      const payouts = await PayoutRequest.find({ vendor: vendorId })
        .populate('commissions')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit);

      const total = await PayoutRequest.countDocuments({ vendor: vendorId });

      return {
        payouts,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      console.error('Error getting payout history:', error);
      throw error;
    }
  }

  // Check if vendor is eligible for payout
  isVendorEligibleForPayout(vendor) {
    return vendor.kycStatus === 'verified' &&
           vendor.status === 'active' &&
           vendor.payoutMethod &&
           vendor.payoutMethod.verified;
  }

  // Setup automatic payouts
  async setupAutomaticPayouts(vendorId, schedule, minimumAmount) {
    try {
      const vendor = await Vendor.findById(vendorId);
      if (!vendor) {
        throw new Error('Vendor not found');
      }

      vendor.payoutSettings = {
        automatic: true,
        schedule: schedule,
        minimumAmount: minimumAmount,
        nextPayoutDate: this.calculateNextPayoutDate(schedule)
      };

      await vendor.save();
      return vendor.payoutSettings;
    } catch (error) {
      console.error('Error setting up automatic payouts:', error);
      throw error;
    }
  }

  // Calculate next payout date
  calculateNextPayoutDate(schedule) {
    const now = new Date();
    const nextDate = new Date(now);

    switch (schedule) {
      case 'daily':
        nextDate.setDate(now.getDate() + 1);
        break;
      case 'weekly':
        nextDate.setDate(now.getDate() + 7);
        break;
      case 'bi_weekly':
        nextDate.setDate(now.getDate() + 14);
        break;
      case 'monthly':
        nextDate.setMonth(now.getMonth() + 1);
        break;
    }

    return nextDate;
  }

  // Process automatic payouts (to be run by cron job)
  async processAutomaticPayouts() {
    try {
      const vendors = await Vendor.find({
        'payoutSettings.automatic': true,
        'payoutSettings.nextPayoutDate': { $lte: new Date() },
        status: 'active'
      });

      const results = [];

      for (const vendor of vendors) {
        try {
          const balance = await this.getVendorBalance(vendor._id);

          if (balance >= vendor.payoutSettings.minimumAmount) {
            const payoutRequest = await this.createPayoutRequest(
              vendor._id,
              balance,
              vendor.payoutMethod,
              vendor.defaultCurrency || 'USD'
            );

            // Auto-approve and process
            await this.processPayoutRequest(payoutRequest._id, null);

            results.push({
              vendorId: vendor._id,
              success: true,
              amount: balance,
              payoutRequestId: payoutRequest._id
            });
          }

          // Update next payout date
          vendor.payoutSettings.nextPayoutDate = this.calculateNextPayoutDate(
            vendor.payoutSettings.schedule
          );
          await vendor.save();

        } catch (error) {
          results.push({
            vendorId: vendor._id,
            success: false,
            error: error.message
          });
        }
      }

      return results;
    } catch (error) {
      console.error('Error processing automatic payouts:', error);
      throw error;
    }
  }
}

module.exports = new PaymentsPayoutsService();
