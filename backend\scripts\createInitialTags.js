const mongoose = require('mongoose');
const dotenv = require('dotenv');
const Tag = require('../models/Tag');
const Post = require('../models/Post');
const Reel = require('../models/Reel');

// Load environment variables
dotenv.config();

// Connect to MongoDB
mongoose.connect(process.env.MONGO_URI || 'mongodb://localhost:27017/letstalk', {
  useNewUrlParser: true,
  useUnifiedTopology: true,
});

const createInitialTags = async () => {
  try {
    // Clear existing tags
    await Tag.deleteMany({});
    console.log('Existing tags cleared');

    // Create initial tags
    const initialTags = [
      { name: 'happy', count: 120 },
      { name: 'sad', count: 85 },
      { name: 'excited', count: 95 },
      { name: 'calm', count: 70 },
      { name: 'anxious', count: 65 },
      { name: 'love', count: 150 },
      { name: 'nature', count: 110 },
      { name: 'travel', count: 100 },
      { name: 'food', count: 130 },
      { name: 'fitness', count: 90 },
      { name: 'music', count: 105 },
      { name: 'art', count: 80 },
      { name: 'photography', count: 75 },
      { name: 'fashion', count: 85 },
      { name: 'technology', count: 70 },
    ];

    const createdTags = await Tag.insertMany(initialTags);
    console.log(`${createdTags.length} initial tags created`);

    // Find posts and reels with these tags and update the tag references
    for (const tag of createdTags) {
      const posts = await Post.find({ tags: tag.name });
      const reels = await Reel.find({ tags: tag.name });

      tag.posts = posts.map(post => post._id);
      tag.reels = reels.map(reel => reel._id);
      
      await tag.save();
      console.log(`Updated tag ${tag.name} with ${tag.posts.length} posts and ${tag.reels.length} reels`);
    }

    console.log('Initial tags created and linked to content successfully');
    process.exit(0);
  } catch (error) {
    console.error('Error creating initial tags:', error);
    process.exit(1);
  }
};

createInitialTags();
